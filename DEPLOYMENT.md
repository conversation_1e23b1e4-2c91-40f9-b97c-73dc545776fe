# 🚀 CRM Deployment Guide

## 📋 Quick Start

### Development Environment

```bash
# First time setup
make init

# Start development
make dev

# Stop everything
make stop
```

### Production Deployment

```bash
# Quick deploy with script
./deploy.sh

# Or manually:
cp .env.example .env
# Edit .env with production values
docker-compose -f docker-compose.production.yml up -d
```

## 🐳 Docker Structure

### Services Architecture

```
┌─────────────────────────────────────────────────────────┐
│                     Nginx (Port 80/443)                  │
├──────────────────────┬──────────────────────────────────┤
│   Frontend (Next.js) │     Backend API (Go)             │
│      Port 3000       │        Port 8080                 │
└──────────────────────┴──────────────────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
   PostgreSQL            Redis Cache           MinIO Storage
   Port 5432            Port 6379             Port 9000/9001
```

### Container Images

| Service | Base Image | Size | Multi-stage |
|---------|------------|------|-------------|
| Backend | alpine:latest | ~50MB | ✅ Yes |
| Frontend | node:20-alpine | ~150MB | ✅ Yes |
| PostgreSQL | postgres:15-alpine | ~200MB | - |
| Redis | redis:7-alpine | ~30MB | - |
| MinIO | minio/minio:latest | ~180MB | - |
| Nginx | nginx:alpine | ~40MB | - |

## 🔧 Configuration

### Environment Variables

Create `.env` file:

```env
# Database
DB_PASSWORD=strong_password_here
DB_NAME=crm
DB_USER=postgres

# Redis (optional password)
REDIS_PASSWORD=redis_password_here

# JWT
JWT_SECRET=your-256-bit-secret-key
JWT_TOKEN_DURATION=168

# MinIO
MINIO_USER=minioadmin
MINIO_PASSWORD=strong_minio_password

# Frontend
NEXT_PUBLIC_API_URL=https://your-domain.com

# Ports
HTTP_PORT=80
HTTPS_PORT=443
```

### SSL/TLS Setup

1. Obtain SSL certificates (Let's Encrypt recommended)
2. Update `nginx.production.conf`:
   - Uncomment SSL sections
   - Add certificate paths
3. Restart nginx container

## 🎯 Deployment Methods

### Method 1: Using Makefile (Recommended)

```bash
# Full production deployment
make prod

# Build only
make prod-build

# Start containers
make prod-up

# Stop containers
make prod-down

# View logs
make logs-prod
```

### Method 2: Using Deploy Script

```bash
# Interactive deployment
./deploy.sh
```

### Method 3: Manual Docker Compose

```bash
# Build images
docker-compose -f docker-compose.production.yml build

# Start all services
docker-compose -f docker-compose.production.yml up -d

# Check status
docker-compose -f docker-compose.production.yml ps

# View logs
docker-compose -f docker-compose.production.yml logs -f
```

## 📊 Monitoring

### Health Checks

```bash
# All services status
make health

# Individual checks
curl http://localhost/health        # Backend
curl http://localhost:3000          # Frontend
docker exec crm-postgres pg_isready # PostgreSQL
docker exec crm-redis redis-cli ping # Redis
```

### Logs

```bash
# All logs
docker-compose -f docker-compose.production.yml logs -f

# Specific service
docker-compose -f docker-compose.production.yml logs -f backend
docker-compose -f docker-compose.production.yml logs -f frontend

# Last 100 lines
docker-compose -f docker-compose.production.yml logs --tail=100
```

### Metrics

Access points:
- Nginx status: `http://localhost/nginx-status`
- Backend health: `http://localhost/api/health`

## 🔐 Security

### Checklist

- [ ] Change default passwords
- [ ] Update JWT secret
- [ ] Configure SSL/TLS
- [ ] Set up firewall rules
- [ ] Enable rate limiting
- [ ] Configure CORS properly
- [ ] Set secure headers
- [ ] Regular security updates

### Firewall Rules

```bash
# Allow only necessary ports
ufw allow 80/tcp   # HTTP
ufw allow 443/tcp  # HTTPS
ufw allow 22/tcp   # SSH (restrict to your IP)
ufw enable
```

## 💾 Backup & Restore

### Database Backup

```bash
# Create backup
make db-backup

# Or manually
docker exec crm-postgres pg_dump -U postgres crm > backup_$(date +%Y%m%d).sql
```

### Full System Backup

```bash
# Backup all data
docker run --rm \
  -v crm_new_postgres_data:/postgres \
  -v crm_new_redis_data:/redis \
  -v crm_new_minio_data:/minio \
  -v $(pwd)/backups:/backup \
  alpine tar czf /backup/full_backup_$(date +%Y%m%d).tar.gz \
  /postgres /redis /minio
```

### Restore

```bash
# Database restore
make db-restore FILE=backups/backup_20240101.sql

# Full restore
docker run --rm \
  -v crm_new_postgres_data:/postgres \
  -v crm_new_redis_data:/redis \
  -v crm_new_minio_data:/minio \
  -v $(pwd)/backups:/backup \
  alpine tar xzf /backup/full_backup_20240101.tar.gz
```

## 🔄 Updates

### Update Application

```bash
# Pull latest code
git pull

# Rebuild and restart
docker-compose -f docker-compose.production.yml build
docker-compose -f docker-compose.production.yml up -d

# Or using Makefile
make prod-build
make prod-up
```

### Update Dependencies

```bash
# Backend
go get -u ./...
go mod tidy

# Frontend
cd frontend
npm update

# Rebuild containers
make prod-build
```

## 🚨 Troubleshooting

### Common Issues

#### Container won't start
```bash
# Check logs
docker-compose -f docker-compose.production.yml logs [service_name]

# Check resources
docker system df
docker system prune -a  # Clean up
```

#### Database connection issues
```bash
# Check PostgreSQL
docker exec crm-postgres pg_isready
docker exec crm-postgres psql -U postgres -c "SELECT 1"
```

#### Redis connection issues
```bash
# Check Redis
docker exec crm-redis redis-cli ping
docker exec crm-redis redis-cli INFO server
```

#### Port already in use
```bash
# Find process using port
lsof -i :80
# Kill process
kill -9 [PID]
```

### Reset Everything

```bash
# Complete reset
make clean
make init
make prod
```

## 📈 Scaling

### Horizontal Scaling

For high load, use Docker Swarm or Kubernetes:

```yaml
# docker-compose.production.yml
services:
  backend:
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
```

### Load Balancing

Nginx already configured for upstream backends:
```nginx
upstream backend {
    least_conn;
    server backend1:8080;
    server backend2:8080;
    server backend3:8080;
}
```

## 📞 Support

For issues:
1. Check logs: `docker-compose logs`
2. Check health: `make health`
3. Review this guide
4. Check `CONTAINERIZATION_AUDIT.md` for detailed analysis

## 🎉 Success Indicators

When properly deployed, you should see:
- ✅ All containers running: `docker-compose ps`
- ✅ Health checks passing: `make health`
- ✅ Frontend accessible: `http://your-domain`
- ✅ API responding: `http://your-domain/api/health`
- ✅ Can login with credentials
- ✅ Database migrations applied
- ✅ File uploads working

---

**Last Updated:** 2024
**Version:** 1.0.0