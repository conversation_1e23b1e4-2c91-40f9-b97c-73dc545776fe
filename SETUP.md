# CRM System Setup Guide

## 🚀 Quick Start

### 1. First Time Setup
```bash
# Copy environment template
cp .env.example .env

# Initialize system (install dependencies + start infrastructure)
make init

# Start development environment
make dev
```

### 2. Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **MinIO Console**: http://localhost:9001 (minioadmin/minioadmin)
- **PostgreSQL**: localhost:5432 (postgres/postgres)
- **Redis**: localhost:6379

### 3. Default Login
- Email: `<EMAIL>`
- Password: `admin123`

## 📝 Development Workflow

### Starting Development
```bash
# Start infrastructure only (PostgreSQL, Redis, MinIO)
make dev-infra

# Run backend locally
make dev-backend

# Run frontend locally (in another terminal)
make dev-frontend

# Or start everything at once
make dev
```

### Stopping Services
```bash
# Stop all services
make stop

# Stop only local services (keep infrastructure running)
make stop-dev
```

### Database Management
```bash
# Connect to database
make db-shell

# Create backup
make db-backup

# Restore from backup
make db-restore FILE=backups/backup_file.sql

# Seed test data
docker exec -e PGPASSWORD=postgres -i crm-postgres psql -U postgres -d crm < migrations/simple_seed.sql
```

## 🐳 Production Deployment

### Build and Run
```bash
# Build production images
make prod-build

# Start production environment
make prod

# View production logs
make logs-prod
```

### Production URLs
- Frontend: http://localhost:80
- Backend API: http://localhost:80/api

## 🧪 Testing

```bash
# Run all tests
make test

# Backend tests
make test-backend

# Frontend tests
make test-frontend

# E2E tests
make test-e2e

# Type checking
make frontend-type-check

# Linting
make frontend-lint
```

## 🔧 Troubleshooting

### Check Service Health
```bash
make health
make status
```

### View Logs
```bash
# Infrastructure logs
make logs

# Backend logs (local)
make backend-logs

# Frontend logs (local)
make frontend-logs
```

### Reset Everything
```bash
# Clean all data and containers
make clean

# Full reset with fresh data
make dev-reset
```

## 📋 Environment Variables

Key environment variables in `.env`:

```env
# Database
DB_PASSWORD=postgres

# JWT Auth
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# MinIO
MINIO_USER=minioadmin
MINIO_PASSWORD=minioadmin

# Frontend API URL
NEXT_PUBLIC_API_URL=http://localhost:8080
```

## 🏗️ Project Structure

```
Development Mode:
┌─────────────────┐     ┌─────────────────┐
│  Frontend       │────>│  Backend        │
│  (localhost:3000│     │  (localhost:8080│
└─────────────────┘     └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌───────────▼───────────┐
         │    Docker Services    │
         ├───────────────────────┤
         │ PostgreSQL (5432)     │
         │ Redis (6379)          │
         │ MinIO (9000/9001)     │
         └───────────────────────┘

Production Mode:
         ┌───────────────────────┐
         │    Nginx (80/443)     │
         └───────────┬───────────┘
                     │
         ┌───────────▼───────────┐
         │  All services in      │
         │  Docker containers    │
         └───────────────────────┘
```

## 📚 Additional Commands

```bash
# Install/update dependencies
make install
make update

# Flush Redis cache
make redis-flush

# Connect to Redis CLI
make redis-cli

# MinIO shell access
make minio-shell
```

## ⚠️ Important Notes

1. **Development**: Frontend and backend run locally, only infrastructure in Docker
2. **Production**: Everything runs in Docker containers
3. **Database migrations**: Applied automatically on container start
4. **File uploads**: Stored in `./uploads` directory
5. **Logs**: Available in `backend.log` and `frontend.log` when running locally

## 🆘 Common Issues

### Port Already in Use
```bash
# Find process using port
lsof -i :3000
lsof -i :8080

# Kill process
kill -9 <PID>
```

### Database Connection Failed
```bash
# Check PostgreSQL is running
docker-compose ps

# Restart infrastructure
docker-compose restart postgres
```

### Frontend Can't Connect to Backend
- Check backend is running: `curl http://localhost:8080/health`
- Check CORS settings in backend
- Ensure `NEXT_PUBLIC_API_URL` is correct

### Clean Slate
```bash
# Nuclear option - removes everything
make clean
make init
```