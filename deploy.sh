#!/bin/bash

# CRM Quick Deployment Script
# This script helps deploy the CRM system quickly in production

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="CRM System"
COMPOSE_FILE="docker-compose.production.yml"

# Functions
print_header() {
    echo -e "${GREEN}============================================${NC}"
    echo -e "${GREEN}$1${NC}"
    echo -e "${GREEN}============================================${NC}"
}

print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

check_requirements() {
    print_header "Checking Requirements"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    print_success "Docker is installed"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    print_success "Docker Compose is installed"
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    print_success "Docker daemon is running"
}

setup_environment() {
    print_header "Setting Up Environment"
    
    # Create .env if it doesn't exist
    if [ ! -f .env ]; then
        cp .env.example .env
        print_warning ".env file created from template. Please update it with production values!"
        echo ""
        echo "Required environment variables to update:"
        echo "  - DB_PASSWORD"
        echo "  - JWT_SECRET"
        echo "  - REDIS_PASSWORD (optional but recommended)"
        echo "  - MINIO_PASSWORD"
        echo ""
        read -p "Press Enter after updating .env file to continue..."
    else
        print_success ".env file exists"
    fi
    
    # Create necessary directories
    mkdir -p uploads backups logs
    print_success "Created necessary directories"
}

build_images() {
    print_header "Building Docker Images"
    
    echo "This may take several minutes..."
    docker-compose -f $COMPOSE_FILE build
    
    print_success "Docker images built successfully"
}

start_services() {
    print_header "Starting Services"
    
    # Start infrastructure first
    echo "Starting infrastructure services..."
    docker-compose -f $COMPOSE_FILE up -d postgres redis minio
    
    # Wait for databases to be ready
    echo "Waiting for databases to be ready..."
    sleep 10
    
    # Start application services
    echo "Starting application services..."
    docker-compose -f $COMPOSE_FILE up -d backend frontend nginx
    
    print_success "All services started"
}

check_health() {
    print_header "Health Check"
    
    echo "Waiting for services to be healthy..."
    sleep 5
    
    # Check each service
    services=("postgres" "redis" "minio" "backend" "frontend")
    all_healthy=true
    
    for service in "${services[@]}"; do
        if docker-compose -f $COMPOSE_FILE ps | grep $service | grep -q "healthy\|Up"; then
            print_success "$service is running"
        else
            print_error "$service is not healthy"
            all_healthy=false
        fi
    done
    
    if $all_healthy; then
        print_success "All services are healthy!"
    else
        print_warning "Some services are not healthy. Check logs with: docker-compose -f $COMPOSE_FILE logs"
    fi
}

print_access_info() {
    print_header "Access Information"
    
    # Get the host IP
    HOST_IP=$(hostname -I | awk '{print $1}')
    
    echo ""
    echo "🚀 $PROJECT_NAME is now running!"
    echo ""
    echo "Access URLs:"
    echo "  Frontend:    http://localhost (or http://$HOST_IP)"
    echo "  Backend API: http://localhost/api (or http://$HOST_IP/api)"
    echo "  Health:      http://localhost/health"
    echo ""
    echo "Default credentials:"
    echo "  Email:    <EMAIL>"
    echo "  Password: admin123"
    echo ""
    echo "⚠️  IMPORTANT: Change the default password immediately!"
    echo ""
    echo "Useful commands:"
    echo "  View logs:        docker-compose -f $COMPOSE_FILE logs -f"
    echo "  Stop services:    docker-compose -f $COMPOSE_FILE down"
    echo "  Restart services: docker-compose -f $COMPOSE_FILE restart"
    echo "  Backup database:  make db-backup"
    echo ""
}

# Main execution
main() {
    print_header "🚀 $PROJECT_NAME Deployment"
    
    # Check requirements
    check_requirements
    
    # Setup environment
    setup_environment
    
    # Ask for confirmation
    echo ""
    read -p "Do you want to proceed with deployment? (y/n): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Deployment cancelled"
        exit 0
    fi
    
    # Build and start
    build_images
    start_services
    
    # Health check
    check_health
    
    # Print access information
    print_access_info
}

# Handle Ctrl+C
trap 'print_error "Deployment interrupted"; exit 1' INT

# Run main function
main