package events

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/domain/entities"
	"crm-backend/internal/usecases"
	"github.com/gofiber/contrib/websocket"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// Event represents a server-sent event
type Event struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
	UserID    *uuid.UUID  `json:"user_id,omitempty"`
}

// Client represents a connected SSE client
type Client struct {
	ID       string
	UserID   uuid.UUID
	Channel  chan Event
	Context  context.Context
	Cancel   context.CancelFunc
	LastSeen time.Time
}

// SSEManager manages server-sent events
type SSEManager struct {
	clients    map[string]*Client
	clientsMux sync.RWMutex
	register   chan *Client
	unregister chan *Client
	broadcast  chan Event
	shutdown   chan struct{}
}

// NewSSEManager creates a new SSE manager
func NewSSEManager() *SSEManager {
	return &SSEManager{
		clients:    make(map[string]*Client),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		broadcast:  make(chan Event, 1000),
		shutdown:   make(chan struct{}),
	}
}

// Start starts the SSE manager
func (m *SSEManager) Start() {
	go m.run()
}

// Stop stops the SSE manager
func (m *SSEManager) Stop() {
	close(m.shutdown)
}

// run runs the main event loop
func (m *SSEManager) run() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case client := <-m.register:
			m.clientsMux.Lock()
			m.clients[client.ID] = client
			m.clientsMux.Unlock()
			log.Printf("SSE client registered: %s (user: %s)", client.ID, client.UserID)

		case client := <-m.unregister:
			m.clientsMux.Lock()
			if _, ok := m.clients[client.ID]; ok {
				delete(m.clients, client.ID)
				close(client.Channel)
			}
			m.clientsMux.Unlock()
			log.Printf("SSE client unregistered: %s", client.ID)

		case event := <-m.broadcast:
			m.broadcastEvent(event)

		case <-ticker.C:
			m.cleanupStaleClients()

		case <-m.shutdown:
			m.shutdownAllClients()
			return
		}
	}
}

// RegisterClient registers a new SSE client
func (m *SSEManager) RegisterClient(userID uuid.UUID, ctx context.Context) *Client {
	clientID := uuid.New().String()
	clientCtx, cancel := context.WithCancel(ctx)

	client := &Client{
		ID:       clientID,
		UserID:   userID,
		Channel:  make(chan Event, 100),
		Context:  clientCtx,
		Cancel:   cancel,
		LastSeen: time.Now(),
	}

	m.register <- client
	return client
}

// UnregisterClient unregisters an SSE client
func (m *SSEManager) UnregisterClient(client *Client) {
	client.Cancel()
	m.unregister <- client
}

// BroadcastEvent broadcasts an event to all relevant clients
func (m *SSEManager) BroadcastEvent(event Event) {
	event.Timestamp = time.Now()
	m.broadcast <- event
}

// broadcastEvent sends an event to relevant clients
func (m *SSEManager) broadcastEvent(event Event) {
	m.clientsMux.RLock()
	defer m.clientsMux.RUnlock()

	for _, client := range m.clients {
		// Send to specific user or broadcast to all
		if event.UserID != nil && *event.UserID != client.UserID {
			continue
		}

		select {
		case client.Channel <- event:
			client.LastSeen = time.Now()
		default:
			// Channel is full, close the client
			log.Printf("SSE client channel full, closing: %s", client.ID)
			go m.UnregisterClient(client)
		}
	}
}

// cleanupStaleClients removes stale clients
func (m *SSEManager) cleanupStaleClients() {
	m.clientsMux.Lock()
	defer m.clientsMux.Unlock()

	staleTime := time.Now().Add(-5 * time.Minute)
	for id, client := range m.clients {
		if client.LastSeen.Before(staleTime) {
			log.Printf("Removing stale SSE client: %s", id)
			delete(m.clients, id)
			close(client.Channel)
			client.Cancel()
		}
	}
}

// shutdownAllClients closes all clients
func (m *SSEManager) shutdownAllClients() {
	m.clientsMux.Lock()
	defer m.clientsMux.Unlock()

	for _, client := range m.clients {
		close(client.Channel)
		client.Cancel()
	}
	m.clients = make(map[string]*Client)
}

// GetClientCount returns the number of connected clients
func (m *SSEManager) GetClientCount() int {
	m.clientsMux.RLock()
	defer m.clientsMux.RUnlock()
	return len(m.clients)
}

// GetClientsByUser returns clients for a specific user
func (m *SSEManager) GetClientsByUser(userID uuid.UUID) []*Client {
	m.clientsMux.RLock()
	defer m.clientsMux.RUnlock()

	var clients []*Client
	for _, client := range m.clients {
		if client.UserID == userID {
			clients = append(clients, client)
		}
	}
	return clients
}

// SSEHandler creates a Fiber handler for SSE connections
func (m *SSEManager) SSEHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Set SSE headers
		c.Set("Content-Type", "text/event-stream")
		c.Set("Cache-Control", "no-cache")
		c.Set("Connection", "keep-alive")
		c.Set("Access-Control-Allow-Origin", "*")
		c.Set("Transfer-Encoding", "chunked")

		// Get user ID from context (should be set by auth middleware)
		userID := getUserIDFromContext(c)
		if userID == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "Authentication required",
			})
		}

		// Register client
		client := m.RegisterClient(*userID, c.Context())
		defer m.UnregisterClient(client)

		// Use SetBodyStreamWriter for SSE streaming
		c.Context().SetBodyStreamWriter(func(w *bufio.Writer) {
			// Send initial connection event
			initialEvent := Event{
				Type: "connection",
				Data: map[string]interface{}{
					"status":    "connected",
					"client_id": client.ID,
				},
				Timestamp: time.Now(),
			}

			data, _ := json.Marshal(initialEvent)
			fmt.Fprintf(w, "data: %s\n\n", data)
			if err := w.Flush(); err != nil {
				log.Printf("SSE: Client disconnected: %v", err)
				return
			}

			// Keep connection alive with a ticker
			ticker := time.NewTicker(30 * time.Second)
			defer ticker.Stop()

			// Listen for events
			for {
				select {
				case event := <-client.Channel:
					data, err := json.Marshal(event)
					if err != nil {
						continue
					}
					fmt.Fprintf(w, "data: %s\n\n", data)
					if err := w.Flush(); err != nil {
						log.Printf("SSE: Error writing event: %v", err)
						return
					}

				case <-ticker.C:
					// Send keep-alive comment
					fmt.Fprintf(w, ": keep-alive\n\n")
					if err := w.Flush(); err != nil {
						log.Printf("SSE: Error writing keep-alive: %v", err)
						return
					}

				case <-client.Context.Done():
					return
				}
			}
		})
		return nil
	}
}

// WebSocketHandler creates a WebSocket handler for real-time events
func (m *SSEManager) WebSocketHandler() fiber.Handler {
	return websocket.New(func(c *websocket.Conn) {
		// Note: In a production environment, you'd implement proper authentication
		// for WebSocket connections. This is a simplified version.
		
		defer c.Close()
		
		// For demo purposes, generate a random user ID
		userID := uuid.New()
		client := m.RegisterClient(userID, context.Background())
		defer m.UnregisterClient(client)

		// Send initial connection message
		initialMessage := map[string]interface{}{
			"type": "connection",
			"data": map[string]interface{}{
				"status":    "connected",
				"client_id": client.ID,
			},
			"timestamp": time.Now(),
		}
		
		if err := c.WriteJSON(initialMessage); err != nil {
			log.Printf("WebSocket write error: %v", err)
			return
		}

		// Handle incoming messages in a goroutine
		go func() {
			for {
				var msg map[string]interface{}
				if err := c.ReadJSON(&msg); err != nil {
					log.Printf("WebSocket read error: %v", err)
					return
				}
				
				// Handle ping messages
				if msgType, ok := msg["type"].(string); ok && msgType == "ping" {
					pong := map[string]interface{}{
						"type":      "pong",
						"timestamp": time.Now(),
					}
					if err := c.WriteJSON(pong); err != nil {
						log.Printf("WebSocket pong error: %v", err)
						return
					}
				}
			}
		}()

		// Send events to WebSocket client
		for {
			select {
			case event := <-client.Channel:
				if err := c.WriteJSON(event); err != nil {
					log.Printf("WebSocket write error: %v", err)
					return
				}
			case <-client.Context.Done():
				return
			}
		}
	})
}


// EventBusImpl implements the EventBus interface
type EventBusImpl struct {
	sseManager *SSEManager
}

// NewEventBus creates a new event bus
func NewEventBus(sseManager *SSEManager) usecases.EventBus {
	return &EventBusImpl{
		sseManager: sseManager,
	}
}

// PublishCardCreated publishes card created event
func (e *EventBusImpl) PublishCardCreated(card *entities.Card) {
	event := Event{
		Type: "card.created",
		Data: card,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishCardUpdated publishes card updated event
func (e *EventBusImpl) PublishCardUpdated(card *entities.Card) {
	event := Event{
		Type: "card.updated",
		Data: card,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishCardDeleted publishes card deleted event
func (e *EventBusImpl) PublishCardDeleted(card *entities.Card) {
	event := Event{
		Type: "card.deleted",
		Data: map[string]interface{}{
			"id":    card.ID,
			"title": card.Title,
		},
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishCardStageChanged publishes card stage changed event
func (e *EventBusImpl) PublishCardStageChanged(card *entities.Card, oldStageID *uuid.UUID, newStageID *uuid.UUID) {
	event := Event{
		Type: "card.stage_changed",
		Data: map[string]interface{}{
			"card_id":      card.ID,
			"card_title":   card.Title,
			"old_stage_id": oldStageID,
			"new_stage_id": newStageID,
		},
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishContactCreated publishes contact created event
func (e *EventBusImpl) PublishContactCreated(contact *entities.Contact) {
	event := Event{
		Type: "contact.created",
		Data: contact,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishContactUpdated publishes contact updated event
func (e *EventBusImpl) PublishContactUpdated(contact *entities.Contact) {
	event := Event{
		Type: "contact.updated",
		Data: contact,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishContactDeleted publishes contact deleted event
func (e *EventBusImpl) PublishContactDeleted(contact *entities.Contact) {
	event := Event{
		Type: "contact.deleted",
		Data: map[string]interface{}{
			"id":   contact.ID,
			"name": contact.FullName(),
		},
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishCompanyCreated publishes company created event
func (e *EventBusImpl) PublishCompanyCreated(company *entities.Company) {
	event := Event{
		Type: "company.created",
		Data: company,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishCompanyUpdated publishes company updated event
func (e *EventBusImpl) PublishCompanyUpdated(company *entities.Company) {
	event := Event{
		Type: "company.updated",
		Data: company,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishCompanyDeleted publishes company deleted event
func (e *EventBusImpl) PublishCompanyDeleted(company *entities.Company) {
	event := Event{
		Type: "company.deleted",
		Data: map[string]interface{}{
			"id":   company.ID,
			"name": company.Name,
		},
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishPipelineCreated publishes pipeline created event
func (e *EventBusImpl) PublishPipelineCreated(pipeline *entities.Pipeline) {
	event := Event{
		Type: "pipeline.created",
		Data: pipeline,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishPipelineUpdated publishes pipeline updated event
func (e *EventBusImpl) PublishPipelineUpdated(pipeline *entities.Pipeline) {
	event := Event{
		Type: "pipeline.updated",
		Data: pipeline,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishPipelineDeleted publishes pipeline deleted event
func (e *EventBusImpl) PublishPipelineDeleted(pipeline *entities.Pipeline) {
	event := Event{
		Type: "pipeline.deleted",
		Data: map[string]interface{}{
			"id":   pipeline.ID,
			"name": pipeline.Name,
		},
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishActivityCreated publishes activity created event
func (e *EventBusImpl) PublishActivityCreated(activity *entities.Activity) {
	event := Event{
		Type: "activity.created",
		Data: activity,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishActivityUpdated publishes activity updated event
func (e *EventBusImpl) PublishActivityUpdated(activity *entities.Activity) {
	event := Event{
		Type: "activity.updated",
		Data: activity,
	}
	e.sseManager.BroadcastEvent(event)
}

// PublishActivityCompleted publishes activity completed event
func (e *EventBusImpl) PublishActivityCompleted(activity *entities.Activity) {
	event := Event{
		Type: "activity.completed",
		Data: activity,
	}
	e.sseManager.BroadcastEvent(event)
}

// getUserIDFromContext extracts user ID from Fiber context
func getUserIDFromContext(c *fiber.Ctx) *uuid.UUID {
	// Import the context key from middleware package
	if user := c.UserContext().Value(middleware.UserContextKey); user != nil {
		if u, ok := user.(*entities.User); ok {
			return &u.ID
		}
	}
	return nil
}