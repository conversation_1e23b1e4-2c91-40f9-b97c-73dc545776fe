package webhook

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

// WebhookService handles webhook delivery
type WebhookService struct {
	webhookRepo         repositories.WebhookRepository
	webhookDeliveryRepo repositories.WebhookDeliveryRepository
	httpClient          *http.Client
}

// NewWebhookService creates a new webhook service
func NewWebhookService(
	webhookRepo repositories.WebhookRepository,
	webhookDeliveryRepo repositories.WebhookDeliveryRepository,
) *WebhookService {
	return &WebhookService{
		webhookRepo:         webhookRepo,
		webhookDeliveryRepo: webhookDeliveryRepo,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// WebhookPayload represents the payload sent to webhook endpoints
type WebhookPayload struct {
	Event     entities.WebhookEvent `json:"event"`
	Data      interface{}           `json:"data"`
	Timestamp time.Time             `json:"timestamp"`
	ID        string                `json:"id"`
}

// DeliverWebhook delivers a webhook for a specific event
func (s *WebhookService) DeliverWebhook(ctx context.Context, event entities.WebhookEvent, data interface{}) error {
	// Get active webhooks for this event
	webhooks, err := s.webhookRepo.GetWebhooksForEvent(ctx, event)
	if err != nil {
		return fmt.Errorf("failed to get webhooks for event %s: %w", event, err)
	}

	// Deliver to each webhook
	for _, webhook := range webhooks {
		if !webhook.IsActive {
			continue
		}

		if err := s.deliverToWebhook(ctx, webhook, event, data); err != nil {
			// Log error but continue with other webhooks
			fmt.Printf("Failed to deliver webhook %s: %v\n", webhook.ID, err)
		}
	}

	return nil
}

// deliverToWebhook delivers a webhook to a specific endpoint
func (s *WebhookService) deliverToWebhook(ctx context.Context, webhook entities.Webhook, event entities.WebhookEvent, data interface{}) error {
	deliveryID := uuid.New().String()
	
	// Create payload
	payload := WebhookPayload{
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
		ID:        deliveryID,
	}

	// Serialize payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create delivery record
	delivery := &entities.WebhookDelivery{
		WebhookID:    webhook.ID.String(),
		Event:        event,
		Payload:      entities.CustomFields{"payload": payload},
		AttemptCount: 1,
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", webhook.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		delivery.Success = false
		delivery.Response = fmt.Sprintf("Failed to create request: %v", err)
		s.webhookDeliveryRepo.Create(ctx, delivery)
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "CRM-Webhook/1.0")
	
	// Add custom headers
	for key, value := range webhook.Headers {
		if valueStr, ok := value.(string); ok {
			req.Header.Set(key, valueStr)
		}
	}

	// Add signature
	signature := s.generateSignature(payloadBytes, webhook.Secret)
	req.Header.Set("X-Webhook-Signature", signature)
	req.Header.Set("X-Webhook-Event", string(event))
	req.Header.Set("X-Webhook-ID", deliveryID)

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		delivery.Success = false
		delivery.Response = fmt.Sprintf("Request failed: %v", err)
		delivery.ScheduleRetry()
		s.webhookDeliveryRepo.Create(ctx, delivery)
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	var responseBody bytes.Buffer
	responseBody.ReadFrom(resp.Body)

	// Update delivery record
	delivery.StatusCode = resp.StatusCode
	delivery.Response = responseBody.String()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		delivery.Success = true
	} else {
		delivery.Success = false
		delivery.ScheduleRetry()
	}

	// Save delivery record
	if err := s.webhookDeliveryRepo.Create(ctx, delivery); err != nil {
		fmt.Printf("Failed to save delivery record: %v\n", err)
	}

	if !delivery.Success {
		return fmt.Errorf("webhook delivery failed with status %d: %s", resp.StatusCode, responseBody.String())
	}

	return nil
}

// RetryFailedDeliveries retries failed webhook deliveries
func (s *WebhookService) RetryFailedDeliveries(ctx context.Context) error {
	// Get pending retries
	deliveries, err := s.webhookDeliveryRepo.GetPendingRetries(ctx, time.Now(), 100)
	if err != nil {
		return fmt.Errorf("failed to get pending retries: %w", err)
	}

	for _, delivery := range deliveries {
		if !delivery.CanRetry() {
			continue
		}

		// Get webhook
		webhook, err := s.webhookRepo.GetByID(ctx, uuid.MustParse(delivery.WebhookID))
		if err != nil {
			continue
		}

		// Extract payload from delivery
		var payload WebhookPayload
		if payloadData, ok := delivery.Payload["payload"]; ok {
			if payloadBytes, err := json.Marshal(payloadData); err == nil {
				json.Unmarshal(payloadBytes, &payload)
			}
		}

		// Retry delivery
		delivery.AttemptCount++
		
		if err := s.retryDelivery(ctx, *webhook, &delivery, payload); err != nil {
			fmt.Printf("Retry failed for delivery %s: %v\n", delivery.ID, err)
		}
	}

	return nil
}

// retryDelivery retries a specific delivery
func (s *WebhookService) retryDelivery(ctx context.Context, webhook entities.Webhook, delivery *entities.WebhookDelivery, payload WebhookPayload) error {
	// Serialize payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", webhook.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		delivery.Success = false
		delivery.Response = fmt.Sprintf("Failed to create retry request: %v", err)
		delivery.ScheduleRetry()
		s.webhookDeliveryRepo.Update(ctx, delivery)
		return fmt.Errorf("failed to create retry request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "CRM-Webhook/1.0")
	req.Header.Set("X-Webhook-Retry", fmt.Sprintf("%d", delivery.AttemptCount))
	
	// Add custom headers
	for key, value := range webhook.Headers {
		if valueStr, ok := value.(string); ok {
			req.Header.Set(key, valueStr)
		}
	}

	// Add signature
	signature := s.generateSignature(payloadBytes, webhook.Secret)
	req.Header.Set("X-Webhook-Signature", signature)
	req.Header.Set("X-Webhook-Event", string(delivery.Event))
	req.Header.Set("X-Webhook-ID", delivery.ID.String())

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		delivery.Success = false
		delivery.Response = fmt.Sprintf("Retry request failed: %v", err)
		delivery.ScheduleRetry()
		s.webhookDeliveryRepo.Update(ctx, delivery)
		return fmt.Errorf("retry request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	var responseBody bytes.Buffer
	responseBody.ReadFrom(resp.Body)

	// Update delivery record
	delivery.StatusCode = resp.StatusCode
	delivery.Response = responseBody.String()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		delivery.Success = true
		delivery.NextRetryAt = nil
	} else {
		delivery.Success = false
		delivery.ScheduleRetry()
	}

	// Save delivery record
	if err := s.webhookDeliveryRepo.Update(ctx, delivery); err != nil {
		fmt.Printf("Failed to update delivery record: %v\n", err)
	}

	return nil
}

// generateSignature generates HMAC signature for webhook payload
func (s *WebhookService) generateSignature(payload []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	return "sha256=" + hex.EncodeToString(h.Sum(nil))
}

// ValidateSignature validates webhook signature
func (s *WebhookService) ValidateSignature(payload []byte, signature string, secret string) bool {
	expectedSignature := s.generateSignature(payload, secret)
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// CleanupOldDeliveries removes old webhook delivery records
func (s *WebhookService) CleanupOldDeliveries(ctx context.Context, olderThanDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -olderThanDays)
	return s.webhookDeliveryRepo.CleanupOldDeliveries(ctx, cutoffTime)
}

// GetDeliveryStats returns delivery statistics
func (s *WebhookService) GetDeliveryStats(ctx context.Context, webhookID uuid.UUID, days int) (map[string]int64, error) {
	// This would implement statistics gathering
	// For now, return empty stats
	stats := map[string]int64{
		"total":      0,
		"successful": 0,
		"failed":     0,
		"pending":    0,
	}
	return stats, nil
}

// StartRetryWorker starts a background worker to retry failed deliveries
func (s *WebhookService) StartRetryWorker(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := s.RetryFailedDeliveries(ctx); err != nil {
				fmt.Printf("Error retrying failed deliveries: %v\n", err)
			}
		case <-ctx.Done():
			return
		}
	}
}

// StartCleanupWorker starts a background worker to cleanup old deliveries
func (s *WebhookService) StartCleanupWorker(ctx context.Context) {
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := s.CleanupOldDeliveries(ctx, 30); err != nil {
				fmt.Printf("Error cleaning up old deliveries: %v\n", err)
			}
		case <-ctx.Done():
			return
		}
	}
}