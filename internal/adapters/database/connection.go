package database

import (
	"fmt"
	"log"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/infrastructure/config"
	"crm-backend/internal/infrastructure/database"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// NewConnection creates a new database connection
func NewConnection(cfg *config.DatabaseConfig) (*gorm.DB, error) {
	dsn := cfg.GetDSN()

	// Configure GORM logger
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	}

	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// Set optimized connection pool settings for production workloads
	// These settings are based on best practices for high-traffic CRM systems
	sqlDB.SetMaxIdleConns(10)  // Keep 10 connections ready for immediate use
	sqlDB.SetMaxOpenConns(50)  // Limit to 50 concurrent connections (PostgreSQL default is 100)
	sqlDB.SetConnMaxLifetime(time.Hour) // Recycle connections every hour to prevent issues
	sqlDB.SetConnMaxIdleTime(10 * time.Minute) // Close idle connections after 10 minutes
	
	// Log connection pool stats
	log.Printf("Database connection pool configured: MaxIdle=%d, MaxOpen=%d, MaxLifetime=%s, MaxIdleTime=%s",
		10, 50, time.Hour, 10*time.Minute)

	// Test connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Successfully connected to database")
	return db, nil
}

// AutoMigrate runs database migrations
func AutoMigrate(db *gorm.DB) error {
	log.Println("Starting database migration...")

	// Enable UUID extension
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		return fmt.Errorf("failed to create uuid extension: %w", err)
	}

	// Create tables
	err := db.AutoMigrate(
		// Authorization
		&entities.Role{},
		&entities.Permission{},
		&entities.RolePermission{},
		&entities.User{},
		
		// Core entities
		&entities.FieldDefinition{},
		&entities.Pipeline{},
		&entities.Stage{},
		&entities.Company{},
		&entities.Contact{},
		&entities.Card{},
		&entities.Activity{},
		&entities.Tag{},
		&entities.Attachment{},
		
		// Communication entities
		&entities.Email{},
		&entities.EmailTemplate{},
		&entities.EmailSequence{},
		&entities.EmailSequenceStep{},
		&entities.Comment{},
		&entities.CommentEdit{},
		&entities.Notification{},
		&entities.NotificationPreference{},
		
		// Integration entities
		&entities.Integration{},
		&entities.IntegrationLog{},
		
		// Webhook entities
		&entities.Webhook{},
		&entities.WebhookDelivery{},
		
		// Data transfer entity
		&entities.DataTransfer{},
	)

	if err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	// Create indexes for JSONB fields
	if err := createJSONBIndexes(db); err != nil {
		return fmt.Errorf("failed to create JSONB indexes: %w", err)
	}

	log.Println("Database migration completed successfully")
	return nil
}

// createJSONBIndexes creates GIN indexes for JSONB fields to improve query performance
func createJSONBIndexes(db *gorm.DB) error {
	indexes := []string{
		// JSONB indexes for custom fields
		"CREATE INDEX IF NOT EXISTS idx_cards_custom_fields_gin ON cards USING GIN (custom_fields)",
		"CREATE INDEX IF NOT EXISTS idx_contacts_custom_fields_gin ON contacts USING GIN (custom_fields)",
		"CREATE INDEX IF NOT EXISTS idx_companies_custom_fields_gin ON companies USING GIN (custom_fields)",
		"CREATE INDEX IF NOT EXISTS idx_activities_metadata_gin ON activities USING GIN (metadata)",
		"CREATE INDEX IF NOT EXISTS idx_field_definitions_options_gin ON field_definitions USING GIN (options)",
		"CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_payload_gin ON webhook_deliveries USING GIN (payload)",
		
		// Email indexes
		"CREATE INDEX IF NOT EXISTS idx_emails_to_gin ON emails USING GIN (\"to\")",
		"CREATE INDEX IF NOT EXISTS idx_emails_thread_id ON emails(thread_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_emails_message_id ON emails(message_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_emails_status ON emails(status) WHERE deleted_at IS NULL",
		
		// Comment indexes
		"CREATE INDEX IF NOT EXISTS idx_comments_entity ON comments(entity_type, entity_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON comments(parent_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_comments_reactions_gin ON comments USING GIN (reactions)",
		
		// Notification indexes
		"CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, is_read) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_notifications_scheduled ON notifications(scheduled_for) WHERE is_sent = false AND deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_notifications_group_key ON notifications(group_key) WHERE deleted_at IS NULL",
		
		// Integration indexes
		"CREATE INDEX IF NOT EXISTS idx_integrations_type_enabled ON integrations(type, is_enabled) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_integrations_config_gin ON integrations USING GIN (config)",
		"CREATE INDEX IF NOT EXISTS idx_integration_logs_integration_id ON integration_logs(integration_id) WHERE deleted_at IS NULL",
		
		// Performance indexes for common queries
		"CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_cards_pipeline_id_stage_id ON cards(pipeline_id, stage_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_cards_contact_id ON cards(contact_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_cards_company_id ON cards(company_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_activities_card_id ON activities(card_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_contacts_company_id ON contacts(company_id) WHERE deleted_at IS NULL",
		"CREATE INDEX IF NOT EXISTS idx_stages_pipeline_id_sort_order ON stages(pipeline_id, sort_order) WHERE deleted_at IS NULL",
	}

	for _, index := range indexes {
		if err := db.Exec(index).Error; err != nil {
			return fmt.Errorf("failed to create index: %s - %w", index, err)
		}
	}

	return nil
}

// SeedData seeds initial data into the database
func SeedData(db *gorm.DB) error {
	log.Println("Starting data seeding...")

	// Step 1: Create test users FIRST (they are referenced by pipelines)
	testUsers := []struct {
		Email     string
		Password  string
		FirstName string
		LastName  string
		Role      string
	}{
		{
			Email:     "<EMAIL>",
			Password:  "admin123",
			FirstName: "Admin",
			LastName:  "User",
			Role:      "admin",
		},
		{
			Email:     "<EMAIL>",
			Password:  "manager123",
			FirstName: "John",
			LastName:  "Manager",
			Role:      "user",
		},
		{
			Email:     "<EMAIL>",
			Password:  "sales123",
			FirstName: "Sarah",
			LastName:  "Sales",
			Role:      "user",
		},
		{
			Email:     "<EMAIL>",
			Password:  "support123",
			FirstName: "Mike",
			LastName:  "Support",
			Role:      "user",
		},
		{
			Email:     "<EMAIL>",
			Password:  "test123",
			FirstName: "Test",
			LastName:  "User",
			Role:      "user",
		},
	}

	for _, userData := range testUsers {
		var existingUser entities.User
		result := db.Where("email = ?", userData.Email).First(&existingUser)
		
		if result.Error != nil {
			// User doesn't exist, create it
			newUser := &entities.User{
				Email:       userData.Email,
				FirstName:   userData.FirstName,
				LastName:    userData.LastName,
				LegacyRole:  userData.Role,
				IsActive:    true,
			}
			newUser.SetPassword(userData.Password)

			if err := db.Create(newUser).Error; err != nil {
				return fmt.Errorf("failed to create user %s: %w", userData.Email, err)
			}
			
			log.Printf("Created test user: %s with password: %s", userData.Email, userData.Password)
		}
	}

	// Step 2: Get the admin user for pipeline CreatedByID reference
	var adminUser entities.User
	if err := db.Where("legacy_role = ?", "admin").First(&adminUser).Error; err != nil {
		// Admin user might not have been created in previous runs, skip pipelines for now
		log.Println("Admin user not found, skipping pipeline creation for now")
		
		// Step 8: Seed roles and permissions first
		seeder := database.NewSeeder(db)
		if err := seeder.Seed(); err != nil {
			return fmt.Errorf("failed to seed roles and permissions: %w", err)
		}
		
		log.Println("Data seeding completed successfully")
		return nil
	}

	// Step 3: Create pipelines with stages (now that admin user exists)
	var pipelineCount int64
	db.Model(&entities.Pipeline{}).Count(&pipelineCount)
	
	if pipelineCount == 0 {
		pipelines := []struct {
			Pipeline entities.Pipeline
			Stages   []entities.Stage
		}{
			{
				Pipeline: entities.Pipeline{
					Name:        "Sales Pipeline",
					Description: "Main sales process pipeline",
					Type:        entities.PipelineTypeSales,
					IsDefault:   true,
					Color:       "#3B82F6",
					Icon:        "💰",
					CreatedByID: &adminUser.ID,
					Settings: entities.PipelineSettings{
						AutomationEnabled: true,
						SLAEnabled:        true,
						AllowedRoles:      []string{"admin", "user"},
					},
				},
				Stages: []entities.Stage{
					{Name: "Lead", SortOrder: 1, Color: "#6B7280", Icon: "🎯", Probability: 10},
					{Name: "Qualified", SortOrder: 2, Color: "#F59E0B", Icon: "✅", Probability: 25},
					{Name: "Proposal", SortOrder: 3, Color: "#3B82F6", Icon: "📄", Probability: 50},
					{Name: "Negotiation", SortOrder: 4, Color: "#8B5CF6", Icon: "🤝", Probability: 75},
					{Name: "Closed Won", SortOrder: 5, Color: "#10B981", Icon: "🎉", IsClosedWon: true, IsFinal: true, Probability: 100},
					{Name: "Closed Lost", SortOrder: 6, Color: "#EF4444", Icon: "❌", IsClosedLost: true, IsFinal: true, Probability: 0},
				},
			},
			{
				Pipeline: entities.Pipeline{
					Name:        "HR Pipeline",
					Description: "Recruitment and hiring process",
					Type:        entities.PipelineTypeHR,
					Color:       "#8B5CF6",
					Icon:        "👥",
					CreatedByID: &adminUser.ID,
					Settings: entities.PipelineSettings{
						AutomationEnabled: true,
						AllowedRoles:      []string{"admin", "user"},
					},
				},
				Stages: []entities.Stage{
					{Name: "Application", SortOrder: 1, Color: "#6B7280", Icon: "📝"},
					{Name: "Screening", SortOrder: 2, Color: "#F59E0B", Icon: "🔍"},
					{Name: "Interview", SortOrder: 3, Color: "#3B82F6", Icon: "🗣️"},
					{Name: "Technical Test", SortOrder: 4, Color: "#8B5CF6", Icon: "💻"},
					{Name: "Final Interview", SortOrder: 5, Color: "#EC4899", Icon: "🤔"},
					{Name: "Offer", SortOrder: 6, Color: "#10B981", Icon: "📮"},
					{Name: "Hired", SortOrder: 7, Color: "#059669", Icon: "✅", IsClosedWon: true, IsFinal: true},
					{Name: "Rejected", SortOrder: 8, Color: "#EF4444", Icon: "❌", IsClosedLost: true, IsFinal: true},
				},
			},
			{
				Pipeline: entities.Pipeline{
					Name:        "Support Tickets",
					Description: "Customer support workflow",
					Type:        entities.PipelineTypeSupport,
					Color:       "#F59E0B",
					Icon:        "🎫",
					CreatedByID: &adminUser.ID,
					Settings: entities.PipelineSettings{
						SLAEnabled:   true,
						AllowedRoles: []string{"admin", "user"},
					},
				},
				Stages: []entities.Stage{
					{Name: "New", SortOrder: 1, Color: "#EF4444", Icon: "🆕"},
					{Name: "In Progress", SortOrder: 2, Color: "#F59E0B", Icon: "⚙️"},
					{Name: "Waiting Customer", SortOrder: 3, Color: "#8B5CF6", Icon: "⏳"},
					{Name: "Resolved", SortOrder: 4, Color: "#10B981", Icon: "✅", IsClosedWon: true, IsFinal: true},
					{Name: "Closed", SortOrder: 5, Color: "#6B7280", Icon: "🔒", IsClosedLost: true, IsFinal: true},
				},
			},
		}

		for _, p := range pipelines {
			if err := db.Create(&p.Pipeline).Error; err != nil {
				return fmt.Errorf("failed to create pipeline %s: %w", p.Pipeline.Name, err)
			}
			
			// Create stages for this pipeline
			for i := range p.Stages {
				p.Stages[i].PipelineID = p.Pipeline.ID
			}
			
			if err := db.Create(&p.Stages).Error; err != nil {
				return fmt.Errorf("failed to create stages for pipeline %s: %w", p.Pipeline.Name, err)
			}
		}
		
		log.Printf("Created %d pipelines with stages", len(pipelines))
	}

	// Step 4: Create default field definitions
	var fieldCount int64
	db.Model(&entities.FieldDefinition{}).Count(&fieldCount)
	
	if fieldCount == 0 {
		fields := []entities.FieldDefinition{
			{
				Name:       "source",
				Label:      "Lead Source",
				Type:       entities.FieldTypeSelect,
				Required:   false,
				EntityType: "card",
				Order:      1,
				Options: entities.FieldOptions{
					Options: []string{"Website", "Referral", "Social Media", "Cold Call", "Email", "Advertisement"},
				},
			},
			{
				Name:       "budget",
				Label:      "Budget Range",
				Type:       entities.FieldTypeSelect,
				Required:   false,
				EntityType: "card",
				Order:      2,
				Options: entities.FieldOptions{
					Options: []string{"< $10K", "$10K - $50K", "$50K - $100K", "$100K - $500K", "> $500K"},
				},
			},
			{
				Name:       "linkedin",
				Label:      "LinkedIn Profile",
				Type:       entities.FieldTypeURL,
				Required:   false,
				EntityType: "contact",
				Order:      1,
			},
			{
				Name:       "department",
				Label:      "Department",
				Type:       entities.FieldTypeText,
				Required:   false,
				EntityType: "contact",
				Order:      2,
			},
		}

		if err := db.Create(&fields).Error; err != nil {
			return fmt.Errorf("failed to create default field definitions: %w", err)
		}
		log.Printf("Created %d field definitions", len(fields))
	}

	// Step 5: Create test companies if they don't exist
	var companyCount int64
	db.Model(&entities.Company{}).Count(&companyCount)
	
	if companyCount == 0 {
		companies := []entities.Company{
			{
				Name:        "Tech Corp",
				Website:     "https://techcorp.example.com",
				Industry:    "Technology",
				Size:        "100-500",
				Description: "Leading technology solutions provider",
				Email:       "<EMAIL>",
				Phone:       "******-567-8900",
				City:        "San Francisco",
				State:       "CA",
				Country:     "USA",
			},
			{
				Name:        "Sales Pro Inc",
				Website:     "https://salespro.example.com",
				Industry:    "Sales & Marketing",
				Size:        "50-100",
				Description: "Sales automation and CRM solutions",
				Email:       "<EMAIL>",
				Phone:       "******-567-8901",
				City:        "New York",
				State:       "NY",
				Country:     "USA",
			},
			{
				Name:        "Global Services Ltd",
				Website:     "https://globalservices.example.com",
				Industry:    "Consulting",
				Size:        "500-1000",
				Description: "International business consulting",
				Email:       "<EMAIL>",
				Phone:       "******-567-8902",
				City:        "London",
				Country:     "UK",
			},
		}

		if err := db.Create(&companies).Error; err != nil {
			return fmt.Errorf("failed to create test companies: %w", err)
		}
		log.Printf("Created %d test companies", len(companies))

		// Step 6: Create test contacts for companies
		contacts := []entities.Contact{
			{
				FirstName: "Alice",
				LastName:  "Johnson",
				Email:     "<EMAIL>",
				Phone:     "******-567-8910",
				JobTitle:  "CTO",
				CompanyID: &companies[0].ID,
			},
			{
				FirstName: "Bob",
				LastName:  "Smith",
				Email:     "<EMAIL>",
				Phone:     "******-567-8911",
				JobTitle:  "Sales Director",
				CompanyID: &companies[1].ID,
			},
			{
				FirstName: "Carol",
				LastName:  "Williams",
				Email:     "<EMAIL>",
				Phone:     "******-567-8912",
				JobTitle:  "Project Manager",
				CompanyID: &companies[2].ID,
			},
			{
				FirstName: "David",
				LastName:  "Brown",
				Email:     "<EMAIL>",
				Phone:     "******-567-8913",
				JobTitle:  "Lead Developer",
				CompanyID: &companies[0].ID,
			},
		}

		if err := db.Create(&contacts).Error; err != nil {
			return fmt.Errorf("failed to create test contacts: %w", err)
		}
		log.Printf("Created %d test contacts", len(contacts))

		// Step 7: Create test cards in pipelines
		var salesPipeline entities.Pipeline
		db.Where("type = ?", entities.PipelineTypeSales).First(&salesPipeline)
		
		var salesStages []entities.Stage
		db.Where("pipeline_id = ?", salesPipeline.ID).Order("sort_order").Find(&salesStages)
		
		if len(salesStages) > 0 {
			// Use the admin user we already retrieved earlier (no need to query again)
			now := time.Now()
			cards := []entities.Card{
				{
					Title:       "Enterprise Software Deal",
					Description: "Potential enterprise software implementation",
					Value:       250000,
					Currency:    "USD",
					Priority:    entities.PriorityHigh,
					PipelineID:  &salesPipeline.ID,
					StageID:     &salesStages[2].ID, // Proposal stage
					ContactID:   &contacts[0].ID,
					CompanyID:   &companies[0].ID,
					AssignedToID: &adminUser.ID,
					StageEnteredAt: &now,
				},
				{
					Title:       "CRM Implementation Project",
					Description: "Full CRM system implementation and training",
					Value:       75000,
					Currency:    "USD",
					Priority:    entities.PriorityMedium,
					PipelineID:  &salesPipeline.ID,
					StageID:     &salesStages[1].ID, // Qualified stage
					ContactID:   &contacts[1].ID,
					CompanyID:   &companies[1].ID,
					AssignedToID: &adminUser.ID,
					StageEnteredAt: &now,
				},
				{
					Title:       "Consulting Services Agreement",
					Description: "Annual consulting services contract",
					Value:       120000,
					Currency:    "USD",
					Priority:    entities.PriorityMedium,
					PipelineID:  &salesPipeline.ID,
					StageID:     &salesStages[3].ID, // Negotiation stage
					ContactID:   &contacts[2].ID,
					CompanyID:   &companies[2].ID,
					AssignedToID: &adminUser.ID,
					StageEnteredAt: &now,
				},
				{
					Title:       "Website Development",
					Description: "Corporate website redesign and development",
					Value:       35000,
					Currency:    "USD",
					Priority:    entities.PriorityLow,
					PipelineID:  &salesPipeline.ID,
					StageID:     &salesStages[0].ID, // Lead stage
					ContactID:   &contacts[3].ID,
					CompanyID:   &companies[0].ID,
					AssignedToID: &adminUser.ID,
					StageEnteredAt: &now,
				},
				{
					Title:       "Mobile App Development",
					Description: "iOS and Android app development project",
					Value:       95000,
					Currency:    "USD",
					Priority:    entities.PriorityHigh,
					PipelineID:  &salesPipeline.ID,
					StageID:     &salesStages[1].ID, // Qualified stage
					ContactID:   &contacts[0].ID,
					CompanyID:   &companies[0].ID,
					AssignedToID: &adminUser.ID,
					StageEnteredAt: &now,
				},
			}

			if err := db.Create(&cards).Error; err != nil {
				return fmt.Errorf("failed to create test cards: %w", err)
			}
			log.Printf("Created %d test cards", len(cards))
		}
	}
	
	// Step 8: Seed roles and permissions
	seeder := database.NewSeeder(db)
	if err := seeder.Seed(); err != nil {
		return fmt.Errorf("failed to seed roles and permissions: %w", err)
	}
	
	// Step 9: Create default email integration
	var integrationCount int64
	db.Model(&entities.Integration{}).Where("type = ?", entities.IntegrationTypeEmail).Count(&integrationCount)
	
	if integrationCount == 0 {
		defaultEmailIntegration := &entities.Integration{
			Name:        "Default SMTP",
			Type:        entities.IntegrationTypeEmail,
			Provider:    entities.EmailProviderSMTP,
			Description: "Default SMTP email integration (disabled by default, configure in settings)",
			Icon:        "📧",
			IsEnabled:   false, // Disabled by default, user needs to configure
			IsDefault:   true,
			Config: map[string]interface{}{
				"smtp_host":       "smtp.gmail.com",
				"smtp_port":       587,
				"smtp_username":   "",
				"smtp_password":   "",
				"smtp_from":       "<EMAIL>",
				"smtp_from_name":  "CRM System",
				"use_tls":         false,
				"use_starttls":    true,
				"max_retries":     3,
				"retry_delay":     5,
				"send_timeout":    30,
				"rate_limit":      60,
				"enable_tracking": false,
				"enable_queue":    true,
				"queue_workers":   4,
			},
			IsConnected: false,
			CreatedByID: &adminUser.ID,
		}
		
		if err := db.Create(&defaultEmailIntegration).Error; err != nil {
			log.Printf("Warning: failed to create default email integration: %v", err)
		} else {
			log.Println("Created default email integration (disabled)")
		}
	}

	log.Println("Data seeding completed successfully")
	return nil
}