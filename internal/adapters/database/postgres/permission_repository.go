package postgres

import (
	"context"
	"errors"
	
	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type permissionRepository struct {
	db *gorm.DB
}

func NewPermissionRepository(db *gorm.DB) repositories.PermissionRepository {
	return &permissionRepository{db: db}
}

func (p *permissionRepository) Create(ctx context.Context, permission *entities.Permission) error {
	return p.db.WithContext(ctx).Create(permission).Error
}

func (p *permissionRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Permission, error) {
	var permission entities.Permission
	err := p.db.WithContext(ctx).First(&permission, "id = ?", id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &permission, nil
}

func (p *permissionRepository) GetByResourceAction(ctx context.Context, resource, action string) (*entities.Permission, error) {
	var permission entities.Permission
	err := p.db.WithContext(ctx).
		First(&permission, "resource = ? AND action = ?", resource, action).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &permission, nil
}

func (p *permissionRepository) List(ctx context.Context) ([]entities.Permission, error) {
	var permissions []entities.Permission
	err := p.db.WithContext(ctx).
		Order("resource ASC, action ASC").
		Find(&permissions).Error
	return permissions, err
}

func (p *permissionRepository) ListByResource(ctx context.Context, resource string) ([]entities.Permission, error) {
	var permissions []entities.Permission
	err := p.db.WithContext(ctx).
		Where("resource = ?", resource).
		Order("action ASC").
		Find(&permissions).Error
	return permissions, err
}

func (p *permissionRepository) Update(ctx context.Context, permission *entities.Permission) error {
	return p.db.WithContext(ctx).Save(permission).Error
}

func (p *permissionRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return p.db.WithContext(ctx).Delete(&entities.Permission{}, "id = ?", id).Error
}

func (p *permissionRepository) CreateBulk(ctx context.Context, permissions []entities.Permission) error {
	if len(permissions) == 0 {
		return nil
	}
	return p.db.WithContext(ctx).CreateInBatches(permissions, 100).Error
}

func (p *permissionRepository) GetByIDs(ctx context.Context, ids []uuid.UUID) ([]entities.Permission, error) {
	var permissions []entities.Permission
	if len(ids) == 0 {
		return permissions, nil
	}
	err := p.db.WithContext(ctx).
		Where("id IN ?", ids).
		Find(&permissions).Error
	return permissions, err
}

func (p *permissionRepository) GetPermissionRoles(ctx context.Context, permissionID uuid.UUID) ([]entities.Role, error) {
	var roles []entities.Role
	err := p.db.WithContext(ctx).
		Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
		Where("role_permissions.permission_id = ?", permissionID).
		Find(&roles).Error
	return roles, err
}