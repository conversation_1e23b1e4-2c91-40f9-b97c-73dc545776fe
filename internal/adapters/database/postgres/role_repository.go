package postgres

import (
	"context"
	"errors"
	
	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type roleRepository struct {
	db *gorm.DB
}

func NewRoleRepository(db *gorm.DB) repositories.RoleRepository {
	return &roleRepository{db: db}
}

func (r *roleRepository) Create(ctx context.Context, role *entities.Role) error {
	return r.db.WithContext(ctx).Create(role).Error
}

func (r *roleRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Role, error) {
	var role entities.Role
	err := r.db.WithContext(ctx).
		Preload("Permissions").
		First(&role, "id = ?", id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	
	// Count users for this role
	count, err := r.CountUsersInRole(ctx, role.ID)
	if err == nil {
		role.UserCount = int(count)
	}
	
	return &role, nil
}

func (r *roleRepository) GetByName(ctx context.Context, name string) (*entities.Role, error) {
	var role entities.Role
	err := r.db.WithContext(ctx).
		Preload("Permissions").
		First(&role, "name = ?", name).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	
	// Count users for this role
	count, err := r.CountUsersInRole(ctx, role.ID)
	if err == nil {
		role.UserCount = int(count)
	}
	
	return &role, nil
}

func (r *roleRepository) List(ctx context.Context, limit, offset int) ([]entities.Role, int64, error) {
	var roles []entities.Role
	var total int64
	
	query := r.db.WithContext(ctx).Model(&entities.Role{})
	
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	err := query.
		Preload("Permissions").
		Order("created_at DESC").
		Find(&roles).Error
	
	if err != nil {
		return nil, 0, err
	}
	
	// Batch query to count users for all roles at once (avoid N+1)
	if len(roles) > 0 {
		type UserCount struct {
			RoleID uuid.UUID
			Count  int64
		}
		var counts []UserCount
		
		roleIDs := make([]uuid.UUID, len(roles))
		for i, role := range roles {
			roleIDs[i] = role.ID
		}
		
		err := r.db.WithContext(ctx).
			Table("users").
			Select("role_id, COUNT(*) as count").
			Where("role_id IN ? AND deleted_at IS NULL", roleIDs).
			Group("role_id").
			Scan(&counts).Error
		
		if err == nil {
			countMap := make(map[uuid.UUID]int64)
			for _, c := range counts {
				countMap[c.RoleID] = c.Count
			}
			
			for i := range roles {
				roles[i].UserCount = int(countMap[roles[i].ID])
			}
		}
	}
	
	return roles, total, nil
}

func (r *roleRepository) Update(ctx context.Context, role *entities.Role) error {
	return r.db.WithContext(ctx).Save(role).Error
}

func (r *roleRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.Role{}, "id = ?", id).Error
}

func (r *roleRepository) GetRolePermissions(ctx context.Context, roleID uuid.UUID) ([]entities.Permission, error) {
	var permissions []entities.Permission
	err := r.db.WithContext(ctx).
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", roleID).
		Find(&permissions).Error
	return permissions, err
}

func (r *roleRepository) AssignPermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, permID := range permissionIDs {
			rolePermission := entities.RolePermission{
				RoleID:       roleID,
				PermissionID: permID,
			}
			if err := tx.Create(&rolePermission).Error; err != nil {
				if !errors.Is(err, gorm.ErrDuplicatedKey) {
					return err
				}
			}
		}
		return nil
	})
}

func (r *roleRepository) RemovePermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).
		Where("role_id = ? AND permission_id IN ?", roleID, permissionIDs).
		Delete(&entities.RolePermission{}).Error
}

func (r *roleRepository) ReplacePermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Remove all existing permissions
		if err := tx.Where("role_id = ?", roleID).Delete(&entities.RolePermission{}).Error; err != nil {
			return err
		}
		
		// Add new permissions
		for _, permID := range permissionIDs {
			rolePermission := entities.RolePermission{
				RoleID:       roleID,
				PermissionID: permID,
			}
			if err := tx.Create(&rolePermission).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (r *roleRepository) GetRoleUsers(ctx context.Context, roleID uuid.UUID) ([]entities.User, error) {
	var users []entities.User
	err := r.db.WithContext(ctx).
		Where("role_id = ?", roleID).
		Find(&users).Error
	return users, err
}

func (r *roleRepository) AssignUserToRole(ctx context.Context, userID, roleID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("id = ?", userID).
		Update("role_id", roleID).Error
}

func (r *roleRepository) RemoveUserFromRole(ctx context.Context, userID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("id = ?", userID).
		Update("role_id", nil).Error
}

func (r *roleRepository) CountUsersInRole(ctx context.Context, roleID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("role_id = ?", roleID).
		Count(&count).Error
	return count, err
}