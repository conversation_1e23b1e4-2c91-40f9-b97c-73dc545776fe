package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// stageRepository implements repositories.StageRepository
type stageRepository struct {
	db *gorm.DB
}

// NewStageRepository creates a new stage repository
func NewStageRepository(db *gorm.DB) repositories.StageRepository {
	return &stageRepository{db: db}
}

// Create creates a new stage
func (r *stageRepository) Create(ctx context.Context, stage *entities.Stage) error {
	return r.db.WithContext(ctx).Create(stage).Error
}

// GetByID retrieves a stage by ID
func (r *stageRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Stage, error) {
	var stage entities.Stage
	err := r.db.WithContext(ctx).
		Preload("Pipeline").
		Preload("Cards").
		First(&stage, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &stage, nil
}

// GetByPipelineID retrieves all stages for a pipeline
func (r *stageRepository) GetByPipelineID(ctx context.Context, pipelineID uuid.UUID) ([]entities.Stage, error) {
	var stages []entities.Stage
	err := r.db.WithContext(ctx).
		Where("pipeline_id = ?", pipelineID).
		Order("sort_order ASC").
		Find(&stages).Error
	return stages, err
}

// Update updates a stage
func (r *stageRepository) Update(ctx context.Context, stage *entities.Stage) error {
	return r.db.WithContext(ctx).Save(stage).Error
}

// Delete deletes a stage
func (r *stageRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.Stage{}, "id = ?", id).Error
}

// UpdateOrder updates the order of stages
func (r *stageRepository) UpdateOrder(ctx context.Context, pipelineID uuid.UUID, stageIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for i, stageID := range stageIDs {
			if err := tx.Model(&entities.Stage{}).
				Where("id = ? AND pipeline_id = ?", stageID, pipelineID).
				Update("sort_order", i).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetWithCardCount retrieves stages with card count
func (r *stageRepository) GetWithCardCount(ctx context.Context, pipelineID uuid.UUID) ([]entities.Stage, error) {
	var stages []entities.Stage
	err := r.db.WithContext(ctx).
		Select("stages.*, COUNT(cards.id) as card_count").
		Joins("LEFT JOIN cards ON cards.stage_id = stages.id").
		Where("stages.pipeline_id = ?", pipelineID).
		Group("stages.id").
		Order("stages.sort_order ASC").
		Find(&stages).Error
	return stages, err
}

// ListByPipelineID retrieves all stages for a pipeline
func (r *stageRepository) ListByPipelineID(ctx context.Context, pipelineID uuid.UUID) ([]entities.Stage, error) {
	var stages []entities.Stage
	err := r.db.WithContext(ctx).
		Where("pipeline_id = ?", pipelineID).
		Order("sort_order ASC").
		Find(&stages).Error
	return stages, err
}

// ReorderStages reorders stages in a pipeline
func (r *stageRepository) ReorderStages(ctx context.Context, pipelineID uuid.UUID, stageOrders []struct {
	ID    uuid.UUID
	Order int
}) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, so := range stageOrders {
			if err := tx.Model(&entities.Stage{}).
				Where("id = ? AND pipeline_id = ?", so.ID, pipelineID).
				Update("sort_order", so.Order).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetWithCards retrieves a stage with its cards
func (r *stageRepository) GetWithCards(ctx context.Context, id uuid.UUID) (*entities.Stage, error) {
	var stage entities.Stage
	err := r.db.WithContext(ctx).
		Preload("Pipeline").
		Preload("Cards").
		Preload("Cards.Contact").
		Preload("Cards.Company").
		Preload("Cards.AssignedTo").
		First(&stage, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &stage, nil
}