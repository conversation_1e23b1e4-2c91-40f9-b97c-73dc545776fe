package repositories

import (
	"context"
	"fmt"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// cardRepository implements the CardRepository interface
type cardRepository struct {
	db *gorm.DB
}

// NewCardRepository creates a new card repository
func NewCardRepository(db *gorm.DB) repositories.CardRepository {
	return &cardRepository{db: db}
}

// Create creates a new card
func (r *cardRepository) Create(ctx context.Context, card *entities.Card) error {
	return r.db.WithContext(ctx).Create(card).Error
}

// GetByID retrieves a card by ID
func (r *cardRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Card, error) {
	var card entities.Card
	err := r.db.WithContext(ctx).First(&card, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// GetByIDWithRelations retrieves a card with all relations
func (r *cardRepository) GetByIDWithRelations(ctx context.Context, id uuid.UUID) (*entities.Card, error) {
	var card entities.Card
	err := r.db.WithContext(ctx).
		Preload("Stage").
		Preload("Stage.Pipeline").
		Preload("Contact").
		Preload("Company").
		Preload("AssignedTo").
		Preload("Tags").
		Preload("Attachments").
		Preload("Activities", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at DESC").Limit(10)
		}).
		Preload("Activities.User").
		First(&card, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// GetByIDWithSelectivePreload retrieves a card with selective relation preloading
func (r *cardRepository) GetByIDWithSelectivePreload(ctx context.Context, id uuid.UUID, preloads []string) (*entities.Card, error) {
	var card entities.Card
	query := r.db.WithContext(ctx)
	
	// Only preload requested relations
	for _, preload := range preloads {
		if preload == "Activities" {
			// Special handling for activities to limit and order
			query = query.Preload("Activities", func(db *gorm.DB) *gorm.DB {
				return db.Order("created_at DESC").Limit(10)
			})
			query = query.Preload("Activities.User")
		} else {
			query = query.Preload(preload)
		}
	}
	
	err := query.First(&card, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

// Update updates an existing card
func (r *cardRepository) Update(ctx context.Context, card *entities.Card) error {
	return r.db.WithContext(ctx).Save(card).Error
}

// UpdateCustomFields updates only custom fields of a card
func (r *cardRepository) UpdateCustomFields(ctx context.Context, id uuid.UUID, fields entities.CustomFields) error {
	return r.db.WithContext(ctx).
		Model(&entities.Card{}).
		Where("id = ?", id).
		Update("custom_fields", fields).Error
}

// Delete deletes a card
func (r *cardRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.Card{}, "id = ?", id).Error
}

// List lists cards with filtering - optimized version
func (r *cardRepository) List(ctx context.Context, filters repositories.CardFilters, limit, offset int) ([]entities.Card, int64, error) {
	var cards []entities.Card
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Card{})

	// Apply filters
	query = r.applyFilters(query, filters)

	// Get total count using a separate optimized query
	countQuery := query.Session(&gorm.Session{})
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get cards with selective preloading based on actual usage
	// Only preload what's necessary for list views
	err := query.
		Preload("Stage", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name", "color", "pipeline_id")
		}).
		Preload("Contact", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "first_name", "last_name", "email")
		}).
		Preload("Company", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name")
		}).
		Preload("AssignedTo", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "first_name", "last_name", "email")
		}).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&cards).Error

	return cards, total, err
}

// ListOptimized lists cards with optimized query using joins instead of preloads
func (r *cardRepository) ListOptimized(ctx context.Context, filters repositories.CardFilters, limit, offset int) ([]entities.Card, int64, error) {
	var total int64
	
	// First get total count
	countQuery := r.db.WithContext(ctx).Model(&entities.Card{})
	countQuery = r.applyFilters(countQuery, filters)
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Use raw SQL for optimized query with joins
	var cards []entities.Card
	query := `
		SELECT 
			c.*,
			s.id as stage_id, s.name as stage_name, s.color as stage_color,
			con.id as contact_id, con.first_name as contact_first_name, con.last_name as contact_last_name,
			com.id as company_id, com.name as company_name,
			u.id as assigned_to_id, u.first_name as assigned_first_name, u.last_name as assigned_last_name
		FROM cards c
		LEFT JOIN stages s ON c.stage_id = s.id
		LEFT JOIN contacts con ON c.contact_id = con.id
		LEFT JOIN companies com ON c.company_id = com.id
		LEFT JOIN users u ON c.assigned_to_id = u.id
		WHERE c.deleted_at IS NULL
		ORDER BY c.created_at DESC
		LIMIT ? OFFSET ?
	`
	
	if err := r.db.Raw(query, limit, offset).Scan(&cards).Error; err != nil {
		return nil, 0, err
	}

	return cards, total, nil
}

// ListByStageID lists cards by stage ID
func (r *cardRepository) ListByStageID(ctx context.Context, stageID uuid.UUID, limit, offset int) ([]entities.Card, int64, error) {
	var cards []entities.Card
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Card{}).Where("stage_id = ?", stageID)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := query.
		Preload("Contact").
		Preload("Company").
		Preload("AssignedTo").
		Preload("Tags").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&cards).Error

	return cards, total, err
}

// ListByContactID lists cards by contact ID
func (r *cardRepository) ListByContactID(ctx context.Context, contactID uuid.UUID, limit, offset int) ([]entities.Card, int64, error) {
	var cards []entities.Card
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Card{}).Where("contact_id = ?", contactID)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := query.
		Preload("Stage").
		Preload("Company").
		Preload("AssignedTo").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&cards).Error

	return cards, total, err
}

// ListByCompanyID lists cards by company ID
func (r *cardRepository) ListByCompanyID(ctx context.Context, companyID uuid.UUID, limit, offset int) ([]entities.Card, int64, error) {
	var cards []entities.Card
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Card{}).Where("company_id = ?", companyID)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := query.
		Preload("Stage").
		Preload("Contact").
		Preload("AssignedTo").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&cards).Error

	return cards, total, err
}

// MoveToStage moves a card to a different stage
func (r *cardRepository) MoveToStage(ctx context.Context, cardID, stageID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&entities.Card{}).
		Where("id = ?", cardID).
		Update("stage_id", stageID).Error
}

// GetOverdueCards retrieves overdue cards
func (r *cardRepository) GetOverdueCards(ctx context.Context, limit, offset int) ([]entities.Card, int64, error) {
	var cards []entities.Card
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Card{}).
		Where("expected_close_date < NOW() AND actual_close_date IS NULL")

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := query.
		Preload("Stage").
		Preload("Contact").
		Preload("Company").
		Preload("AssignedTo").
		Order("expected_close_date ASC").
		Limit(limit).
		Offset(offset).
		Find(&cards).Error

	return cards, total, err
}

// GetCardsByValue retrieves cards within a value range
func (r *cardRepository) GetCardsByValue(ctx context.Context, minValue, maxValue float64, limit, offset int) ([]entities.Card, int64, error) {
	var cards []entities.Card
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Card{}).
		Where("value BETWEEN ? AND ?", minValue, maxValue)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := query.
		Preload("Stage").
		Preload("Contact").
		Preload("Company").
		Preload("AssignedTo").
		Order("value DESC").
		Limit(limit).
		Offset(offset).
		Find(&cards).Error

	return cards, total, err
}

// SearchByCustomFields searches cards by custom fields - optimized with GIN index
func (r *cardRepository) SearchByCustomFields(ctx context.Context, searchQuery map[string]interface{}, limit, offset int) ([]entities.Card, int64, error) {
	var cards []entities.Card
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Card{})

	// Build optimized JSONB query using GIN index
	// Use @> operator for better index utilization
	if len(searchQuery) > 0 {
		query = query.Where("custom_fields @> ?", searchQuery)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := query.
		Preload("Stage").
		Preload("Contact").
		Preload("Company").
		Preload("AssignedTo").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&cards).Error

	return cards, total, err
}

// applyFilters applies filters to the query
func (r *cardRepository) applyFilters(query *gorm.DB, filters repositories.CardFilters) *gorm.DB {
	if filters.StageID != nil {
		query = query.Where("stage_id = ?", *filters.StageID)
	}

	if filters.ContactID != nil {
		query = query.Where("contact_id = ?", *filters.ContactID)
	}

	if filters.CompanyID != nil {
		query = query.Where("company_id = ?", *filters.CompanyID)
	}

	if filters.AssignedToID != nil {
		query = query.Where("assigned_to_id = ?", *filters.AssignedToID)
	}

	if filters.Priority != nil {
		query = query.Where("priority = ?", *filters.Priority)
	}

	if filters.Search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?",
			fmt.Sprintf("%%%s%%", filters.Search),
			fmt.Sprintf("%%%s%%", filters.Search))
	}

	if len(filters.Tags) > 0 {
		query = query.Joins("JOIN card_tags ON cards.id = card_tags.card_id").
			Where("card_tags.tag_id IN ?", filters.Tags)
	}

	if len(filters.CustomFields) > 0 {
		for key, value := range filters.CustomFields {
			query = query.Where("custom_fields ->> ? = ?", key, value)
		}
	}

	return query
}