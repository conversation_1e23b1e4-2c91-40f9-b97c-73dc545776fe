package repositories

import (
	"context"
	"fmt"
	"strings"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// companyRepository implements the CompanyRepository interface
type companyRepository struct {
	db *gorm.DB
}

// NewCompanyRepository creates a new company repository
func NewCompanyRepository(db *gorm.DB) repositories.CompanyRepository {
	return &companyRepository{db: db}
}

// Create creates a new company
func (r *companyRepository) Create(ctx context.Context, company *entities.Company) error {
	return r.db.WithContext(ctx).Create(company).Error
}

// GetByID retrieves a company by ID
func (r *companyRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Company, error) {
	var company entities.Company
	err := r.db.WithContext(ctx).First(&company, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &company, nil
}

// GetByIDWithRelations retrieves a company by ID with all relations
func (r *companyRepository) GetByIDWithRelations(ctx context.Context, id uuid.UUID) (*entities.Company, error) {
	var company entities.Company
	err := r.db.WithContext(ctx).
		Preload("Contacts").
		Preload("Cards").
		First(&company, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &company, nil
}

// GetByName retrieves a company by name
func (r *companyRepository) GetByName(ctx context.Context, name string) (*entities.Company, error) {
	var company entities.Company
	err := r.db.WithContext(ctx).First(&company, "LOWER(name) = LOWER(?)", name).Error
	if err != nil {
		return nil, err
	}
	return &company, nil
}

// Update updates an existing company
func (r *companyRepository) Update(ctx context.Context, company *entities.Company) error {
	return r.db.WithContext(ctx).Save(company).Error
}

// UpdateCustomFields updates only custom fields of a company using JSONB partial updates
func (r *companyRepository) UpdateCustomFields(ctx context.Context, id uuid.UUID, fields entities.CustomFields) error {
	// Use JSONB || operator for efficient partial updates in PostgreSQL
	return r.db.WithContext(ctx).
		Model(&entities.Company{}).
		Where("id = ?", id).
		Update("custom_fields", gorm.Expr("custom_fields || ?", fields)).Error
}

// Delete soft deletes a company
func (r *companyRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.Company{}, "id = ?", id).Error
}

// List lists companies with filtering and pagination
func (r *companyRepository) List(ctx context.Context, filters repositories.CompanyFilters, limit, offset int) ([]entities.Company, int64, error) {
	var companies []entities.Company
	var total int64

	// Create base query with Session to ensure independent instances
	baseQuery := r.db.Session(&gorm.Session{}).WithContext(ctx).Model(&entities.Company{})
	
	// Apply filters to base query
	baseQuery = r.applyCompanyFilters(baseQuery, filters)

	// Clone query for counting using Session to ensure true independence
	countQuery := r.db.Session(&gorm.Session{}).WithContext(ctx).Model(&entities.Company{})
	countQuery = r.applyCompanyFilters(countQuery, filters)
	
	// Get total count
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get companies with contact count using the filtered query
	err := baseQuery.
		Select("companies.*, (SELECT COUNT(*) FROM contacts WHERE contacts.company_id = companies.id AND contacts.deleted_at IS NULL) as contact_count").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&companies).Error

	return companies, total, err
}

// Search searches companies by query across multiple fields
func (r *companyRepository) Search(ctx context.Context, query string, limit, offset int) ([]entities.Company, int64, error) {
	var companies []entities.Company
	var total int64

	searchQuery := "%" + strings.ToLower(query) + "%"
	
	// Create base query with search conditions using Session
	baseQuery := r.db.Session(&gorm.Session{}).WithContext(ctx).Model(&entities.Company{}).Where(
		"LOWER(name) LIKE ? OR LOWER(website) LIKE ? OR LOWER(industry) LIKE ? OR LOWER(email) LIKE ? OR LOWER(phone) LIKE ? OR LOWER(city) LIKE ? OR LOWER(country) LIKE ?",
		searchQuery, searchQuery, searchQuery, searchQuery, searchQuery, searchQuery, searchQuery,
	)

	// Clone for counting using Session
	countQuery := r.db.Session(&gorm.Session{}).WithContext(ctx).Model(&entities.Company{}).Where(
		"LOWER(name) LIKE ? OR LOWER(website) LIKE ? OR LOWER(industry) LIKE ? OR LOWER(email) LIKE ? OR LOWER(phone) LIKE ? OR LOWER(city) LIKE ? OR LOWER(country) LIKE ?",
		searchQuery, searchQuery, searchQuery, searchQuery, searchQuery, searchQuery, searchQuery,
	)
	
	// Get total count
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get companies
	err := baseQuery.
		Select("companies.*, (SELECT COUNT(*) FROM contacts WHERE contacts.company_id = companies.id AND contacts.deleted_at IS NULL) as contact_count").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&companies).Error

	return companies, total, err
}

// SearchByCustomFields searches companies by custom fields using JSONB queries
func (r *companyRepository) SearchByCustomFields(ctx context.Context, searchQuery map[string]interface{}, limit, offset int) ([]entities.Company, int64, error) {
	var companies []entities.Company
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Company{})

	// Build JSONB query conditions
	for key, value := range searchQuery {
		switch v := value.(type) {
		case string:
			// For string values, use JSONB ->> operator with ILIKE for case-insensitive search
			query = query.Where("custom_fields ->> ? ILIKE ?", key, fmt.Sprintf("%%%s%%", v))
		case []interface{}:
			// For arrays, use JSONB ? operator to check if any value exists
			placeholders := make([]string, len(v))
			values := make([]interface{}, len(v)+1)
			values[0] = key
			
			for i, item := range v {
				placeholders[i] = "?"
				values[i+1] = fmt.Sprintf("%v", item)
			}
			
			query = query.Where(
				fmt.Sprintf("custom_fields ->> ? IN (%s)", strings.Join(placeholders, ",")),
				values...,
			)
		default:
			// For other types, use exact match with JSONB ->> operator
			query = query.Where("custom_fields ->> ? = ?", key, fmt.Sprintf("%v", v))
		}
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get companies
	err := query.
		Select("companies.*, (SELECT COUNT(*) FROM contacts WHERE contacts.company_id = companies.id AND contacts.deleted_at IS NULL) as contact_count").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&companies).Error

	return companies, total, err
}

// GetByIndustry retrieves companies by industry with pagination
func (r *companyRepository) GetByIndustry(ctx context.Context, industry string, limit, offset int) ([]entities.Company, int64, error) {
	var companies []entities.Company
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Company{}).Where("LOWER(industry) = LOWER(?)", industry)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get companies
	err := query.
		Select("companies.*, (SELECT COUNT(*) FROM contacts WHERE contacts.company_id = companies.id AND contacts.deleted_at IS NULL) as contact_count").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&companies).Error

	return companies, total, err
}

// applyCompanyFilters applies filters to the query
func (r *companyRepository) applyCompanyFilters(query *gorm.DB, filters repositories.CompanyFilters) *gorm.DB {
	if filters.Industry != "" {
		query = query.Where("LOWER(industry) = LOWER(?)", filters.Industry)
	}

	if filters.Size != "" {
		query = query.Where("LOWER(size) = LOWER(?)", filters.Size)
	}

	if filters.Country != "" {
		query = query.Where("LOWER(country) = LOWER(?)", filters.Country)
	}

	if filters.Search != "" {
		searchQuery := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(name) LIKE ? OR LOWER(website) LIKE ? OR LOWER(industry) LIKE ? OR LOWER(email) LIKE ? OR LOWER(phone) LIKE ? OR LOWER(city) LIKE ? OR LOWER(country) LIKE ?",
			searchQuery, searchQuery, searchQuery, searchQuery, searchQuery, searchQuery, searchQuery,
		)
	}

	// Apply custom fields filters using JSONB queries
	for key, value := range filters.CustomFields {
		switch v := value.(type) {
		case string:
			query = query.Where("custom_fields ->> ? ILIKE ?", key, fmt.Sprintf("%%%s%%", v))
		default:
			query = query.Where("custom_fields ->> ? = ?", key, fmt.Sprintf("%v", v))
		}
	}

	return query
}