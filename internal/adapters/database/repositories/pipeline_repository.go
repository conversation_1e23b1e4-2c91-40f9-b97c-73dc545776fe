package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// pipelineRepository implements repositories.PipelineRepository
type pipelineRepository struct {
	db *gorm.DB
}

// NewPipelineRepository creates a new pipeline repository
func NewPipelineRepository(db *gorm.DB) repositories.PipelineRepository {
	return &pipelineRepository{db: db}
}

// Create creates a new pipeline
func (r *pipelineRepository) Create(ctx context.Context, pipeline *entities.Pipeline) error {
	return r.db.WithContext(ctx).Create(pipeline).Error
}

// GetByID retrieves a pipeline by ID
func (r *pipelineRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Pipeline, error) {
	var pipeline entities.Pipeline
	err := r.db.WithContext(ctx).First(&pipeline, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &pipeline, nil
}

// GetByIDWithStages retrieves a pipeline by ID with its stages
func (r *pipelineRepository) GetByIDWithStages(ctx context.Context, id uuid.UUID) (*entities.Pipeline, error) {
	var pipeline entities.Pipeline
	err := r.db.WithContext(ctx).
		Preload("Stages", func(db *gorm.DB) *gorm.DB {
			return db.Order("sort_order ASC")
		}).
		Preload("Stages.Cards").
		Preload("Stages.Cards.Contact").
		Preload("Stages.Cards.Company").
		Preload("Stages.Cards.AssignedTo").
		First(&pipeline, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &pipeline, nil
}

// Update updates a pipeline
func (r *pipelineRepository) Update(ctx context.Context, pipeline *entities.Pipeline) error {
	return r.db.WithContext(ctx).Save(pipeline).Error
}

// Delete deletes a pipeline
func (r *pipelineRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.Pipeline{}, "id = ?", id).Error
}

// List retrieves all pipelines with pagination
func (r *pipelineRepository) List(ctx context.Context, limit, offset int) ([]entities.Pipeline, int64, error) {
	var pipelines []entities.Pipeline
	var total int64

	// Count total pipelines
	if err := r.db.WithContext(ctx).
		Model(&entities.Pipeline{}).
		Where("is_active = ?", true).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get pipelines with pagination
	query := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("sort_order ASC, name ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&pipelines).Error
	return pipelines, total, err
}

// ListWithStages retrieves all pipelines with their stages
func (r *pipelineRepository) ListWithStages(ctx context.Context, limit, offset int) ([]entities.Pipeline, int64, error) {
	var pipelines []entities.Pipeline
	var total int64

	// Count total pipelines
	if err := r.db.WithContext(ctx).
		Model(&entities.Pipeline{}).
		Where("is_active = ?", true).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get pipelines with stages
	query := r.db.WithContext(ctx).
		Preload("Stages", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_active = ?", true).Order("sort_order ASC")
		}).
		Preload("Stages.Cards", func(db *gorm.DB) *gorm.DB {
			return db.Order("stage_id ASC").Limit(50) // Limit cards per stage for performance
		}).
		Preload("Stages.Cards.Contact").
		Preload("Stages.Cards.Company").
		Preload("Stages.Cards.AssignedTo").
		Where("is_active = ?", true).
		Order("sort_order ASC, name ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&pipelines).Error
	return pipelines, total, err
}

// GetDefault retrieves the default pipeline
func (r *pipelineRepository) GetDefault(ctx context.Context) (*entities.Pipeline, error) {
	var pipeline entities.Pipeline
	err := r.db.WithContext(ctx).
		Preload("Stages", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_active = ?", true).Order("sort_order ASC")
		}).
		Where("is_default = ? AND is_active = ?", true, true).
		First(&pipeline).Error
	if err != nil {
		return nil, err
	}
	return &pipeline, nil
}

// SetDefault sets a pipeline as default
func (r *pipelineRepository) SetDefault(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Remove default from all pipelines
		if err := tx.Model(&entities.Pipeline{}).
			Where("is_default = ?", true).
			Update("is_default", false).Error; err != nil {
			return err
		}

		// Set new default
		return tx.Model(&entities.Pipeline{}).
			Where("id = ?", id).
			Update("is_default", true).Error
	})
}

// CreateWithStages creates a pipeline with its stages in a transaction
func (r *pipelineRepository) CreateWithStages(ctx context.Context, pipeline *entities.Pipeline) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Create the pipeline without stages first
		if err := tx.Omit("Stages").Create(pipeline).Error; err != nil {
			return err
		}

		// Create stages if provided
		if len(pipeline.Stages) > 0 {
			for i := range pipeline.Stages {
				// Ensure each stage has a unique ID and correct PipelineID
				if pipeline.Stages[i].ID == uuid.Nil {
					pipeline.Stages[i].ID = uuid.New()
				}
				pipeline.Stages[i].PipelineID = pipeline.ID
			}
			if err := tx.Create(&pipeline.Stages).Error; err != nil {
				return err
			}
		}

		return nil
	})
}