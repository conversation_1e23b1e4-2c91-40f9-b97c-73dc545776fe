package repositories

import (
	"context"
	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type dataTransferRepository struct {
	db *gorm.DB
}

func NewDataTransferRepository(db *gorm.DB) repositories.DataTransferRepository {
	return &dataTransferRepository{db: db}
}

func (r *dataTransferRepository) Create(ctx context.Context, transfer *entities.DataTransfer) error {
	return r.db.WithContext(ctx).Create(transfer).Error
}

func (r *dataTransferRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.DataTransfer, error) {
	var transfer entities.DataTransfer
	err := r.db.WithContext(ctx).
		Preload("CreatedBy").
		First(&transfer, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &transfer, nil
}

func (r *dataTransferRepository) Update(ctx context.Context, transfer *entities.DataTransfer) error {
	return r.db.WithContext(ctx).Save(transfer).Error
}

func (r *dataTransferRepository) List(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*entities.DataTransfer, int64, error) {
	var transfers []*entities.DataTransfer
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.DataTransfer{})
	if userID != uuid.Nil {
		query = query.Where("created_by_id = ?", userID)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := query.
		Preload("CreatedBy").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&transfers).Error

	return transfers, total, err
}

func (r *dataTransferRepository) GetPending(ctx context.Context) ([]*entities.DataTransfer, error) {
	var transfers []*entities.DataTransfer
	err := r.db.WithContext(ctx).
		Where("status = ?", entities.DataTransferStatusPending).
		Find(&transfers).Error
	return transfers, err
}

func (r *dataTransferRepository) GetByStatus(ctx context.Context, status entities.DataTransferStatus) ([]*entities.DataTransfer, error) {
	var transfers []*entities.DataTransfer
	err := r.db.WithContext(ctx).
		Where("status = ?", status).
		Find(&transfers).Error
	return transfers, err
}