package repositories

import (
	"context"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// activityRepository implements repositories.ActivityRepository
type activityRepository struct {
	db *gorm.DB
}

// NewActivityRepository creates a new activity repository
func NewActivityRepository(db *gorm.DB) repositories.ActivityRepository {
	return &activityRepository{db: db}
}

// Create creates a new activity
func (r *activityRepository) Create(ctx context.Context, activity *entities.Activity) error {
	return r.db.WithContext(ctx).Create(activity).Error
}

// GetByID retrieves an activity by ID
func (r *activityRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Activity, error) {
	var activity entities.Activity
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		First(&activity, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &activity, nil
}

// List retrieves activities with filters
func (r *activityRepository) List(ctx context.Context, filters repositories.ActivityFilters, limit, offset int) ([]entities.Activity, int64, error) {
	var activities []entities.Activity
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Activity{})

	// Apply filters
	if filters.UserID != nil {
		query = query.Where("user_id = ?", *filters.UserID)
	}
	if filters.CardID != nil {
		query = query.Where("card_id = ?", *filters.CardID)
	}
	if filters.ContactID != nil {
		query = query.Where("contact_id = ?", *filters.ContactID)
	}
	if filters.CompanyID != nil {
		query = query.Where("company_id = ?", *filters.CompanyID)
	}
	if filters.Type != nil {
		query = query.Where("type = ?", *filters.Type)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}
	if filters.Completed != nil {
		if *filters.Completed {
			query = query.Where("completed_at IS NOT NULL")
		} else {
			query = query.Where("completed_at IS NULL")
		}
	}

	// Count total
	query.Count(&count)

	// Apply pagination and fetch
	err := query.
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error

	return activities, count, err
}

// Update updates an activity
func (r *activityRepository) Update(ctx context.Context, activity *entities.Activity) error {
	return r.db.WithContext(ctx).Save(activity).Error
}

// Delete deletes an activity
func (r *activityRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.Activity{}, "id = ?", id).Error
}

// CompleteActivity marks an activity as completed
func (r *activityRepository) CompleteActivity(ctx context.Context, id uuid.UUID) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&entities.Activity{}).
		Where("id = ?", id).
		Update("completed_at", &now).Error
}

// GetByCardID retrieves activities for a card
func (r *activityRepository) GetByCardID(ctx context.Context, cardID uuid.UUID, limit int) ([]entities.Activity, error) {
	var activities []entities.Activity
	err := r.db.WithContext(ctx).
		Where("card_id = ?", cardID).
		Preload("User").
		Order("created_at DESC").
		Limit(limit).
		Find(&activities).Error
	return activities, err
}

// GetByContactID retrieves activities for a contact
func (r *activityRepository) GetByContactID(ctx context.Context, contactID uuid.UUID, limit int) ([]entities.Activity, error) {
	var activities []entities.Activity
	err := r.db.WithContext(ctx).
		Where("contact_id = ?", contactID).
		Preload("User").
		Order("created_at DESC").
		Limit(limit).
		Find(&activities).Error
	return activities, err
}

// GetByCompanyID retrieves activities for a company
func (r *activityRepository) GetByCompanyID(ctx context.Context, companyID uuid.UUID, limit int) ([]entities.Activity, error) {
	var activities []entities.Activity
	err := r.db.WithContext(ctx).
		Where("company_id = ?", companyID).
		Preload("User").
		Order("created_at DESC").
		Limit(limit).
		Find(&activities).Error
	return activities, err
}

// GetUpcoming retrieves upcoming scheduled activities
func (r *activityRepository) GetUpcoming(ctx context.Context, userID *uuid.UUID, limit int) ([]entities.Activity, error) {
	var activities []entities.Activity
	query := r.db.WithContext(ctx).
		Where("scheduled_at > ? AND completed_at IS NULL", time.Now())

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	err := query.
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("scheduled_at ASC").
		Limit(limit).
		Find(&activities).Error

	return activities, err
}

// GetOverdue retrieves overdue activities
func (r *activityRepository) GetOverdue(ctx context.Context, userID *uuid.UUID, limit int) ([]entities.Activity, error) {
	var activities []entities.Activity
	query := r.db.WithContext(ctx).
		Where("scheduled_at < ? AND completed_at IS NULL", time.Now())

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	err := query.
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("scheduled_at ASC").
		Limit(limit).
		Find(&activities).Error

	return activities, err
}

// ListByCardID lists activities for a card with pagination
func (r *activityRepository) ListByCardID(ctx context.Context, cardID uuid.UUID, limit, offset int) ([]entities.Activity, int64, error) {
	var activities []entities.Activity
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Activity{}).Where("card_id = ?", cardID)
	query.Count(&count)

	err := query.
		Preload("User").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error

	return activities, count, err
}

// ListByContactID lists activities for a contact with pagination
func (r *activityRepository) ListByContactID(ctx context.Context, contactID uuid.UUID, limit, offset int) ([]entities.Activity, int64, error) {
	var activities []entities.Activity
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Activity{}).Where("contact_id = ?", contactID)
	query.Count(&count)

	err := query.
		Preload("User").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error

	return activities, count, err
}

// ListByCompanyID lists activities for a company with pagination
func (r *activityRepository) ListByCompanyID(ctx context.Context, companyID uuid.UUID, limit, offset int) ([]entities.Activity, int64, error) {
	var activities []entities.Activity
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Activity{}).Where("company_id = ?", companyID)
	query.Count(&count)

	err := query.
		Preload("User").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error

	return activities, count, err
}

// ListByUserID lists activities for a user with pagination
func (r *activityRepository) ListByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]entities.Activity, int64, error) {
	var activities []entities.Activity
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Activity{}).Where("user_id = ?", userID)
	query.Count(&count)

	err := query.
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error

	return activities, count, err
}

// GetOverdueActivities retrieves overdue activities with pagination
func (r *activityRepository) GetOverdueActivities(ctx context.Context, userID *uuid.UUID, limit, offset int) ([]entities.Activity, int64, error) {
	var activities []entities.Activity
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Activity{}).
		Where("scheduled_at < ? AND completed_at IS NULL", time.Now())

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	query.Count(&count)

	err := query.
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("scheduled_at ASC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error

	return activities, count, err
}

// GetUpcomingActivities retrieves upcoming activities within specified days
func (r *activityRepository) GetUpcomingActivities(ctx context.Context, userID *uuid.UUID, days int, limit, offset int) ([]entities.Activity, int64, error) {
	var activities []entities.Activity
	var count int64

	endDate := time.Now().AddDate(0, 0, days)
	query := r.db.WithContext(ctx).Model(&entities.Activity{}).
		Where("scheduled_at BETWEEN ? AND ? AND completed_at IS NULL", time.Now(), endDate)

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	query.Count(&count)

	err := query.
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("scheduled_at ASC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error

	return activities, count, err
}

// MarkAsCompleted marks an activity as completed
func (r *activityRepository) MarkAsCompleted(ctx context.Context, id uuid.UUID) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&entities.Activity{}).
		Where("id = ?", id).
		Update("completed_at", &now).Error
}

// GetActivitiesByType retrieves activities by type with pagination
func (r *activityRepository) GetActivitiesByType(ctx context.Context, activityType entities.ActivityType, limit, offset int) ([]entities.Activity, int64, error) {
	var activities []entities.Activity
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Activity{}).Where("type = ?", activityType)
	query.Count(&count)

	err := query.
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error

	return activities, count, err
}