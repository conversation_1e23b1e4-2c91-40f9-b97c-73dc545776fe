package repositories

import (
	"context"
	"fmt"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type commentRepository struct {
	db *gorm.DB
}

func NewCommentRepository(db *gorm.DB) repositories.CommentRepository {
	return &commentRepository{db: db}
}

func (r *commentRepository) Create(ctx context.Context, comment *entities.Comment) error {
	return r.db.WithContext(ctx).Create(comment).Error
}

func (r *commentRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Comment, error) {
	var comment entities.Comment
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Preload("Parent").
		Preload("Replies.User").
		Preload("MentionedUsers").
		Preload("Attachments").
		Preload("EditHistory.EditedBy").
		First(&comment, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &comment, nil
}

func (r *commentRepository) Update(ctx context.Context, comment *entities.Comment) error {
	return r.db.WithContext(ctx).Save(comment).Error
}

func (r *commentRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.Comment{}, "id = ?", id).Error
}

func (r *commentRepository) List(ctx context.Context, filter repositories.CommentFilter) ([]*entities.Comment, error) {
	query := r.buildFilterQuery(r.db.WithContext(ctx), filter)
	
	var comments []*entities.Comment
	err := query.
		Preload("User").
		Preload("MentionedUsers").
		Preload("Attachments").
		Order("created_at DESC").
		Find(&comments).Error
	
	return comments, err
}

func (r *commentRepository) ListWithPagination(ctx context.Context, filter repositories.CommentFilter, limit, offset int) ([]*entities.Comment, int64, error) {
	query := r.buildFilterQuery(r.db.WithContext(ctx), filter)
	
	var total int64
	if err := query.Model(&entities.Comment{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	var comments []*entities.Comment
	err := query.
		Preload("User").
		Preload("MentionedUsers").
		Preload("Attachments").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&comments).Error
	
	return comments, total, err
}

func (r *commentRepository) GetByEntity(ctx context.Context, entityType string, entityID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Where("entity_type = ? AND entity_id = ?", entityType, entityID).
		Preload("User").
		Preload("Replies.User").
		Preload("MentionedUsers").
		Preload("Attachments").
		Order("is_pinned DESC, created_at DESC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) GetByEntityWithReplies(ctx context.Context, entityType string, entityID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Where("entity_type = ? AND entity_id = ? AND parent_id IS NULL", entityType, entityID).
		Preload("User").
		Preload("Replies", func(db *gorm.DB) *gorm.DB {
			return db.Preload("User").Order("created_at ASC")
		}).
		Preload("MentionedUsers").
		Preload("Attachments").
		Order("is_pinned DESC, created_at DESC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) CountByEntity(ctx context.Context, entityType string, entityID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("entity_type = ? AND entity_id = ?", entityType, entityID).
		Count(&count).Error
	return count, err
}

func (r *commentRepository) GetByCardID(ctx context.Context, cardID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Where("card_id = ?", cardID).
		Preload("User").
		Preload("Replies.User").
		Preload("MentionedUsers").
		Preload("Attachments").
		Order("is_pinned DESC, created_at DESC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) GetCardCommentCount(ctx context.Context, cardID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("card_id = ?", cardID).
		Count(&count).Error
	return count, err
}

func (r *commentRepository) GetPinnedCardComments(ctx context.Context, cardID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Where("card_id = ? AND is_pinned = ?", cardID, true).
		Preload("User").
		Order("created_at DESC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) GetByContactID(ctx context.Context, contactID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Where("contact_id = ?", contactID).
		Preload("User").
		Preload("Replies.User").
		Preload("MentionedUsers").
		Preload("Attachments").
		Order("is_pinned DESC, created_at DESC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) GetContactCommentCount(ctx context.Context, contactID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("contact_id = ?", contactID).
		Count(&count).Error
	return count, err
}

func (r *commentRepository) GetByCompanyID(ctx context.Context, companyID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Where("company_id = ?", companyID).
		Preload("User").
		Preload("Replies.User").
		Preload("MentionedUsers").
		Preload("Attachments").
		Order("is_pinned DESC, created_at DESC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) GetCompanyCommentCount(ctx context.Context, companyID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("company_id = ?", companyID).
		Count(&count).Error
	return count, err
}

func (r *commentRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("created_at DESC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) GetUserMentions(ctx context.Context, userID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Joins("JOIN comment_mentions ON comment_mentions.comment_id = comments.id").
		Where("comment_mentions.user_id = ?", userID).
		Preload("User").
		Preload("Card").
		Preload("Contact").
		Preload("Company").
		Order("comments.created_at DESC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) GetReplies(ctx context.Context, parentID uuid.UUID) ([]*entities.Comment, error) {
	var comments []*entities.Comment
	err := r.db.WithContext(ctx).
		Where("parent_id = ?", parentID).
		Preload("User").
		Preload("MentionedUsers").
		Preload("Attachments").
		Order("created_at ASC").
		Find(&comments).Error
	return comments, err
}

func (r *commentRepository) GetThreadDepth(ctx context.Context, commentID uuid.UUID) (int, error) {
	var depth int
	err := r.db.WithContext(ctx).Raw(`
		WITH RECURSIVE comment_tree AS (
			SELECT id, parent_id, 0 as depth
			FROM comments
			WHERE id = ?
			UNION ALL
			SELECT c.id, c.parent_id, ct.depth + 1
			FROM comments c
			JOIN comment_tree ct ON c.parent_id = ct.id
		)
		SELECT MAX(depth) FROM comment_tree
	`, commentID).Scan(&depth).Error
	return depth, err
}

func (r *commentRepository) PinComment(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("id = ?", id).
		Update("is_pinned", true).Error
}

func (r *commentRepository) UnpinComment(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("id = ?", id).
		Update("is_pinned", false).Error
}

func (r *commentRepository) AddReaction(ctx context.Context, id uuid.UUID, userID string, reaction string) error {
	comment, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}
	
	comment.AddReaction(userID, reaction)
	return r.Update(ctx, comment)
}

func (r *commentRepository) RemoveReaction(ctx context.Context, id uuid.UUID, userID string, reaction string) error {
	comment, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}
	
	comment.RemoveReaction(userID, reaction)
	return r.Update(ctx, comment)
}

func (r *commentRepository) CreateEditHistory(ctx context.Context, edit *entities.CommentEdit) error {
	return r.db.WithContext(ctx).Create(edit).Error
}

func (r *commentRepository) GetEditHistory(ctx context.Context, commentID uuid.UUID) ([]*entities.CommentEdit, error) {
	var edits []*entities.CommentEdit
	err := r.db.WithContext(ctx).
		Where("comment_id = ?", commentID).
		Preload("EditedBy").
		Order("created_at DESC").
		Find(&edits).Error
	return edits, err
}

func (r *commentRepository) CreateBatch(ctx context.Context, comments []*entities.Comment) error {
	return r.db.WithContext(ctx).
		Clauses(clause.OnConflict{DoNothing: true}).
		Create(&comments).Error
}

func (r *commentRepository) DeleteByEntity(ctx context.Context, entityType string, entityID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Where("entity_type = ? AND entity_id = ?", entityType, entityID).
		Delete(&entities.Comment{}).Error
}

func (r *commentRepository) buildFilterQuery(db *gorm.DB, filter repositories.CommentFilter) *gorm.DB {
	query := db.Model(&entities.Comment{})
	
	if filter.EntityType != nil {
		query = query.Where("entity_type = ?", *filter.EntityType)
	}
	if filter.EntityID != nil {
		query = query.Where("entity_id = ?", *filter.EntityID)
	}
	if filter.CardID != nil {
		query = query.Where("card_id = ?", *filter.CardID)
	}
	if filter.ContactID != nil {
		query = query.Where("contact_id = ?", *filter.ContactID)
	}
	if filter.CompanyID != nil {
		query = query.Where("company_id = ?", *filter.CompanyID)
	}
	if filter.UserID != nil {
		query = query.Where("user_id = ?", *filter.UserID)
	}
	if filter.IsInternal != nil {
		query = query.Where("is_internal = ?", *filter.IsInternal)
	}
	if filter.IsPinned != nil {
		query = query.Where("is_pinned = ?", *filter.IsPinned)
	}
	if filter.ParentID != nil {
		query = query.Where("parent_id = ?", *filter.ParentID)
	}
	if filter.HasReplies != nil {
		if *filter.HasReplies {
			query = query.Where("id IN (SELECT DISTINCT parent_id FROM comments WHERE parent_id IS NOT NULL)")
		} else {
			query = query.Where("id NOT IN (SELECT DISTINCT parent_id FROM comments WHERE parent_id IS NOT NULL)")
		}
	}
	if filter.SearchTerm != "" {
		query = query.Where("content ILIKE ?", fmt.Sprintf("%%%s%%", filter.SearchTerm))
	}
	
	return query
}