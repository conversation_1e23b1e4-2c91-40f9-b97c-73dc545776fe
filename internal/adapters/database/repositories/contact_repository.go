package repositories

import (
	"context"
	"fmt"
	"strings"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// contactRepository implements the ContactRepository interface
type contactRepository struct {
	db *gorm.DB
}

// NewContactRepository creates a new contact repository
func NewContactRepository(db *gorm.DB) repositories.ContactRepository {
	return &contactRepository{db: db}
}

// Create creates a new contact
func (r *contactRepository) Create(ctx context.Context, contact *entities.Contact) error {
	return r.db.WithContext(ctx).Create(contact).Error
}

// GetByID retrieves a contact by ID
func (r *contactRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Contact, error) {
	var contact entities.Contact
	err := r.db.WithContext(ctx).First(&contact, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &contact, nil
}

// GetByIDWithRelations retrieves a contact by ID with all relations
func (r *contactRepository) GetByIDWithRelations(ctx context.Context, id uuid.UUID) (*entities.Contact, error) {
	var contact entities.Contact
	err := r.db.WithContext(ctx).
		Preload("Company").
		Preload("Cards").
		Preload("Activities").
		First(&contact, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &contact, nil
}

// GetByEmail retrieves a contact by email
func (r *contactRepository) GetByEmail(ctx context.Context, email string) (*entities.Contact, error) {
	var contact entities.Contact
	err := r.db.WithContext(ctx).First(&contact, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	return &contact, nil
}

// Update updates an existing contact
func (r *contactRepository) Update(ctx context.Context, contact *entities.Contact) error {
	return r.db.WithContext(ctx).Save(contact).Error
}

// UpdateCustomFields updates only custom fields of a contact using JSONB partial updates
func (r *contactRepository) UpdateCustomFields(ctx context.Context, id uuid.UUID, fields entities.CustomFields) error {
	// Use JSONB || operator for efficient partial updates in PostgreSQL
	return r.db.WithContext(ctx).
		Model(&entities.Contact{}).
		Where("id = ?", id).
		Update("custom_fields", gorm.Expr("custom_fields || ?", fields)).Error
}

// Delete soft deletes a contact
func (r *contactRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.Contact{}, "id = ?", id).Error
}

// List lists contacts with filtering and pagination
func (r *contactRepository) List(ctx context.Context, filters repositories.ContactFilters, limit, offset int) ([]entities.Contact, int64, error) {
	var contacts []entities.Contact
	var total int64

	// Debug logging
	fmt.Printf("DEBUG: List called with filters - Search: '%s', Email: '%s'\n", filters.Search, filters.Email)

	// Create base query with Session to ensure independent instances
	baseQuery := r.db.Session(&gorm.Session{}).WithContext(ctx).Model(&entities.Contact{})
	
	// Apply filters to base query
	baseQuery = r.applyContactFilters(baseQuery, filters)

	// Clone query for counting using Session to ensure true independence
	countQuery := r.db.Session(&gorm.Session{}).WithContext(ctx).Model(&entities.Contact{})
	countQuery = r.applyContactFilters(countQuery, filters)
	
	// Get total count
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	fmt.Printf("DEBUG: Total count after filters: %d\n", total)

	// Get contacts with preloaded company using the filtered query
	err := baseQuery.
		Preload("Company").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&contacts).Error

	fmt.Printf("DEBUG: Found %d contacts\n", len(contacts))

	return contacts, total, err
}

// ListByCompanyID lists contacts for a specific company
func (r *contactRepository) ListByCompanyID(ctx context.Context, companyID uuid.UUID, limit, offset int) ([]entities.Contact, int64, error) {
	var contacts []entities.Contact
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Contact{}).Where("company_id = ?", companyID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get contacts
	err := query.
		Preload("Company").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&contacts).Error

	return contacts, total, err
}

// Search searches contacts by query across multiple fields
// Optimized version using full-text search with GIN index
func (r *contactRepository) Search(ctx context.Context, query string, limit, offset int) ([]entities.Contact, int64, error) {
	var contacts []entities.Contact
	var total int64

	// First try full-text search if search_vector column exists
	var hasSearchVector bool
	r.db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'contacts' AND column_name = 'search_vector')").Scan(&hasSearchVector)
	
	if hasSearchVector && query != "" {
		// Use full-text search for better performance
		return r.FullTextSearch(ctx, query, limit, offset)
	}
	
	// Fallback to LIKE queries (slower but works without search_vector)
	searchQuery := "%" + strings.ToLower(query) + "%"
	
	dbQuery := r.db.WithContext(ctx).Model(&entities.Contact{}).Where(
		"LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ? OR LOWER(email) LIKE ? OR LOWER(phone) LIKE ? OR LOWER(job_title) LIKE ?",
		searchQuery, searchQuery, searchQuery, searchQuery, searchQuery,
	)

	// Get total count
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get contacts with selective preloading
	err := dbQuery.
		Preload("Company", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name")
		}).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&contacts).Error

	return contacts, total, err
}

// FullTextSearch performs optimized full-text search using PostgreSQL's text search capabilities
func (r *contactRepository) FullTextSearch(ctx context.Context, query string, limit, offset int) ([]entities.Contact, int64, error) {
	var contacts []entities.Contact
	var total int64

	// Prepare the search query for PostgreSQL full-text search
	tsQuery := strings.ReplaceAll(query, " ", " & ")
	
	// Count query using full-text search
	countQuery := `
		SELECT COUNT(*) 
		FROM contacts 
		WHERE deleted_at IS NULL 
		AND search_vector @@ plainto_tsquery('english', ?)
	`
	if err := r.db.Raw(countQuery, tsQuery).Scan(&total).Error; err != nil {
		return nil, 0, err
	}

	// Main search query with ranking
	searchQuerySQL := `
		SELECT 
			c.*,
			ts_rank(c.search_vector, plainto_tsquery('english', ?)) as rank
		FROM contacts c
		WHERE c.deleted_at IS NULL 
		AND c.search_vector @@ plainto_tsquery('english', ?)
		ORDER BY rank DESC, c.created_at DESC
		LIMIT ? OFFSET ?
	`
	
	if err := r.db.Raw(searchQuerySQL, tsQuery, tsQuery, limit, offset).Scan(&contacts).Error; err != nil {
		return nil, 0, err
	}

	// Load company relations for the found contacts
	if len(contacts) > 0 {
		contactIDs := make([]uuid.UUID, len(contacts))
		for i, contact := range contacts {
			contactIDs[i] = contact.ID
		}
		
		err := r.db.WithContext(ctx).
			Preload("Company", func(db *gorm.DB) *gorm.DB {
				return db.Select("id", "name")
			}).
			Where("id IN ?", contactIDs).
			Find(&contacts).Error
		if err != nil {
			return nil, 0, err
		}
	}

	return contacts, total, nil
}

// SearchByCustomFields searches contacts by custom fields using optimized JSONB queries with GIN index
func (r *contactRepository) SearchByCustomFields(ctx context.Context, searchQuery map[string]interface{}, limit, offset int) ([]entities.Contact, int64, error) {
	var contacts []entities.Contact
	var total int64

	query := r.db.WithContext(ctx).Model(&entities.Contact{})

	// Use @> operator for better GIN index utilization
	if len(searchQuery) > 0 {
		// Build a single JSONB containment query for better performance
		query = query.Where("custom_fields @> ?", searchQuery)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get contacts
	err := query.
		Preload("Company").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&contacts).Error

	return contacts, total, err
}

// applyContactFilters applies filters to the query
func (r *contactRepository) applyContactFilters(query *gorm.DB, filters repositories.ContactFilters) *gorm.DB {
	if filters.CompanyID != nil {
		query = query.Where("company_id = ?", *filters.CompanyID)
	}

	if filters.Email != "" {
		query = query.Where("email = ?", filters.Email)
	}

	if filters.Search != "" {
		searchQuery := "%" + strings.ToLower(filters.Search) + "%"
		// Debug logging
		fmt.Printf("DEBUG: Applying search filter with query: %s\n", filters.Search)
		query = query.Where(
			"LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ? OR LOWER(email) LIKE ? OR LOWER(phone) LIKE ? OR LOWER(job_title) LIKE ?",
			searchQuery, searchQuery, searchQuery, searchQuery, searchQuery,
		)
	}

	// Apply custom fields filters using JSONB queries
	for key, value := range filters.CustomFields {
		switch v := value.(type) {
		case string:
			query = query.Where("custom_fields ->> ? ILIKE ?", key, fmt.Sprintf("%%%s%%", v))
		default:
			query = query.Where("custom_fields ->> ? = ?", key, fmt.Sprintf("%v", v))
		}
	}

	return query
}