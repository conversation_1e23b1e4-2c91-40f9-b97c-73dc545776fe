package handlers

import (
	"strconv"

	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/domain/repositories"
	"crm-backend/internal/usecases"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// ContactHandler handles contact endpoints
type ContactHandler struct {
	contactUsecase *usecases.ContactUsecase
	validator      *validator.Validate
}

// NewContactHandler creates a new contact handler
func NewContactHandler(contactUsecase *usecases.ContactUsecase) *ContactHandler {
	return &ContactHandler{
		contactUsecase: contactUsecase,
		validator:      validator.New(),
	}
}

// CreateContact handles contact creation
// @Summary Create contact
// @Description Create a new contact
// @Tags contacts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param contactRequest body usecases.CreateContactRequest true "Contact data"
// @Success 201 {object} entities.Contact
// @Failure 400 {object} ErrorResponse
// @Router /contacts [post]
func (h *ContactHandler) CreateContact(c *fiber.Ctx) error {
	var req usecases.CreateContactRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	contact, err := h.contactUsecase.CreateContact(c.Context(), &req, userID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "Contact created successfully",
		Data:    contact,
	})
}

// GetContact handles getting a specific contact
// @Summary Get contact
// @Description Get a contact by ID with all relations
// @Tags contacts
// @Security BearerAuth
// @Param id path string true "Contact ID"
// @Produce json
// @Success 200 {object} entities.Contact
// @Failure 404 {object} ErrorResponse
// @Router /contacts/{id} [get]
func (h *ContactHandler) GetContact(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid contact ID",
		})
	}

	contact, err := h.contactUsecase.GetContact(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "Contact not found",
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Contact retrieved successfully",
		Data:    contact,
	})
}

// UpdateContact handles contact updates
// @Summary Update contact
// @Description Update an existing contact
// @Tags contacts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Contact ID"
// @Param contactRequest body usecases.UpdateContactRequest true "Contact update data"
// @Success 200 {object} entities.Contact
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /contacts/{id} [put]
func (h *ContactHandler) UpdateContact(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid contact ID",
		})
	}

	var req usecases.UpdateContactRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	contact, err := h.contactUsecase.UpdateContact(c.Context(), id, &req, userID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Contact updated successfully",
		Data:    contact,
	})
}

// DeleteContact handles contact deletion
// @Summary Delete contact
// @Description Delete a contact by ID
// @Tags contacts
// @Security BearerAuth
// @Param id path string true "Contact ID"
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /contacts/{id} [delete]
func (h *ContactHandler) DeleteContact(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid contact ID",
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	if err := h.contactUsecase.DeleteContact(c.Context(), id, userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Contact deleted successfully",
	})
}

// ListContacts handles listing contacts with filters and pagination
// @Summary List contacts
// @Description Get list of contacts with optional filtering and pagination
// @Tags contacts
// @Security BearerAuth
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search in contact fields"
// @Param company_id query string false "Filter by company ID"
// @Param email query string false "Filter by email"
// @Success 200 {object} PaginatedResponse
// @Failure 400 {object} ErrorResponse
// @Router /contacts [get]
func (h *ContactHandler) ListContacts(c *fiber.Ctx) error {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "20"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// Parse filters - support both 'q' and 'search' parameters
	searchQuery := c.Query("q")
	if searchQuery == "" {
		searchQuery = c.Query("search")
	}
	
	filters := repositories.ContactFilters{
		Search: searchQuery,
		Email:  c.Query("email"),
	}

	// Parse company_id if provided
	if companyIDStr := c.Query("company_id"); companyIDStr != "" {
		if companyID, err := uuid.Parse(companyIDStr); err == nil {
			filters.CompanyID = &companyID
		}
	}

	// Parse custom fields filters
	customFields := make(map[string]interface{})
	for key, values := range c.Queries() {
		// Exclude standard parameters from custom fields
		if key != "page" && key != "limit" && key != "q" && key != "search" && key != "company_id" && key != "email" && key != "sort" && key != "order" {
			if len(values) == 1 {
				customFields[key] = values[0]
			} else {
				customFields[key] = values
			}
		}
	}
	filters.CustomFields = customFields

	contacts, total, err := h.contactUsecase.ListContacts(c.Context(), filters, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	meta := calculateMetaData(total, limit, offset)

	return c.JSON(PaginatedResponse{
		Message: "Contacts retrieved successfully",
		Data:    contacts,
		Meta:    meta,
	})
}

// SearchContacts handles searching contacts
// @Summary Search contacts
// @Description Search contacts by query string
// @Tags contacts
// @Security BearerAuth
// @Produce json
// @Param q query string true "Search query"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} PaginatedResponse
// @Failure 400 {object} ErrorResponse
// @Router /contacts/search [get]
func (h *ContactHandler) SearchContacts(c *fiber.Ctx) error {
	query := c.Query("q")
	if query == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Search query is required",
		})
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "20"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	contacts, total, err := h.contactUsecase.SearchContacts(c.Context(), query, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	meta := calculateMetaData(total, limit, offset)

	return c.JSON(PaginatedResponse{
		Message: "Contacts search completed successfully",
		Data:    contacts,
		Meta:    meta,
	})
}

// UpdateCustomFields handles updating custom fields only
// @Summary Update contact custom fields
// @Description Update custom fields of a contact
// @Tags contacts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Contact ID"
// @Param customFields body map[string]interface{} true "Custom fields data"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /contacts/{id}/custom-fields [patch]
func (h *ContactHandler) UpdateCustomFields(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid contact ID",
		})
	}

	var customFields map[string]interface{}
	if err := c.BodyParser(&customFields); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	if err := h.contactUsecase.UpdateCustomFields(c.Context(), id, customFields, userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Contact custom fields updated successfully",
	})
}