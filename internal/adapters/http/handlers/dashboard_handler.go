package handlers

import (
	"context"
	"encoding/json"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/infrastructure/redis"
	"crm-backend/internal/usecases"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// DashboardHandler handles dashboard-related HTTP requests
type DashboardHandler struct {
	cardUsecase     *usecases.CardUsecase
	pipelineUsecase *usecases.PipelineUsecase
	db              *gorm.DB
	redisClient     *redis.Client
}

// NewDashboardHandler creates a new dashboard handler
func NewDashboardHandler(
	cardUsecase *usecases.CardUsecase,
	pipelineUsecase *usecases.PipelineUsecase,
	db *gorm.DB,
	redisClient *redis.Client,
) *DashboardHandler {
	return &DashboardHandler{
		cardUsecase:     cardUsecase,
		pipelineUsecase: pipelineUsecase,
		db:              db,
		redisClient:     redisClient,
	}
}

// DashboardStats represents dashboard statistics
type DashboardStats struct {
	TotalCards       int64               `json:"total_cards"`
	ActiveCards      int64               `json:"active_cards"`
	ClosedWonCards   int64               `json:"closed_won_cards"`
	ClosedLostCards  int64               `json:"closed_lost_cards"`
	OverdueCards     int64               `json:"overdue_cards"`
	TotalRevenue     float64             `json:"total_revenue"`
	ExpectedRevenue  float64             `json:"expected_revenue"`
	ConversionRate   float64             `json:"conversion_rate"`
	AvgDealSize      float64             `json:"avg_deal_size"`
	TotalPipelines   int64               `json:"total_pipelines"`
	TotalStages      int64               `json:"total_stages"`
	CardsByStage     []StageCardCount    `json:"cards_by_stage"`
	CardsByPipeline  []PipelineCardCount `json:"cards_by_pipeline"`
	RecentActivities []ActivitySummary   `json:"recent_activities"`
	TopPerformers    []UserPerformance   `json:"top_performers"`
	RevenueByMonth   []MonthlyRevenue    `json:"revenue_by_month"`
	ConversionFunnel []ConversionStep    `json:"conversion_funnel"`
}

type StageCardCount struct {
	StageID    string  `json:"stage_id"`
	StageName  string  `json:"stage_name"`
	CardCount  int64   `json:"card_count"`
	TotalValue float64 `json:"total_value"`
}

type PipelineCardCount struct {
	PipelineID   string  `json:"pipeline_id"`
	PipelineName string  `json:"pipeline_name"`
	CardCount    int64   `json:"card_count"`
	TotalValue   float64 `json:"total_value"`
}

type ActivitySummary struct {
	Type        string    `json:"type"`
	Count       int64     `json:"count"`
	LastUpdated time.Time `json:"last_updated"`
}

type UserPerformance struct {
	UserID       string  `json:"user_id"`
	UserName     string  `json:"user_name"`
	CardsCount   int64   `json:"cards_count"`
	TotalRevenue float64 `json:"total_revenue"`
	WinRate      float64 `json:"win_rate"`
}

type MonthlyRevenue struct {
	Month   string  `json:"month"`
	Revenue float64 `json:"revenue"`
	Count   int64   `json:"count"`
}

type ConversionStep struct {
	StageName string  `json:"stage_name"`
	CardCount int64   `json:"card_count"`
	DropRate  float64 `json:"drop_rate"`
}

// GetDashboardStats handles GET /api/dashboard/stats
// Optimized version with single query and Redis caching
func (h *DashboardHandler) GetDashboardStats(c *fiber.Ctx) error {
	ctx := c.Context()

	// Try to get from Redis cache first
	cacheKey := "dashboard:stats"
	if h.redisClient != nil {
		cachedData, err := h.redisClient.Get(cacheKey)
		if err == nil && cachedData != "" {
			var stats DashboardStats
			if err := json.Unmarshal([]byte(cachedData), &stats); err == nil {
				return c.JSON(SuccessResponse{Data: stats})
			}
		}
	}

	// If not in cache or cache miss, fetch from database
	stats, err := h.getOptimizedDashboardStats(ctx)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to get dashboard statistics: " + err.Error(),
		})
	}

	// Cache the result for 5 minutes
	if h.redisClient != nil {
		statsJSON, _ := json.Marshal(stats)
		_ = h.redisClient.Set(cacheKey, string(statsJSON), 5*time.Minute)
	}

	return c.JSON(SuccessResponse{
		Data: stats,
	})
}

// getOptimizedDashboardStats fetches all dashboard stats in a single optimized query
func (h *DashboardHandler) getOptimizedDashboardStats(ctx context.Context) (*DashboardStats, error) {
	stats := &DashboardStats{}

	// Single CTE query to get all card statistics
	type CardStatistics struct {
		TotalCards      int64   `json:"total_cards"`
		ActiveCards     int64   `json:"active_cards"`
		ClosedWonCards  int64   `json:"closed_won_cards"`
		ClosedLostCards int64   `json:"closed_lost_cards"`
		OverdueCards    int64   `json:"overdue_cards"`
		TotalRevenue    float64 `json:"total_revenue"`
		ExpectedRevenue float64 `json:"expected_revenue"`
	}

	var cardStats CardStatistics
	cardStatsQuery := `
		WITH card_stats AS (
			SELECT 
				COUNT(*) as total_cards,
				COUNT(*) FILTER (WHERE c.deleted_at IS NULL) as active_cards,
				COUNT(*) FILTER (WHERE s.is_closed_won = true) as closed_won_cards,
				COUNT(*) FILTER (WHERE s.is_closed_lost = true) as closed_lost_cards,
				COUNT(*) FILTER (WHERE c.expected_close_date < NOW() AND c.deleted_at IS NULL AND s.is_closed_won = false AND s.is_closed_lost = false) as overdue_cards,
				COALESCE(SUM(c.value) FILTER (WHERE s.is_closed_won = true), 0) as total_revenue,
				COALESCE(SUM(c.value * s.probability / 100.0) FILTER (WHERE s.is_closed_won = false AND s.is_closed_lost = false AND c.deleted_at IS NULL), 0) as expected_revenue
			FROM cards c
			LEFT JOIN stages s ON c.stage_id = s.id
		)
		SELECT * FROM card_stats
	`

	if err := h.db.Raw(cardStatsQuery).Scan(&cardStats).Error; err != nil {
		return nil, err
	}

	stats.TotalCards = cardStats.TotalCards
	stats.ActiveCards = cardStats.ActiveCards
	stats.ClosedWonCards = cardStats.ClosedWonCards
	stats.ClosedLostCards = cardStats.ClosedLostCards
	stats.OverdueCards = cardStats.OverdueCards
	stats.TotalRevenue = cardStats.TotalRevenue
	stats.ExpectedRevenue = cardStats.ExpectedRevenue

	// Calculate derived metrics
	if stats.TotalCards > 0 {
		stats.ConversionRate = float64(stats.ClosedWonCards) / float64(stats.TotalCards) * 100
	}
	if stats.ClosedWonCards > 0 {
		stats.AvgDealSize = stats.TotalRevenue / float64(stats.ClosedWonCards)
	}

	// Get pipeline and stage counts in single query
	pipelineStatsQuery := `
		SELECT 
			(SELECT COUNT(*) FROM pipelines WHERE is_active = true) as total_pipelines,
			(SELECT COUNT(*) FROM stages WHERE is_active = true) as total_stages
	`
	var pipelineStats struct {
		TotalPipelines int64 `json:"total_pipelines"`
		TotalStages    int64 `json:"total_stages"`
	}
	if err := h.db.Raw(pipelineStatsQuery).Scan(&pipelineStats).Error; err != nil {
		return nil, err
	}
	stats.TotalPipelines = pipelineStats.TotalPipelines
	stats.TotalStages = pipelineStats.TotalStages

	// Get cards by stage with single query
	if err := h.getCardsByStageOptimized(ctx, stats); err != nil {
		return nil, err
	}

	// Get other statistics using parallel queries
	errs := make(chan error, 5)
	go func() { errs <- h.getCardsByPipelineOptimized(ctx, stats) }()
	go func() { errs <- h.getRecentActivitiesOptimized(ctx, stats) }()
	go func() { errs <- h.getTopPerformersOptimized(ctx, stats) }()
	go func() { errs <- h.getRevenueByMonthOptimized(ctx, stats) }()
	go func() { errs <- h.getConversionFunnelOptimized(ctx, stats) }()

	// Wait for all queries to complete
	for i := 0; i < 5; i++ {
		if err := <-errs; err != nil {
			return nil, err
		}
	}

	return stats, nil
}

func (h *DashboardHandler) getCardStatistics(ctx interface{}, stats *DashboardStats) error {
	// Total cards
	if err := h.db.Model(&entities.Card{}).Count(&stats.TotalCards).Error; err != nil {
		return err
	}

	// Active cards (not archived)
	if err := h.db.Model(&entities.Card{}).Where("cards.deleted_at IS NULL").Count(&stats.ActiveCards).Error; err != nil {
		return err
	}

	// Closed won cards
	if err := h.db.Model(&entities.Card{}).
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("stages.is_closed_won = ?", true).
		Count(&stats.ClosedWonCards).Error; err != nil {
		return err
	}

	// Closed lost cards
	if err := h.db.Model(&entities.Card{}).
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("stages.is_closed_lost = ?", true).
		Count(&stats.ClosedLostCards).Error; err != nil {
		return err
	}

	// Overdue cards
	if err := h.db.Model(&entities.Card{}).
		Where("cards.expected_close_date < ? AND cards.deleted_at IS NULL", time.Now()).
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("stages.is_closed_won = ? AND stages.is_closed_lost = ?", false, false).
		Count(&stats.OverdueCards).Error; err != nil {
		return err
	}

	// Total revenue (from closed won deals)
	var totalRevenue *float64
	if err := h.db.Model(&entities.Card{}).
		Select("COALESCE(SUM(value), 0) as total").
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("stages.is_closed_won = ?", true).
		Scan(&totalRevenue).Error; err != nil {
		return err
	}
	if totalRevenue != nil {
		stats.TotalRevenue = *totalRevenue
	}

	// Expected revenue (from active deals)
	var expectedRevenue *float64
	if err := h.db.Model(&entities.Card{}).
		Select("COALESCE(SUM(cards.value * stages.probability / 100.0), 0) as expected").
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("stages.is_closed_won = ? AND stages.is_closed_lost = ? AND cards.deleted_at IS NULL", false, false).
		Scan(&expectedRevenue).Error; err != nil {
		return err
	}
	if expectedRevenue != nil {
		stats.ExpectedRevenue = *expectedRevenue
	}

	// Calculate conversion rate
	if stats.TotalCards > 0 {
		stats.ConversionRate = float64(stats.ClosedWonCards) / float64(stats.TotalCards) * 100
	}

	// Calculate average deal size
	if stats.ClosedWonCards > 0 {
		stats.AvgDealSize = stats.TotalRevenue / float64(stats.ClosedWonCards)
	}

	return nil
}

func (h *DashboardHandler) getPipelineStatistics(ctx interface{}, stats *DashboardStats) error {
	// Total pipelines
	if err := h.db.Model(&entities.Pipeline{}).Where("is_active = ?", true).Count(&stats.TotalPipelines).Error; err != nil {
		return err
	}

	// Total stages
	if err := h.db.Model(&entities.Stage{}).Where("is_active = ?", true).Count(&stats.TotalStages).Error; err != nil {
		return err
	}

	return nil
}

func (h *DashboardHandler) getCardsByStageOptimized(ctx context.Context, stats *DashboardStats) error {
	var results []struct {
		StageID    string  `json:"stage_id"`
		StageName  string  `json:"stage_name"`
		CardCount  int64   `json:"card_count"`
		TotalValue float64 `json:"total_value"`
	}

	query := `
		SELECT 
			s.id as stage_id,
			s.name as stage_name,
			COUNT(c.id) as card_count,
			COALESCE(SUM(c.value), 0) as total_value
		FROM stages s
		LEFT JOIN cards c ON c.stage_id = s.id AND c.deleted_at IS NULL
		WHERE s.is_active = true
		GROUP BY s.id, s.name, s.sort_order
		ORDER BY s.sort_order ASC
	`

	if err := h.db.Raw(query).Scan(&results).Error; err != nil {
		return err
	}

	stats.CardsByStage = make([]StageCardCount, len(results))
	for i, result := range results {
		stats.CardsByStage[i] = StageCardCount{
			StageID:    result.StageID,
			StageName:  result.StageName,
			CardCount:  result.CardCount,
			TotalValue: result.TotalValue,
		}
	}

	return nil
}

func (h *DashboardHandler) getCardsByStage(ctx interface{}, stats *DashboardStats) error {
	var results []struct {
		StageID    string  `json:"stage_id"`
		StageName  string  `json:"stage_name"`
		CardCount  int64   `json:"card_count"`
		TotalValue float64 `json:"total_value"`
	}

	if err := h.db.Model(&entities.Card{}).
		Select("stages.id as stage_id, stages.name as stage_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value").
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("cards.deleted_at IS NULL AND stages.is_active = ?", true).
		Group("stages.id, stages.name").
		Order("stages.sort_order ASC").
		Scan(&results).Error; err != nil {
		return err
	}

	stats.CardsByStage = make([]StageCardCount, len(results))
	for i, result := range results {
		stats.CardsByStage[i] = StageCardCount{
			StageID:    result.StageID,
			StageName:  result.StageName,
			CardCount:  result.CardCount,
			TotalValue: result.TotalValue,
		}
	}

	return nil
}

func (h *DashboardHandler) getCardsByPipelineOptimized(ctx context.Context, stats *DashboardStats) error {
	var results []struct {
		PipelineID   string  `json:"pipeline_id"`
		PipelineName string  `json:"pipeline_name"`
		CardCount    int64   `json:"card_count"`
		TotalValue   float64 `json:"total_value"`
	}

	query := `
		SELECT 
			p.id as pipeline_id,
			p.name as pipeline_name,
			COUNT(DISTINCT c.id) as card_count,
			COALESCE(SUM(c.value), 0) as total_value
		FROM pipelines p
		LEFT JOIN stages s ON s.pipeline_id = p.id AND s.is_active = true
		LEFT JOIN cards c ON c.stage_id = s.id AND c.deleted_at IS NULL
		WHERE p.is_active = true
		GROUP BY p.id, p.name, p.sort_order
		ORDER BY p.sort_order ASC
	`

	if err := h.db.Raw(query).Scan(&results).Error; err != nil {
		return err
	}

	stats.CardsByPipeline = make([]PipelineCardCount, len(results))
	for i, result := range results {
		stats.CardsByPipeline[i] = PipelineCardCount{
			PipelineID:   result.PipelineID,
			PipelineName: result.PipelineName,
			CardCount:    result.CardCount,
			TotalValue:   result.TotalValue,
		}
	}

	return nil
}

func (h *DashboardHandler) getCardsByPipeline(ctx interface{}, stats *DashboardStats) error {
	var results []struct {
		PipelineID   string  `json:"pipeline_id"`
		PipelineName string  `json:"pipeline_name"`
		CardCount    int64   `json:"card_count"`
		TotalValue   float64 `json:"total_value"`
	}

	if err := h.db.Model(&entities.Card{}).
		Select("pipelines.id as pipeline_id, pipelines.name as pipeline_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value").
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Joins("JOIN pipelines ON stages.pipeline_id = pipelines.id").
		Where("cards.deleted_at IS NULL AND pipelines.is_active = ?", true).
		Group("pipelines.id, pipelines.name").
		Order("pipelines.sort_order ASC").
		Scan(&results).Error; err != nil {
		return err
	}

	stats.CardsByPipeline = make([]PipelineCardCount, len(results))
	for i, result := range results {
		stats.CardsByPipeline[i] = PipelineCardCount{
			PipelineID:   result.PipelineID,
			PipelineName: result.PipelineName,
			CardCount:    result.CardCount,
			TotalValue:   result.TotalValue,
		}
	}

	return nil
}

func (h *DashboardHandler) getRecentActivitiesOptimized(ctx context.Context, stats *DashboardStats) error {
	var results []struct {
		Type        string    `json:"type"`
		Count       int64     `json:"count"`
		LastUpdated time.Time `json:"last_updated"`
	}

	// Use index on (type, created_at)
	query := `
		SELECT 
			type,
			COUNT(*) as count,
			MAX(created_at) as last_updated
		FROM activities
		WHERE created_at >= NOW() - INTERVAL '30 days'
		GROUP BY type
		ORDER BY count DESC
		LIMIT 10
	`

	if err := h.db.Raw(query).Scan(&results).Error; err != nil {
		return err
	}

	stats.RecentActivities = make([]ActivitySummary, len(results))
	for i, result := range results {
		stats.RecentActivities[i] = ActivitySummary{
			Type:        result.Type,
			Count:       result.Count,
			LastUpdated: result.LastUpdated,
		}
	}

	return nil
}

func (h *DashboardHandler) getRecentActivities(ctx interface{}, stats *DashboardStats) error {
	var results []struct {
		Type        string    `json:"type"`
		Count       int64     `json:"count"`
		LastUpdated time.Time `json:"last_updated"`
	}

	if err := h.db.Model(&entities.Activity{}).
		Select("type, COUNT(*) as count, MAX(created_at) as last_updated").
		Where("created_at >= ?", time.Now().AddDate(0, 0, -30)). // Last 30 days
		Group("type").
		Order("count DESC").
		Limit(10).
		Scan(&results).Error; err != nil {
		return err
	}

	stats.RecentActivities = make([]ActivitySummary, len(results))
	for i, result := range results {
		stats.RecentActivities[i] = ActivitySummary{
			Type:        result.Type,
			Count:       result.Count,
			LastUpdated: result.LastUpdated,
		}
	}

	return nil
}

func (h *DashboardHandler) getTopPerformersOptimized(ctx context.Context, stats *DashboardStats) error {
	var results []struct {
		UserID       string  `json:"user_id"`
		FirstName    string  `json:"first_name"`
		LastName     string  `json:"last_name"`
		CardsCount   int64   `json:"cards_count"`
		TotalRevenue float64 `json:"total_revenue"`
		ClosedWon    int64   `json:"closed_won"`
	}

	query := `
		WITH user_performance AS (
			SELECT 
				u.id as user_id,
				u.first_name,
				u.last_name,
				COUNT(c.id) as cards_count,
				COALESCE(SUM(CASE WHEN s.is_closed_won THEN c.value ELSE 0 END), 0) as total_revenue,
				COUNT(CASE WHEN s.is_closed_won THEN 1 END) as closed_won
			FROM users u
			INNER JOIN cards c ON c.assigned_to_id = u.id
			INNER JOIN stages s ON c.stage_id = s.id
			WHERE c.created_at >= NOW() - INTERVAL '3 months'
			GROUP BY u.id, u.first_name, u.last_name
		)
		SELECT * FROM user_performance
		ORDER BY total_revenue DESC
		LIMIT 5
	`

	if err := h.db.Raw(query).Scan(&results).Error; err != nil {
		return err
	}

	stats.TopPerformers = make([]UserPerformance, len(results))
	for i, result := range results {
		winRate := 0.0
		if result.CardsCount > 0 {
			winRate = float64(result.ClosedWon) / float64(result.CardsCount) * 100
		}

		stats.TopPerformers[i] = UserPerformance{
			UserID:       result.UserID,
			UserName:     result.FirstName + " " + result.LastName,
			CardsCount:   result.CardsCount,
			TotalRevenue: result.TotalRevenue,
			WinRate:      winRate,
		}
	}

	return nil
}

func (h *DashboardHandler) getTopPerformers(ctx interface{}, stats *DashboardStats) error {
	var results []struct {
		UserID       string  `json:"user_id"`
		FirstName    string  `json:"first_name"`
		LastName     string  `json:"last_name"`
		CardsCount   int64   `json:"cards_count"`
		TotalRevenue float64 `json:"total_revenue"`
		ClosedWon    int64   `json:"closed_won"`
	}

	if err := h.db.Model(&entities.Card{}).
		Select(`
			users.id as user_id,
			users.first_name,
			users.last_name,
			COUNT(cards.id) as cards_count,
			COALESCE(SUM(CASE WHEN stages.is_closed_won THEN cards.value ELSE 0 END), 0) as total_revenue,
			COUNT(CASE WHEN stages.is_closed_won THEN 1 END) as closed_won
		`).
		Joins("LEFT JOIN users ON cards.assigned_to_id = users.id").
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("cards.created_at >= ? AND users.id IS NOT NULL", time.Now().AddDate(0, -3, 0)). // Last 3 months
		Group("users.id, users.first_name, users.last_name").
		Order("total_revenue DESC").
		Limit(5).
		Scan(&results).Error; err != nil {
		return err
	}

	stats.TopPerformers = make([]UserPerformance, len(results))
	for i, result := range results {
		winRate := 0.0
		if result.CardsCount > 0 {
			winRate = float64(result.ClosedWon) / float64(result.CardsCount) * 100
		}

		stats.TopPerformers[i] = UserPerformance{
			UserID:       result.UserID,
			UserName:     result.FirstName + " " + result.LastName,
			CardsCount:   result.CardsCount,
			TotalRevenue: result.TotalRevenue,
			WinRate:      winRate,
		}
	}

	return nil
}

func (h *DashboardHandler) getRevenueByMonthOptimized(ctx context.Context, stats *DashboardStats) error {
	var results []struct {
		Month   string  `json:"month"`
		Revenue float64 `json:"revenue"`
		Count   int64   `json:"count"`
	}

	query := `
		SELECT 
			TO_CHAR(c.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(c.value), 0) as revenue,
			COUNT(*) as count
		FROM cards c
		INNER JOIN stages s ON c.stage_id = s.id
		WHERE s.is_closed_won = true 
			AND c.actual_close_date >= NOW() - INTERVAL '12 months'
			AND c.actual_close_date IS NOT NULL
		GROUP BY TO_CHAR(c.actual_close_date, 'YYYY-MM')
		ORDER BY month DESC
		LIMIT 12
	`

	if err := h.db.Raw(query).Scan(&results).Error; err != nil {
		return err
	}

	stats.RevenueByMonth = make([]MonthlyRevenue, len(results))
	for i, result := range results {
		stats.RevenueByMonth[i] = MonthlyRevenue{
			Month:   result.Month,
			Revenue: result.Revenue,
			Count:   result.Count,
		}
	}

	return nil
}

func (h *DashboardHandler) getRevenueByMonth(ctx interface{}, stats *DashboardStats) error {
	var results []struct {
		Month   string  `json:"month"`
		Revenue float64 `json:"revenue"`
		Count   int64   `json:"count"`
	}

	if err := h.db.Model(&entities.Card{}).
		Select(`
			TO_CHAR(cards.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(cards.value), 0) as revenue,
			COUNT(*) as count
		`).
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("stages.is_closed_won = ? AND cards.actual_close_date >= ?", true, time.Now().AddDate(-1, 0, 0)). // Last 12 months
		Group("TO_CHAR(cards.actual_close_date, 'YYYY-MM')").
		Order("month DESC").
		Limit(12).
		Scan(&results).Error; err != nil {
		return err
	}

	stats.RevenueByMonth = make([]MonthlyRevenue, len(results))
	for i, result := range results {
		stats.RevenueByMonth[i] = MonthlyRevenue{
			Month:   result.Month,
			Revenue: result.Revenue,
			Count:   result.Count,
		}
	}

	return nil
}

func (h *DashboardHandler) getConversionFunnelOptimized(ctx context.Context, stats *DashboardStats) error {
	var results []struct {
		StageName string `json:"stage_name"`
		CardCount int64  `json:"card_count"`
		SortOrder int    `json:"sort_order"`
	}

	query := `
		SELECT 
			s.name as stage_name,
			COUNT(c.id) as card_count,
			s.sort_order
		FROM stages s
		LEFT JOIN cards c ON c.stage_id = s.id AND c.deleted_at IS NULL
		WHERE s.is_active = true
		GROUP BY s.name, s.sort_order
		ORDER BY s.sort_order ASC
	`

	if err := h.db.Raw(query).Scan(&results).Error; err != nil {
		return err
	}

	stats.ConversionFunnel = make([]ConversionStep, len(results))
	for i, result := range results {
		dropRate := 0.0
		if i > 0 && results[i-1].CardCount > 0 {
			dropRate = float64(results[i-1].CardCount-result.CardCount) / float64(results[i-1].CardCount) * 100
		}

		stats.ConversionFunnel[i] = ConversionStep{
			StageName: result.StageName,
			CardCount: result.CardCount,
			DropRate:  dropRate,
		}
	}

	return nil
}

func (h *DashboardHandler) getConversionFunnel(ctx interface{}, stats *DashboardStats) error {
	var results []struct {
		StageName string `json:"stage_name"`
		CardCount int64  `json:"card_count"`
		SortOrder int    `json:"sort_order"`
	}

	if err := h.db.Model(&entities.Card{}).
		Select("stages.name as stage_name, COUNT(cards.id) as card_count, stages.sort_order").
		Joins("JOIN stages ON cards.stage_id = stages.id").
		Where("cards.deleted_at IS NULL AND stages.is_active = ?", true).
		Group("stages.name, stages.sort_order").
		Order("stages.sort_order ASC").
		Scan(&results).Error; err != nil {
		return err
	}

	stats.ConversionFunnel = make([]ConversionStep, len(results))
	for i, result := range results {
		dropRate := 0.0
		if i > 0 && results[i-1].CardCount > 0 {
			dropRate = float64(results[i-1].CardCount-result.CardCount) / float64(results[i-1].CardCount) * 100
		}

		stats.ConversionFunnel[i] = ConversionStep{
			StageName: result.StageName,
			CardCount: result.CardCount,
			DropRate:  dropRate,
		}
	}

	return nil
}
