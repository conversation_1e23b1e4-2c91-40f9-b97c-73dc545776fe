package handlers

import (
	"fmt"
	"path/filepath"
	"strings"

	"crm-backend/internal/adapters/events"
	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/domain/entities"
	"crm-backend/internal/usecases"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

type DataTransferHandler struct {
	dataTransferUseCase *usecases.DataTransferUseCase
	sseManager          *events.SSEManager
	uploadPath          string
}

func NewDataTransferHandler(dataTransferUseCase *usecases.DataTransferUseCase, sseManager *events.SSEManager, uploadPath string) *DataTransferHandler {
	return &DataTransferHandler{
		dataTransferUseCase: dataTransferUseCase,
		sseManager:          sseManager,
		uploadPath:          uploadPath,
	}
}

// StartImport initiates a data import
func (h *DataTransferHandler) StartImport(c *fiber.Ctx) error {
	userIDPtr := middleware.GetUserIDFromContext(c)
	if userIDPtr == nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "unauthorized",
		})
	}

	// Parse form data
	entityType := c.FormValue("entity_type")
	if entityType == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "entity_type is required",
		})
	}

	// Validate entity type
	validTypes := []string{"cards", "contacts", "companies"}
	valid := false
	for _, vt := range validTypes {
		if entityType == vt {
			valid = true
			break
		}
	}
	if !valid {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid entity_type",
		})
	}

	// Get uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "no file uploaded",
		})
	}

	// Determine file format
	ext := strings.ToLower(filepath.Ext(file.Filename))
	var format entities.DataTransferFormat
	switch ext {
	case ".csv":
		format = entities.DataTransferFormatCSV
	case ".xlsx", ".xls":
		format = entities.DataTransferFormatXLSX
	default:
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "unsupported file format",
		})
	}

	// Save uploaded file
	uploadDir := filepath.Join(h.uploadPath, "imports")
	fileName := fmt.Sprintf("%s_%s_%s", entityType, uuid.New().String()[:8], file.Filename)
	filePath := filepath.Join(uploadDir, fileName)

	if err := c.SaveFile(file, filePath); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "failed to save file",
		})
	}

	// Start import
	transfer, err := h.dataTransferUseCase.StartImport(c.Context(), *userIDPtr, entityType, file.Filename, filePath, format)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.BroadcastEvent(events.Event{
		Type: "import_started",
		Data: map[string]interface{}{
			"transfer_id": transfer.ID,
			"entity_type": entityType,
			"file_name":   file.Filename,
		},
	})

	return c.Status(fiber.StatusAccepted).JSON(transfer)
}

// StartExport initiates a data export
func (h *DataTransferHandler) StartExport(c *fiber.Ctx) error {
	userIDPtr := middleware.GetUserIDFromContext(c)
	if userIDPtr == nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "unauthorized",
		})
	}

	var input struct {
		EntityType string                 `json:"entity_type" validate:"required,oneof=cards contacts companies"`
		Format     string                 `json:"format" validate:"required,oneof=csv xlsx"`
		Filters    map[string]interface{} `json:"filters"`
	}

	if err := c.BodyParser(&input); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid request body",
		})
	}

	// Convert format string to enum
	var format entities.DataTransferFormat
	switch input.Format {
	case "csv":
		format = entities.DataTransferFormatCSV
	case "xlsx":
		format = entities.DataTransferFormatXLSX
	default:
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid format",
		})
	}

	// Start export
	transfer, err := h.dataTransferUseCase.StartExport(c.Context(), *userIDPtr, input.EntityType, format, input.Filters)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Send SSE notification
	h.sseManager.BroadcastEvent(events.Event{
		Type: "export_started",
		Data: map[string]interface{}{
			"transfer_id": transfer.ID,
			"entity_type": input.EntityType,
			"format":      input.Format,
		},
	})

	return c.Status(fiber.StatusAccepted).JSON(transfer)
}

// GetTransferStatus gets the status of a transfer operation
func (h *DataTransferHandler) GetTransferStatus(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid transfer ID",
		})
	}

	transfer, err := h.dataTransferUseCase.GetTransferStatus(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "transfer not found",
		})
	}

	return c.JSON(transfer)
}

// ListTransfers lists all transfers for the current user
func (h *DataTransferHandler) ListTransfers(c *fiber.Ctx) error {
	userIDPtr := middleware.GetUserIDFromContext(c)
	if userIDPtr == nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "unauthorized",
		})
	}

	limit := c.QueryInt("limit", 10)
	offset := c.QueryInt("offset", 0)

	transfers, total, err := h.dataTransferUseCase.ListTransfers(c.Context(), *userIDPtr, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"data":   transfers,
		"total":  total,
		"limit":  limit,
		"offset": offset,
	})
}

// DownloadExport downloads an exported file
func (h *DataTransferHandler) DownloadExport(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid transfer ID",
		})
	}

	filePath, fileName, err := h.dataTransferUseCase.GetExportFile(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Set appropriate headers
	c.Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))

	// Set content type based on file extension
	ext := strings.ToLower(filepath.Ext(fileName))
	switch ext {
	case ".csv":
		c.Set("Content-Type", "text/csv")
	case ".xlsx", ".xls":
		c.Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	default:
		c.Set("Content-Type", "application/octet-stream")
	}

	return c.SendFile(filePath)
}

// GetImportTemplate returns a template file for import
func (h *DataTransferHandler) GetImportTemplate(c *fiber.Ctx) error {
	entityType := c.Query("entity_type")
	format := c.Query("format", "csv")

	if entityType == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "entity_type is required",
		})
	}

	// Define headers based on entity type
	var headers []string
	switch entityType {
	case "cards":
		headers = []string{"title", "description", "value", "priority", "due_date", "custom_field_1", "custom_field_2"}
	case "contacts":
		headers = []string{"first_name", "last_name", "email", "phone", "position", "custom_field_1", "custom_field_2"}
	case "companies":
		headers = []string{"name", "domain", "industry", "size", "annual_revenue", "website", "custom_field_1", "custom_field_2"}
	default:
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid entity_type",
		})
	}

	// Generate template data
	data := [][]string{headers}

	// Add sample row
	sampleRow := make([]string, len(headers))
	for i := range sampleRow {
		sampleRow[i] = "Sample " + headers[i]
	}
	data = append(data, sampleRow)

	// Create temporary file
	tempDir := filepath.Join(h.uploadPath, "templates")
	fileName := fmt.Sprintf("%s_template.%s", entityType, format)
	filePath := filepath.Join(tempDir, fileName)

	// Write template file
	var err error
	switch format {
	case "csv":
		err = h.dataTransferUseCase.WriteCSV(filePath, data)
		c.Set("Content-Type", "text/csv")
	case "xlsx":
		err = h.dataTransferUseCase.WriteExcel(filePath, data, entityType)
		c.Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	default:
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid format",
		})
	}

	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "failed to generate template",
		})
	}

	c.Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))
	return c.SendFile(filePath)
}

// RegisterRoutes registers all data transfer routes
func (h *DataTransferHandler) RegisterRoutes(app fiber.Router) {
	transfers := app.Group("/data-transfers")

	// Import/Export operations
	transfers.Post("/import", h.StartImport)
	transfers.Post("/export", h.StartExport)

	// Status and listing
	transfers.Get("/", h.ListTransfers)
	transfers.Get("/:id", h.GetTransferStatus)
	transfers.Get("/:id/download", h.DownloadExport)

	// Templates
	transfers.Get("/template", h.GetImportTemplate)
}
