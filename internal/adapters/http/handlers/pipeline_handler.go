package handlers

import (
	"strconv"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/usecases"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// PipelineHandler handles pipeline-related HTTP requests
type PipelineHandler struct {
	pipelineUsecase *usecases.PipelineUsecase
}

// NewPipelineHandler creates a new pipeline handler
func NewPipelineHandler(pipelineUsecase *usecases.PipelineUsecase) *PipelineHandler {
	return &PipelineHandler{
		pipelineUsecase: pipelineUsecase,
	}
}

// ListPipelines handles GET /api/pipelines and GET /api/v1/pipelines
func (h *PipelineHandler) ListPipelines(c *fiber.Ctx) error {
	// Parse pagination parameters
	limit := 50 // default limit
	offset := 0 // default offset

	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 {
			limit = parsed
		}
	}

	if o := c.Query("offset"); o != "" {
		if parsed, err := strconv.Atoi(o); err == nil && parsed >= 0 {
			offset = parsed
		}
	}

	// Check if stages should be included
	includeStages := c.QueryBool("include_stages", true)

	var pipelines []entities.Pipeline
	var total int64
	var err error

	if includeStages {
		pipelines, total, err = h.pipelineUsecase.ListPipelinesWithStages(c.Context(), limit, offset)
	} else {
		pipelines, total, err = h.pipelineUsecase.ListPipelines(c.Context(), limit, offset)
	}

	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to retrieve pipelines: " + err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Data: fiber.Map{
			"pipelines": pipelines,
			"total":     total,
			"limit":     limit,
			"offset":    offset,
		},
	})
}

// GetPipeline handles GET /api/pipelines/:id and GET /api/v1/pipelines/:id
func (h *PipelineHandler) GetPipeline(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline ID is required",
		})
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid pipeline ID format",
		})
	}

	// Check if stages should be included
	includeStages := c.QueryBool("include_stages", true)

	var pipeline *entities.Pipeline

	if includeStages {
		pipeline, err = h.pipelineUsecase.GetPipelineWithStages(c.Context(), id)
	} else {
		pipeline, err = h.pipelineUsecase.GetPipeline(c.Context(), id)
	}

	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "Pipeline not found",
		})
	}

	return c.JSON(SuccessResponse{
		Data: pipeline,
	})
}

// GetDefaultPipeline handles GET /api/pipelines/default and GET /api/v1/pipelines/default
func (h *PipelineHandler) GetDefaultPipeline(c *fiber.Ctx) error {
	pipeline, err := h.pipelineUsecase.GetDefaultPipeline(c.Context())
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "Default pipeline not found",
		})
	}

	return c.JSON(SuccessResponse{
		Data: pipeline,
	})
}

// CreatePipeline handles POST /api/v1/pipelines
func (h *PipelineHandler) CreatePipeline(c *fiber.Ctx) error {
	var req struct {
		Name        string                   `json:"name" validate:"required,min=1,max=255"`
		Description string                   `json:"description"`
		Type        entities.PipelineType    `json:"type"`
		Color       string                   `json:"color"`
		Icon        string                   `json:"icon"`
		IsDefault   bool                     `json:"is_default"`
		Settings    *entities.PipelineSettings `json:"settings"`
		Stages      []struct {
			Name              string                  `json:"name" validate:"required"`
			Description       string                  `json:"description"`
			Color             string                  `json:"color"`
			Icon              string                  `json:"icon"`
			IsClosedWon       bool                    `json:"is_closed_won"`
			IsClosedLost      bool                    `json:"is_closed_lost"`
			IsFinal           bool                    `json:"is_final"`
			Probability       float64                 `json:"probability"`
			AutoMoveAfterDays *int                    `json:"auto_move_after_days"`
			Settings          *entities.StageSettings `json:"settings"`
		} `json:"stages"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body: " + err.Error(),
		})
	}

	// Validate required fields
	if req.Name == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline name is required",
		})
	}

	// Set defaults
	if req.Color == "" {
		req.Color = "#3B82F6"
	}
	if req.Type == "" {
		req.Type = entities.PipelineTypeCustom
	}

	// Get user from context
	userID := c.Locals("userID")
	var createdByID *uuid.UUID
	if userID != nil {
		if id, ok := userID.(uuid.UUID); ok {
			createdByID = &id
		}
	}

	pipeline := &entities.Pipeline{
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Color:       req.Color,
		Icon:        req.Icon,
		IsDefault:   req.IsDefault,
		IsActive:    true,
		CreatedByID: createdByID,
	}

	// Set settings if provided
	if req.Settings != nil {
		pipeline.Settings = *req.Settings
	} else {
		pipeline.Settings = entities.PipelineSettings{}
	}

	// Create stages if provided
	if len(req.Stages) > 0 {
		pipeline.Stages = make([]entities.Stage, len(req.Stages))
		for i, s := range req.Stages {
			stage := entities.Stage{
				Name:              s.Name,
				Description:       s.Description,
				Color:             s.Color,
				Icon:              s.Icon,
				IsClosedWon:       s.IsClosedWon,
				IsClosedLost:      s.IsClosedLost,
				IsFinal:           s.IsFinal,
				Probability:       s.Probability,
				AutoMoveAfterDays: s.AutoMoveAfterDays,
				SortOrder:         i,
				IsActive:          true,
			}
			// ID will be generated by the repository
			stage.CreatedAt = time.Now()
			stage.UpdatedAt = time.Now()
			
			if s.Settings != nil {
				stage.Settings = *s.Settings
			} else {
				stage.Settings = entities.StageSettings{}
			}
			if s.Color == "" {
				stage.Color = "#6B7280"
			}
			pipeline.Stages[i] = stage
		}
	}

	if err := h.pipelineUsecase.CreatePipelineWithStages(c.Context(), pipeline); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to create pipeline: " + err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Data: pipeline,
	})
}

// UpdatePipeline handles PUT /api/v1/pipelines/:id
func (h *PipelineHandler) UpdatePipeline(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline ID is required",
		})
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid pipeline ID format",
		})
	}

	var req struct {
		Name        string `json:"name"`
		Description string `json:"description"`
		Color       string `json:"color"`
		IsDefault   *bool  `json:"is_default,omitempty"`
		IsActive    *bool  `json:"is_active,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body: " + err.Error(),
		})
	}

	// Get existing pipeline
	pipeline, err := h.pipelineUsecase.GetPipeline(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "Pipeline not found",
		})
	}

	// Update fields
	if req.Name != "" {
		pipeline.Name = req.Name
	}
	if req.Description != "" {
		pipeline.Description = req.Description
	}
	if req.Color != "" {
		pipeline.Color = req.Color
	}
	if req.IsDefault != nil {
		pipeline.IsDefault = *req.IsDefault
	}
	if req.IsActive != nil {
		pipeline.IsActive = *req.IsActive
	}

	if err := h.pipelineUsecase.UpdatePipeline(c.Context(), pipeline); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to update pipeline: " + err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Data: pipeline,
	})
}

// DeletePipeline handles DELETE /api/v1/pipelines/:id
func (h *PipelineHandler) DeletePipeline(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline ID is required",
		})
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid pipeline ID format",
		})
	}

	if err := h.pipelineUsecase.DeletePipeline(c.Context(), id); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to delete pipeline: " + err.Error(),
		})
	}

	return c.Status(fiber.StatusNoContent).Send(nil)
}

// SetDefaultPipeline handles PATCH /api/v1/pipelines/:id/default
func (h *PipelineHandler) SetDefaultPipeline(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline ID is required",
		})
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid pipeline ID format",
		})
	}

	if err := h.pipelineUsecase.SetDefaultPipeline(c.Context(), id); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to set default pipeline: " + err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Data: fiber.Map{"message": "Pipeline set as default successfully"},
	})
}

// GetPipelineStages handles GET /api/v1/pipelines/:id/stages
func (h *PipelineHandler) GetPipelineStages(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline ID is required",
		})
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid pipeline ID format",
		})
	}

	stages, err := h.pipelineUsecase.GetStagesByPipelineID(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to retrieve pipeline stages: " + err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Data: stages,
	})
}

// ReorderStages handles PATCH /api/v1/pipelines/:id/stages/reorder
func (h *PipelineHandler) ReorderStages(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline ID is required",
		})
	}

	pipelineID, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid pipeline ID format",
		})
	}

	var req struct {
		StageOrders []struct {
			ID    uuid.UUID `json:"id" validate:"required"`
			Order int       `json:"order" validate:"min=0"`
		} `json:"stage_orders" validate:"required,min=1"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body: " + err.Error(),
		})
	}

	if len(req.StageOrders) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Stage orders are required",
		})
	}

	// Convert to use case format
	stageOrders := make([]struct {
		ID    uuid.UUID
		Order int
	}, len(req.StageOrders))

	for i, so := range req.StageOrders {
		stageOrders[i] = struct {
			ID    uuid.UUID
			Order int
		}{
			ID:    so.ID,
			Order: so.Order,
		}
	}

	if err := h.pipelineUsecase.ReorderStages(c.Context(), pipelineID, stageOrders); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to reorder stages: " + err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Data: fiber.Map{"message": "Stages reordered successfully"},
	})
}

// CreateStage handles POST /api/v1/pipelines/:id/stages
func (h *PipelineHandler) CreateStage(c *fiber.Ctx) error {
	pipelineIDStr := c.Params("id")
	if pipelineIDStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline ID is required",
		})
	}

	pipelineID, err := uuid.Parse(pipelineIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid pipeline ID format",
		})
	}

	var req struct {
		Name              string                 `json:"name" validate:"required"`
		Description       string                 `json:"description"`
		Color             string                 `json:"color"`
		Icon              string                 `json:"icon"`
		IsClosedWon       bool                   `json:"is_closed_won"`
		IsClosedLost      bool                   `json:"is_closed_lost"`
		IsFinal           bool                   `json:"is_final"`
		Probability       float64                `json:"probability"`
		AutoMoveAfterDays *int                   `json:"auto_move_after_days"`
		NextStageID       *uuid.UUID             `json:"next_stage_id"`
		Settings          *entities.StageSettings `json:"settings"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body: " + err.Error(),
		})
	}

	if req.Name == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Stage name is required",
		})
	}

	if req.Color == "" {
		req.Color = "#6B7280"
	}

	stage := &entities.Stage{
		PipelineID:        pipelineID,
		Name:              req.Name,
		Description:       req.Description,
		Color:             req.Color,
		Icon:              req.Icon,
		IsClosedWon:       req.IsClosedWon,
		IsClosedLost:      req.IsClosedLost,
		IsFinal:           req.IsFinal,
		Probability:       req.Probability,
		AutoMoveAfterDays: req.AutoMoveAfterDays,
		NextStageID:       req.NextStageID,
		IsActive:          true,
	}

	if req.Settings != nil {
		stage.Settings = *req.Settings
	} else {
		stage.Settings = entities.StageSettings{}
	}

	if err := h.pipelineUsecase.CreateStage(c.Context(), stage); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to create stage: " + err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Data: stage,
	})
}

// UpdateStage handles PUT /api/v1/stages/:id
func (h *PipelineHandler) UpdateStage(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Stage ID is required",
		})
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid stage ID format",
		})
	}

	var req struct {
		Name              string                 `json:"name"`
		Description       string                 `json:"description"`
		Color             string                 `json:"color"`
		Icon              string                 `json:"icon"`
		IsClosedWon       *bool                  `json:"is_closed_won,omitempty"`
		IsClosedLost      *bool                  `json:"is_closed_lost,omitempty"`
		IsFinal           *bool                  `json:"is_final,omitempty"`
		Probability       *float64               `json:"probability,omitempty"`
		AutoMoveAfterDays *int                   `json:"auto_move_after_days,omitempty"`
		NextStageID       *uuid.UUID             `json:"next_stage_id,omitempty"`
		IsActive          *bool                  `json:"is_active,omitempty"`
		Settings          *entities.StageSettings `json:"settings,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body: " + err.Error(),
		})
	}

	stage, err := h.pipelineUsecase.GetStage(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "Stage not found",
		})
	}

	// Update fields
	if req.Name != "" {
		stage.Name = req.Name
	}
	if req.Description != "" {
		stage.Description = req.Description
	}
	if req.Color != "" {
		stage.Color = req.Color
	}
	if req.Icon != "" {
		stage.Icon = req.Icon
	}
	if req.IsClosedWon != nil {
		stage.IsClosedWon = *req.IsClosedWon
	}
	if req.IsClosedLost != nil {
		stage.IsClosedLost = *req.IsClosedLost
	}
	if req.IsFinal != nil {
		stage.IsFinal = *req.IsFinal
	}
	if req.Probability != nil {
		stage.Probability = *req.Probability
	}
	if req.AutoMoveAfterDays != nil {
		stage.AutoMoveAfterDays = req.AutoMoveAfterDays
	}
	if req.NextStageID != nil {
		stage.NextStageID = req.NextStageID
	}
	if req.IsActive != nil {
		stage.IsActive = *req.IsActive
	}
	if req.Settings != nil {
		stage.Settings = *req.Settings
	}

	if err := h.pipelineUsecase.UpdateStage(c.Context(), stage); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to update stage: " + err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Data: stage,
	})
}

// DeleteStage handles DELETE /api/v1/stages/:id
func (h *PipelineHandler) DeleteStage(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Stage ID is required",
		})
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid stage ID format",
		})
	}

	if err := h.pipelineUsecase.DeleteStage(c.Context(), id); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to delete stage: " + err.Error(),
		})
	}

	return c.Status(fiber.StatusNoContent).Send(nil)
}

// UpdatePipelineSettings handles PATCH /api/v1/pipelines/:id/settings
func (h *PipelineHandler) UpdatePipelineSettings(c *fiber.Ctx) error {
	idStr := c.Params("id")
	if idStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Pipeline ID is required",
		})
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid pipeline ID format",
		})
	}

	var settings entities.PipelineSettings
	if err := c.BodyParser(&settings); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body: " + err.Error(),
		})
	}

	if err := h.pipelineUsecase.UpdatePipelineSettings(c.Context(), id, &settings); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to update pipeline settings: " + err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Data: fiber.Map{"message": "Pipeline settings updated successfully"},
	})
}