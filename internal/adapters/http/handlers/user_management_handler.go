package handlers

import (
	"strconv"
	"strings"

	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/application/usecases"
	"crm-backend/internal/domain/entities"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// UserManagementHandler handles user management endpoints
type UserManagementHandler struct {
	userUsecase *usecases.UserManagementUsecase
	roleUsecase *usecases.RoleUsecase
	validator   *validator.Validate
}

// NewUserManagementHandler creates a new user management handler
func NewUserManagementHandler(
	userUsecase *usecases.UserManagementUsecase,
	roleUsecase *usecases.RoleUsecase,
) *UserManagementHandler {
	return &UserManagementHandler{
		userUsecase: userUsecase,
		roleUsecase: roleUsecase,
		validator:   validator.New(),
	}
}

// CreateUserRequest represents the request for creating a user
type CreateUserRequest struct {
	Email       string     `json:"email" validate:"required,email"`
	FirstName   string     `json:"first_name" validate:"required,min=2,max=50"`
	LastName    string     `json:"last_name" validate:"required,min=2,max=50"`
	Password    string     `json:"password" validate:"required,min=8"`
	RoleID      *uuid.UUID `json:"role_id"`
	Phone       string     `json:"phone"`
	Department  string     `json:"department"`
	Position    string     `json:"position"`
	Location    string     `json:"location"`
	Timezone    string     `json:"timezone"`
	Language    string     `json:"language"`
	IsActive    *bool      `json:"is_active"`
}

// UpdateUserRequest represents the request for updating a user
type UpdateUserRequest struct {
	Email              string     `json:"email" validate:"omitempty,email"`
	FirstName          string     `json:"first_name" validate:"omitempty,min=2,max=50"`
	LastName           string     `json:"last_name" validate:"omitempty,min=2,max=50"`
	RoleID             *string    `json:"role_id"` // String to allow null values
	Phone              string     `json:"phone"`
	Department         string     `json:"department"`
	Position           string     `json:"position"`
	Location           string     `json:"location"`
	Timezone           string     `json:"timezone"`
	Language           string     `json:"language"`
	Avatar             string     `json:"avatar"`
	IsActive           *bool      `json:"is_active"`
	EmailVerified      *bool      `json:"email_verified"`
	TwoFactorEnabled   *bool      `json:"two_factor_enabled"`
}

// UpdatePasswordRequest represents the request for updating user password
type UpdatePasswordRequest struct {
	NewPassword string `json:"new_password" validate:"required,min=8"`
}

// BulkActionRequest represents the request for bulk operations
type BulkActionRequest struct {
	UserIDs []uuid.UUID `json:"user_ids" validate:"required,min=1"`
	Action  string      `json:"action" validate:"required,oneof=delete activate deactivate"`
	RoleID  *uuid.UUID  `json:"role_id,omitempty"`
}

// AssignRoleRequest represents the request for assigning role to user
type AssignRoleRequest struct {
	RoleID uuid.UUID `json:"role_id" validate:"required"`
}

// CreateUser handles user creation
// @Summary Create user
// @Description Create a new user with optional role assignment
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param userRequest body CreateUserRequest true "User data"
// @Success 201 {object} SuccessResponse{data=entities.User}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Router /api/v1/users [post]
func (h *UserManagementHandler) CreateUser(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "create") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to create users",
		})
	}

	var req CreateUserRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	// Create user entity
	newUser := &entities.User{
		Email:       req.Email,
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		Password:    req.Password,
		Phone:       req.Phone,
		Department:  req.Department,
		Position:    req.Position,
		Location:    req.Location,
		Timezone:    req.Timezone,
		Language:    req.Language,
		IsActive:    true,
	}

	// Set default values
	if req.IsActive != nil {
		newUser.IsActive = *req.IsActive
	}
	if newUser.Timezone == "" {
		newUser.Timezone = "UTC"
	}
	if newUser.Language == "" {
		newUser.Language = "en"
	}

	createdUser, err := h.userUsecase.CreateUser(c.Context(), newUser, req.RoleID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "User created successfully",
		Data:    createdUser,
	})
}

// GetUser handles getting a specific user
// @Summary Get user
// @Description Get a user by ID with role information
// @Tags users
// @Security BearerAuth
// @Param id path string true "User ID"
// @Produce json
// @Success 200 {object} SuccessResponse{data=entities.User}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/{id} [get]
func (h *UserManagementHandler) GetUser(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view users",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid user ID",
		})
	}

	targetUser, err := h.userUsecase.GetUser(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "User not found",
		})
	}

	return c.JSON(SuccessResponse{
		Message: "User retrieved successfully",
		Data:    targetUser,
	})
}

// UpdateUser handles user updates
// @Summary Update user
// @Description Update an existing user
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "User ID"
// @Param userRequest body UpdateUserRequest true "User update data"
// @Success 200 {object} SuccessResponse{data=entities.User}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/{id} [put]
func (h *UserManagementHandler) UpdateUser(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "update") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to update users",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid user ID",
		})
	}

	var req UpdateUserRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	// Build updates map
	updates := make(map[string]interface{})
	
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.FirstName != "" {
		updates["first_name"] = req.FirstName
	}
	if req.LastName != "" {
		updates["last_name"] = req.LastName
	}
	if req.Phone != "" {
		updates["phone"] = req.Phone
	}
	if req.Department != "" {
		updates["department"] = req.Department
	}
	if req.Position != "" {
		updates["position"] = req.Position
	}
	if req.Location != "" {
		updates["location"] = req.Location
	}
	if req.Timezone != "" {
		updates["timezone"] = req.Timezone
	}
	if req.Language != "" {
		updates["language"] = req.Language
	}
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	if req.EmailVerified != nil {
		updates["email_verified"] = *req.EmailVerified
	}
	if req.TwoFactorEnabled != nil {
		updates["two_factor_enabled"] = *req.TwoFactorEnabled
	}
	if req.RoleID != nil {
		updates["role_id"] = *req.RoleID
	}

	updatedUser, err := h.userUsecase.UpdateUser(c.Context(), id, updates)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "User updated successfully",
		Data:    updatedUser,
	})
}

// DeleteUser handles user deletion
// @Summary Delete user
// @Description Delete a user by ID
// @Tags users
// @Security BearerAuth
// @Param id path string true "User ID"
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/{id} [delete]
func (h *UserManagementHandler) DeleteUser(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "delete") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to delete users",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid user ID",
		})
	}

	if err := h.userUsecase.DeleteUser(c.Context(), id, user.ID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "User deleted successfully",
	})
}

// ListUsers handles listing users with pagination and filters
// @Summary List users
// @Description Get list of users with optional filtering and pagination
// @Tags users
// @Security BearerAuth
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search in user fields"
// @Param role_id query string false "Filter by role ID"
// @Param is_active query boolean false "Filter by active status"
// @Param department query string false "Filter by department"
// @Success 200 {object} PaginatedResponse{data=[]entities.User}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Router /api/v1/users [get]
func (h *UserManagementHandler) ListUsers(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view users",
		})
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "20"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// Parse filters
	filters := make(map[string]interface{})
	
	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}
	if roleID := c.Query("role_id"); roleID != "" {
		if id, err := uuid.Parse(roleID); err == nil {
			filters["role_id"] = id
		}
	}
	if isActive := c.Query("is_active"); isActive != "" {
		if active, err := strconv.ParseBool(isActive); err == nil {
			filters["is_active"] = active
		}
	}
	if department := c.Query("department"); department != "" {
		filters["department"] = department
	}

	users, total, err := h.userUsecase.ListUsers(c.Context(), limit, offset, filters)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	meta := calculateMetaData(total, limit, offset)

	return c.JSON(PaginatedResponse{
		Message: "Users retrieved successfully",
		Data:    users,
		Meta:    meta,
	})
}

// AssignRole handles assigning a role to a user
// @Summary Assign role to user
// @Description Assign a role to a specific user
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "User ID"
// @Param roleRequest body AssignRoleRequest true "Role assignment data"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/{id}/role [post]
func (h *UserManagementHandler) AssignRole(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "update") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to assign user roles",
		})
	}

	idParam := c.Params("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid user ID",
		})
	}

	var req AssignRoleRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	if err := h.userUsecase.AssignRole(c.Context(), userID, req.RoleID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Role assigned successfully",
	})
}

// RemoveRole handles removing role from a user
// @Summary Remove role from user
// @Description Remove role assignment from a user
// @Tags users
// @Security BearerAuth
// @Param id path string true "User ID"
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/{id}/role [delete]
func (h *UserManagementHandler) RemoveRole(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "update") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to modify user roles",
		})
	}

	idParam := c.Params("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid user ID",
		})
	}

	if err := h.userUsecase.RemoveRole(c.Context(), userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Role removed successfully",
	})
}

// ToggleUserStatus handles toggling user active status
// @Summary Toggle user status
// @Description Toggle user active/inactive status
// @Tags users
// @Security BearerAuth
// @Param id path string true "User ID"
// @Produce json
// @Success 200 {object} SuccessResponse{data=entities.User}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/{id}/toggle-status [post]
func (h *UserManagementHandler) ToggleUserStatus(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "update") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to modify user status",
		})
	}

	idParam := c.Params("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid user ID",
		})
	}

	updatedUser, err := h.userUsecase.ToggleUserStatus(c.Context(), userID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "User status updated successfully",
		Data:    updatedUser,
	})
}

// UpdatePassword handles updating user password
// @Summary Update user password
// @Description Update a user's password
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "User ID"
// @Param passwordRequest body UpdatePasswordRequest true "Password update data"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/{id}/password [put]
func (h *UserManagementHandler) UpdatePassword(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "update") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to update user passwords",
		})
	}

	idParam := c.Params("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid user ID",
		})
	}

	var req UpdatePasswordRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	if err := h.userUsecase.UpdatePassword(c.Context(), userID, req.NewPassword); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Password updated successfully",
	})
}

// GetUsersByRole handles getting users by role
// @Summary Get users by role
// @Description Get all users assigned to a specific role
// @Tags users
// @Security BearerAuth
// @Param role_id path string true "Role ID"
// @Produce json
// @Success 200 {object} SuccessResponse{data=[]entities.User}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/role/{role_id} [get]
func (h *UserManagementHandler) GetUsersByRole(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view users",
		})
	}

	roleIDParam := c.Params("role_id")
	roleID, err := uuid.Parse(roleIDParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid role ID",
		})
	}

	users, err := h.userUsecase.GetUsersByRole(c.Context(), roleID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Users retrieved successfully",
		Data:    users,
	})
}

// BulkOperations handles bulk operations on users
// @Summary Bulk operations on users
// @Description Perform bulk operations (delete, activate, deactivate, assign role) on multiple users
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param bulkRequest body BulkActionRequest true "Bulk operation data"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Router /api/v1/users/bulk [post]
func (h *UserManagementHandler) BulkOperations(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)

	var req BulkActionRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	switch strings.ToLower(req.Action) {
	case "delete":
		if !user.HasPermission("users", "delete") {
			return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
				Error: "Insufficient permissions to delete users",
			})
		}
		if err := h.userUsecase.BulkDeleteUsers(c.Context(), req.UserIDs, user.ID); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
				Error: err.Error(),
			})
		}

	case "activate", "deactivate":
		if !user.HasPermission("users", "update") {
			return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
				Error: "Insufficient permissions to modify user status",
			})
		}
		
		// Toggle each user's status
		for _, userID := range req.UserIDs {
			if _, err := h.userUsecase.ToggleUserStatus(c.Context(), userID); err != nil {
				return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
					Error: err.Error(),
				})
			}
		}

	case "assign_role":
		if !user.HasPermission("users", "update") {
			return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
				Error: "Insufficient permissions to assign user roles",
			})
		}
		if req.RoleID == nil {
			return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
				Error: "Role ID is required for role assignment",
			})
		}
		if err := h.userUsecase.BulkAssignRole(c.Context(), req.UserIDs, *req.RoleID); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
				Error: err.Error(),
			})
		}

	default:
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid bulk action",
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Bulk operation completed successfully",
	})
}

// GetUserStatistics handles getting user statistics
// @Summary Get user statistics
// @Description Get comprehensive statistics about users in the system
// @Tags users
// @Security BearerAuth
// @Produce json
// @Success 200 {object} SuccessResponse{data=map[string]interface{}}
// @Failure 403 {object} ErrorResponse
// @Router /api/v1/users/statistics [get]
func (h *UserManagementHandler) GetUserStatistics(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view user statistics",
		})
	}

	statistics, err := h.userUsecase.GetUserStatistics(c.Context())
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "User statistics retrieved successfully",
		Data:    statistics,
	})
}

// GetUserPermissions handles getting permissions for a user
// @Summary Get user permissions
// @Description Get all permissions for a user based on their role
// @Tags users
// @Security BearerAuth
// @Param id path string true "User ID"
// @Produce json
// @Success 200 {object} SuccessResponse{data=[]entities.Permission}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/users/{id}/permissions [get]
func (h *UserManagementHandler) GetUserPermissions(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("users", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view user permissions",
		})
	}

	idParam := c.Params("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid user ID",
		})
	}

	permissions, err := h.roleUsecase.GetUserPermissions(c.Context(), userID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "User permissions retrieved successfully",
		Data:    permissions,
	})
}