package handlers

import (
	"strconv"

	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/application/usecases"
	"crm-backend/internal/domain/entities"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// RoleHandler handles role management endpoints
type RoleHandler struct {
	roleUsecase *usecases.RoleUsecase
	validator   *validator.Validate
}

// NewRoleHandler creates a new role handler
func <PERSON><PERSON>ole<PERSON><PERSON><PERSON>(roleUsecase *usecases.RoleUsecase) *RoleHandler {
	return &RoleHandler{
		roleUsecase: roleUsecase,
		validator:   validator.New(),
	}
}

// CreateRoleRequest represents the request for creating a role
type CreateRoleRequest struct {
	Name          string      `json:"name" validate:"required,min=2,max=50"`
	Description   string      `json:"description" validate:"max=255"`
	Color         string      `json:"color" validate:"max=7"`
	PermissionIDs []uuid.UUID `json:"permission_ids"`
}

// UpdateRoleRequest represents the request for updating a role
type UpdateRoleRequest struct {
	Name          string      `json:"name" validate:"min=2,max=50"`
	Description   string      `json:"description" validate:"max=255"`
	Color         string      `json:"color" validate:"max=7"`
	IsActive      *bool       `json:"is_active"`
	PermissionIDs []uuid.UUID `json:"permission_ids"`
}

// AssignPermissionsRequest represents the request for assigning permissions to a role
type AssignPermissionsRequest struct {
	PermissionIDs []uuid.UUID `json:"permission_ids" validate:"required,min=1"`
}

// CreateRole handles role creation
// @Summary Create role
// @Description Create a new role with optional permissions
// @Tags roles
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param roleRequest body CreateRoleRequest true "Role data"
// @Success 201 {object} SuccessResponse{data=entities.Role}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Router /api/v1/roles [post]
func (h *RoleHandler) CreateRole(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "create") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to create roles",
		})
	}

	var req CreateRoleRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	// Create role entity
	role := &entities.Role{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		IsSystem:    false,
		IsActive:    true,
	}

	// Set default color if not provided
	if role.Color == "" {
		role.Color = "#3B82F6"
	}

	createdRole, err := h.roleUsecase.CreateRole(c.Context(), role, req.PermissionIDs)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "Role created successfully",
		Data:    createdRole,
	})
}

// GetRole handles getting a specific role
// @Summary Get role
// @Description Get a role by ID with all permissions
// @Tags roles
// @Security BearerAuth
// @Param id path string true "Role ID"
// @Produce json
// @Success 200 {object} SuccessResponse{data=entities.Role}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/roles/{id} [get]
func (h *RoleHandler) GetRole(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view roles",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid role ID",
		})
	}

	role, err := h.roleUsecase.GetRole(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "Role not found",
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Role retrieved successfully",
		Data:    role,
	})
}

// UpdateRole handles role updates
// @Summary Update role
// @Description Update an existing role
// @Tags roles
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Role ID"
// @Param roleRequest body UpdateRoleRequest true "Role update data"
// @Success 200 {object} SuccessResponse{data=entities.Role}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/roles/{id} [put]
func (h *RoleHandler) UpdateRole(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "update") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to update roles",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid role ID",
		})
	}

	var req UpdateRoleRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	// Build updates map
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Color != "" {
		updates["color"] = req.Color
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	updatedRole, err := h.roleUsecase.UpdateRole(c.Context(), id, updates, req.PermissionIDs)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Role updated successfully",
		Data:    updatedRole,
	})
}

// DeleteRole handles role deletion
// @Summary Delete role
// @Description Delete a role by ID
// @Tags roles
// @Security BearerAuth
// @Param id path string true "Role ID"
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/roles/{id} [delete]
func (h *RoleHandler) DeleteRole(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "delete") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to delete roles",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid role ID",
		})
	}

	if err := h.roleUsecase.DeleteRole(c.Context(), id); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Role deleted successfully",
	})
}

// ListRoles handles listing roles with pagination
// @Summary List roles
// @Description Get list of roles with pagination
// @Tags roles
// @Security BearerAuth
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} PaginatedResponse{data=[]entities.Role}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Router /api/v1/roles [get]
func (h *RoleHandler) ListRoles(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view roles",
		})
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "20"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	roles, total, err := h.roleUsecase.ListRoles(c.Context(), limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	meta := calculateMetaData(total, limit, offset)

	return c.JSON(PaginatedResponse{
		Message: "Roles retrieved successfully",
		Data:    roles,
		Meta:    meta,
	})
}

// AssignPermissions handles assigning permissions to a role
// @Summary Assign permissions to role
// @Description Replace all permissions for a role with the provided list
// @Tags roles
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Role ID"
// @Param permissionsRequest body AssignPermissionsRequest true "Permission IDs"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/roles/{id}/permissions [post]
func (h *RoleHandler) AssignPermissions(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "update") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to modify role permissions",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid role ID",
		})
	}

	var req AssignPermissionsRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	if err := h.roleUsecase.AssignPermissionsToRole(c.Context(), id, req.PermissionIDs); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Permissions assigned successfully",
	})
}

// GetRolePermissions handles getting permissions for a role
// @Summary Get role permissions
// @Description Get all permissions assigned to a role
// @Tags roles
// @Security BearerAuth
// @Param id path string true "Role ID"
// @Produce json
// @Success 200 {object} SuccessResponse{data=[]entities.Permission}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/roles/{id}/permissions [get]
func (h *RoleHandler) GetRolePermissions(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view role permissions",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid role ID",
		})
	}

	permissions, err := h.roleUsecase.GetRolePermissions(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Role permissions retrieved successfully",
		Data:    permissions,
	})
}

// GetAllPermissions handles getting all available permissions
// @Summary Get all permissions
// @Description Get all available permissions in the system
// @Tags permissions
// @Security BearerAuth
// @Produce json
// @Success 200 {object} SuccessResponse{data=[]entities.Permission}
// @Failure 403 {object} ErrorResponse
// @Router /api/v1/permissions [get]
func (h *RoleHandler) GetAllPermissions(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "read") && !user.HasPermission("users", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view permissions",
		})
	}

	permissions, err := h.roleUsecase.GetAllPermissions(c.Context())
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Permissions retrieved successfully",
		Data:    permissions,
	})
}

// GetPermissionsByResource handles getting permissions for a specific resource
// @Summary Get permissions by resource
// @Description Get all permissions for a specific resource
// @Tags permissions
// @Security BearerAuth
// @Param resource path string true "Resource name"
// @Produce json
// @Success 200 {object} SuccessResponse{data=[]entities.Permission}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Router /api/v1/permissions/resource/{resource} [get]
func (h *RoleHandler) GetPermissionsByResource(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "read") && !user.HasPermission("users", "read") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to view permissions",
		})
	}

	resource := c.Params("resource")
	if resource == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Resource parameter is required",
		})
	}

	permissions, err := h.roleUsecase.GetPermissionsByResource(c.Context(), resource)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Resource permissions retrieved successfully",
		Data:    permissions,
	})
}

// DuplicateRole handles duplicating an existing role
// @Summary Duplicate role
// @Description Create a copy of an existing role with a new name
// @Tags roles
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Role ID to duplicate"
// @Param request body map[string]string true "New role name" 
// @Success 201 {object} SuccessResponse{data=entities.Role}
// @Failure 400 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/roles/{id}/duplicate [post]
func (h *RoleHandler) DuplicateRole(c *fiber.Ctx) error {
	// Check permissions
	user := middleware.MustGetUserFromContext(c)
	if !user.HasPermission("roles", "create") {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Insufficient permissions to create roles",
		})
	}

	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid role ID",
		})
	}

	var req map[string]string
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	newName, ok := req["name"]
	if !ok || newName == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "New role name is required",
		})
	}

	duplicatedRole, err := h.roleUsecase.DuplicateRole(c.Context(), id, newName)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "Role duplicated successfully",
		Data:    duplicatedRole,
	})
}