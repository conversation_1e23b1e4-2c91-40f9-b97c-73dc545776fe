package handlers

import (
	"strings"

	"github.com/go-playground/validator/v10"
)

// ErrorResponse represents a standard error response
type ErrorResponse struct {
	Error string `json:"error"`
}

// SuccessResponse represents a standard success response
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ValidationErrorResponse represents validation error response
type ValidationErrorResponse struct {
	Error  string            `json:"error"`
	Fields map[string]string `json:"fields"`
}

// PaginatedResponse represents a paginated response
type PaginatedResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Meta    MetaData    `json:"meta"`
}

// MetaData represents pagination metadata
type MetaData struct {
	Total       int64 `json:"total"`
	PerPage     int   `json:"per_page"`
	CurrentPage int   `json:"current_page"`
	LastPage    int   `json:"last_page"`
}

// getValidationErrors extracts validation errors
func getValidationErrors(err error) map[string]string {
	fields := make(map[string]string)
	
	if ve, ok := err.(validator.ValidationErrors); ok {
		for _, field := range ve {
			fieldName := strings.ToLower(field.Field())
			switch field.Tag() {
			case "required":
				fields[fieldName] = "This field is required"
			case "email":
				fields[fieldName] = "Must be a valid email address"
			case "min":
				fields[fieldName] = "Value is too short"
			case "max":
				fields[fieldName] = "Value is too long"
			case "url":
				fields[fieldName] = "Must be a valid URL"
			case "oneof":
				fields[fieldName] = "Invalid value"
			default:
				fields[fieldName] = "Invalid value"
			}
		}
	}
	
	return fields
}

// splitAuthHeader splits the authorization header
func splitAuthHeader(header string) []string {
	return strings.SplitN(header, " ", 2)
}

// calculateMetaData calculates pagination metadata
func calculateMetaData(total int64, limit, offset int) MetaData {
	currentPage := (offset / limit) + 1
	lastPage := int((total + int64(limit) - 1) / int64(limit))
	
	return MetaData{
		Total:       total,
		PerPage:     limit,
		CurrentPage: currentPage,
		LastPage:    lastPage,
	}
}