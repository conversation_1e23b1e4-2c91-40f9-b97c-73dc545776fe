package handlers

import (
	"fmt"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/domain/entities"
	"crm-backend/internal/infrastructure/config"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// FileHandler handles file upload endpoints
type FileHandler struct {
	db      *gorm.DB
	config  *config.StorageConfig
}

// NewFileHandler creates a new file handler
func NewFileHandler(db *gorm.DB, config *config.StorageConfig) *FileHandler {
	return &FileHandler{
		db:     db,
		config: config,
	}
}

// UploadRequest represents file upload request
type UploadRequest struct {
	EntityType string `form:"entity_type" validate:"required,oneof=card contact company"`
	EntityID   string `form:"entity_id" validate:"required,uuid"`
}

// UploadFile handles file uploads
// @Summary Upload file
// @Description Upload a file and attach it to an entity
// @Tags files
// @Security BearerAuth
// @Accept multipart/form-data
// @Produce json
// @Param entity_type formData string true "Entity type (card, contact, company)"
// @Param entity_id formData string true "Entity ID"
// @Param file formData file true "File to upload"
// @Success 201 {object} entities.Attachment
// @Failure 400 {object} ErrorResponse
// @Router /files/upload [post]
func (h *FileHandler) UploadFile(c *fiber.Ctx) error {
	// Parse form data
	var req UploadRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid form data",
		})
	}

	// Validate entity ID
	entityID, err := uuid.Parse(req.EntityID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid entity ID",
		})
	}

	// Get uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "No file uploaded",
		})
	}

	// Validate file
	if err := h.validateFile(file); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	// Ensure upload directory exists
	if err := os.MkdirAll(h.config.UploadPath, 0755); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to create upload directory",
		})
	}

	// Generate unique filename
	filename := h.generateFileName(file.Filename)
	filePath := filepath.Join(h.config.UploadPath, filename)

	// Save file
	if err := c.SaveFile(file, filePath); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to save file",
		})
	}

	// Get current user
	userID := middleware.MustGetUserIDFromContext(c)

	// Create attachment record
	attachment := &entities.Attachment{
		FileName:     file.Filename,
		FilePath:     filePath,
		FileSize:     file.Size,
		MimeType:     file.Header.Get("Content-Type"),
		UploadedByID: userID,
	}

	// Set entity relationship
	switch req.EntityType {
	case "card":
		attachment.CardID = &entityID
	case "contact":
		attachment.ContactID = &entityID
	case "company":
		attachment.CompanyID = &entityID
	}

	// Save to database
	if err := h.db.Create(attachment).Error; err != nil {
		// Clean up file if database save fails
		os.Remove(filePath)
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to save attachment record",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "File uploaded successfully",
		Data:    attachment,
	})
}

// DownloadFile handles file downloads
// @Summary Download file
// @Description Download a file by attachment ID
// @Tags files
// @Security BearerAuth
// @Param id path string true "Attachment ID"
// @Produce application/octet-stream
// @Success 200 {file} file
// @Failure 404 {object} ErrorResponse
// @Router /files/{id} [get]
func (h *FileHandler) DownloadFile(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid attachment ID",
		})
	}

	// Get attachment record
	var attachment entities.Attachment
	if err := h.db.First(&attachment, "id = ?", id).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "File not found",
		})
	}

	// Check if file exists
	if _, err := os.Stat(attachment.FilePath); os.IsNotExist(err) {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "File not found on disk",
		})
	}

	// Set headers
	c.Set("Content-Type", attachment.MimeType)
	c.Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", attachment.FileName))

	// Send file
	return c.SendFile(attachment.FilePath)
}

// GetFileInfo gets file information
// @Summary Get file info
// @Description Get file information by attachment ID
// @Tags files
// @Security BearerAuth
// @Param id path string true "Attachment ID"
// @Produce json
// @Success 200 {object} entities.Attachment
// @Failure 404 {object} ErrorResponse
// @Router /files/{id}/info [get]
func (h *FileHandler) GetFileInfo(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid attachment ID",
		})
	}

	var attachment entities.Attachment
	if err := h.db.Preload("UploadedBy").First(&attachment, "id = ?", id).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "File not found",
		})
	}

	return c.JSON(SuccessResponse{
		Message: "File info retrieved successfully",
		Data:    attachment,
	})
}

// DeleteFile handles file deletion
// @Summary Delete file
// @Description Delete a file by attachment ID
// @Tags files
// @Security BearerAuth
// @Param id path string true "Attachment ID"
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 404 {object} ErrorResponse
// @Router /files/{id} [delete]
func (h *FileHandler) DeleteFile(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid attachment ID",
		})
	}

	// Get attachment record
	var attachment entities.Attachment
	if err := h.db.First(&attachment, "id = ?", id).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "File not found",
		})
	}

	// Check permissions - only file uploader or admin can delete
	userID := middleware.MustGetUserIDFromContext(c)
	user := middleware.MustGetUserFromContext(c)
	
	if attachment.UploadedByID != userID && user.LegacyRole != "admin" {
		return c.Status(fiber.StatusForbidden).JSON(ErrorResponse{
			Error: "Permission denied",
		})
	}

	// Delete from database
	if err := h.db.Delete(&attachment).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to delete attachment record",
		})
	}

	// Delete file from disk
	if err := os.Remove(attachment.FilePath); err != nil {
		// Log error but don't fail the request
		fmt.Printf("Warning: Failed to delete file from disk: %v\n", err)
	}

	return c.JSON(SuccessResponse{
		Message: "File deleted successfully",
		Data:    map[string]string{"id": id.String()},
	})
}

// ListFiles lists files for an entity
// @Summary List files
// @Description List files attached to an entity
// @Tags files
// @Security BearerAuth
// @Param entity_type query string true "Entity type (card, contact, company)"
// @Param entity_id query string true "Entity ID"
// @Param limit query int false "Limit" default(10)
// @Param offset query int false "Offset" default(0)
// @Produce json
// @Success 200 {object} PaginatedResponse
// @Router /files [get]
func (h *FileHandler) ListFiles(c *fiber.Ctx) error {
	entityType := c.Query("entity_type")
	entityIDStr := c.Query("entity_id")
	limit := c.QueryInt("limit", 10)
	offset := c.QueryInt("offset", 0)

	if entityType == "" || entityIDStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "entity_type and entity_id are required",
		})
	}

	entityID, err := uuid.Parse(entityIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid entity ID",
		})
	}

	// Build query based on entity type
	query := h.db.Model(&entities.Attachment{}).Preload("UploadedBy")
	
	switch entityType {
	case "card":
		query = query.Where("card_id = ?", entityID)
	case "contact":
		query = query.Where("contact_id = ?", entityID)
	case "company":
		query = query.Where("company_id = ?", entityID)
	default:
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid entity type",
		})
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to count attachments",
		})
	}

	// Get attachments
	var attachments []entities.Attachment
	err = query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&attachments).Error
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to retrieve attachments",
		})
	}

	return c.JSON(PaginatedResponse{
		Message: "Files retrieved successfully",
		Data:    attachments,
		Meta:    calculateMetaData(total, limit, offset),
	})
}

// validateFile validates uploaded file
func (h *FileHandler) validateFile(file *multipart.FileHeader) error {
	// Check file size
	if file.Size > h.config.MaxSize {
		return fmt.Errorf("file size exceeds maximum allowed size of %d bytes", h.config.MaxSize)
	}

	// Check file extension (basic validation)
	allowedExtensions := []string{
		".jpg", ".jpeg", ".png", ".gif", ".webp", // Images
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", // Documents
		".txt", ".csv", ".xml", ".json", // Text files
		".zip", ".rar", ".7z", // Archives
	}

	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowed := false
	for _, allowedExt := range allowedExtensions {
		if ext == allowedExt {
			allowed = true
			break
		}
	}

	if !allowed {
		return fmt.Errorf("file type %s is not allowed", ext)
	}

	return nil
}

// generateFileName generates a unique filename
func (h *FileHandler) generateFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	name := strings.TrimSuffix(originalName, ext)
	
	// Sanitize filename
	name = strings.ReplaceAll(name, " ", "_")
	name = strings.ReplaceAll(name, "..", "")
	
	timestamp := time.Now().Format("20060102_150405")
	uuid := uuid.New().String()[:8]
	
	return fmt.Sprintf("%s_%s_%s%s", name, timestamp, uuid, ext)
}