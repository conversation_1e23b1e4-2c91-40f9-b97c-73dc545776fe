package handlers

import (
	"strconv"

	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/domain/repositories"
	"crm-backend/internal/usecases"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// CompanyHandler handles company endpoints
type CompanyHandler struct {
	companyUsecase *usecases.CompanyUsecase
	validator      *validator.Validate
}

// NewCompanyHandler creates a new company handler
func NewCompanyHandler(companyUsecase *usecases.CompanyUsecase) *CompanyHandler {
	return &CompanyHandler{
		companyUsecase: companyUsecase,
		validator:      validator.New(),
	}
}

// CreateCompany handles company creation
// @Summary Create company
// @Description Create a new company
// @Tags companies
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param companyRequest body usecases.CreateCompanyRequest true "Company data"
// @Success 201 {object} entities.Company
// @Failure 400 {object} ErrorResponse
// @Router /companies [post]
func (h *CompanyHandler) CreateCompany(c *fiber.Ctx) error {
	var req usecases.CreateCompanyRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	company, err := h.companyUsecase.CreateCompany(c.Context(), &req, userID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "Company created successfully",
		Data:    company,
	})
}

// GetCompany handles getting a specific company
// @Summary Get company
// @Description Get a company by ID with all relations
// @Tags companies
// @Security BearerAuth
// @Param id path string true "Company ID"
// @Produce json
// @Success 200 {object} entities.Company
// @Failure 404 {object} ErrorResponse
// @Router /companies/{id} [get]
func (h *CompanyHandler) GetCompany(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid company ID",
		})
	}

	company, err := h.companyUsecase.GetCompany(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "Company not found",
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Company retrieved successfully",
		Data:    company,
	})
}

// UpdateCompany handles company updates
// @Summary Update company
// @Description Update an existing company
// @Tags companies
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Company ID"
// @Param companyRequest body usecases.UpdateCompanyRequest true "Company update data"
// @Success 200 {object} entities.Company
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /companies/{id} [put]
func (h *CompanyHandler) UpdateCompany(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid company ID",
		})
	}

	var req usecases.UpdateCompanyRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	company, err := h.companyUsecase.UpdateCompany(c.Context(), id, &req, userID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Company updated successfully",
		Data:    company,
	})
}

// DeleteCompany handles company deletion
// @Summary Delete company
// @Description Delete a company by ID
// @Tags companies
// @Security BearerAuth
// @Param id path string true "Company ID"
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /companies/{id} [delete]
func (h *CompanyHandler) DeleteCompany(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid company ID",
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	if err := h.companyUsecase.DeleteCompany(c.Context(), id, userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Company deleted successfully",
	})
}

// ListCompanies handles listing companies with filters and pagination
// @Summary List companies
// @Description Get list of companies with optional filtering and pagination
// @Tags companies
// @Security BearerAuth
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search in company fields"
// @Param industry query string false "Filter by industry"
// @Param size query string false "Filter by company size"
// @Param country query string false "Filter by country"
// @Success 200 {object} PaginatedResponse
// @Failure 400 {object} ErrorResponse
// @Router /companies [get]
func (h *CompanyHandler) ListCompanies(c *fiber.Ctx) error {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "20"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// Parse filters - support both 'q' and 'search' parameters
	searchQuery := c.Query("q")
	if searchQuery == "" {
		searchQuery = c.Query("search")
	}
	
	filters := repositories.CompanyFilters{
		Search:   searchQuery,
		Industry: c.Query("industry"),
		Size:     c.Query("size"),
		Country:  c.Query("country"),
	}

	// Parse custom fields filters
	customFields := make(map[string]interface{})
	for key, values := range c.Queries() {
		// Exclude standard parameters from custom fields
		if key != "page" && key != "limit" && key != "q" && key != "search" && key != "industry" && key != "size" && key != "country" && key != "sort" && key != "order" {
			if len(values) == 1 {
				customFields[key] = values[0]
			} else {
				customFields[key] = values
			}
		}
	}
	filters.CustomFields = customFields

	companies, total, err := h.companyUsecase.ListCompanies(c.Context(), filters, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	meta := calculateMetaData(total, limit, offset)

	return c.JSON(PaginatedResponse{
		Message: "Companies retrieved successfully",
		Data:    companies,
		Meta:    meta,
	})
}

// SearchCompanies handles searching companies
// @Summary Search companies
// @Description Search companies by query string
// @Tags companies
// @Security BearerAuth
// @Produce json
// @Param q query string true "Search query"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} PaginatedResponse
// @Failure 400 {object} ErrorResponse
// @Router /companies/search [get]
func (h *CompanyHandler) SearchCompanies(c *fiber.Ctx) error {
	query := c.Query("q")
	if query == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Search query is required",
		})
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "20"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	companies, total, err := h.companyUsecase.SearchCompanies(c.Context(), query, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	meta := calculateMetaData(total, limit, offset)

	return c.JSON(PaginatedResponse{
		Message: "Companies search completed successfully",
		Data:    companies,
		Meta:    meta,
	})
}

// UpdateCustomFields handles updating custom fields only
// @Summary Update company custom fields
// @Description Update custom fields of a company
// @Tags companies
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Company ID"
// @Param customFields body map[string]interface{} true "Custom fields data"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /companies/{id}/custom-fields [patch]
func (h *CompanyHandler) UpdateCustomFields(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid company ID",
		})
	}

	var customFields map[string]interface{}
	if err := c.BodyParser(&customFields); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	if err := h.companyUsecase.UpdateCustomFields(c.Context(), id, customFields, userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Company custom fields updated successfully",
	})
}

// GetCompanyContacts handles getting all contacts for a company
// @Summary Get company contacts
// @Description Get all contacts for a specific company
// @Tags companies
// @Security BearerAuth
// @Param id path string true "Company ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Produce json
// @Success 200 {object} PaginatedResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /companies/{id}/contacts [get]
func (h *CompanyHandler) GetCompanyContacts(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid company ID",
		})
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "20"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	contacts, total, err := h.companyUsecase.GetCompanyContacts(c.Context(), id, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	meta := calculateMetaData(total, limit, offset)

	return c.JSON(PaginatedResponse{
		Message: "Company contacts retrieved successfully",
		Data:    contacts,
		Meta:    meta,
	})
}

// GetCompaniesByIndustry handles getting companies by industry
// @Summary Get companies by industry
// @Description Get all companies for a specific industry
// @Tags companies
// @Security BearerAuth
// @Param industry path string true "Industry name"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Produce json
// @Success 200 {object} PaginatedResponse
// @Failure 400 {object} ErrorResponse
// @Router /companies/industry/{industry} [get]
func (h *CompanyHandler) GetCompaniesByIndustry(c *fiber.Ctx) error {
	industry := c.Params("industry")
	if industry == "" {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Industry is required",
		})
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.Query("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.Query("limit", "20"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	companies, total, err := h.companyUsecase.GetCompaniesByIndustry(c.Context(), industry, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	meta := calculateMetaData(total, limit, offset)

	return c.JSON(PaginatedResponse{
		Message: "Companies by industry retrieved successfully",
		Data:    companies,
		Meta:    meta,
	})
}