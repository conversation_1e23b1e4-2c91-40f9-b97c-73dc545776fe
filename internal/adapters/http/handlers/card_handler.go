package handlers

import (
	"crm-backend/internal/adapters/http/middleware"
	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"crm-backend/internal/usecases"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// CardHandler handles card endpoints
type CardHandler struct {
	cardUsecase *usecases.CardUsecase
	validator   *validator.Validate
}

// NewCardHandler creates a new card handler
func NewCardHandler(cardUsecase *usecases.CardUsecase) *CardHandler {
	return &CardHandler{
		cardUsecase: cardUsecase,
		validator:   validator.New(),
	}
}

// CreateCard handles card creation
// @Summary Create card
// @Description Create a new card
// @Tags cards
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param cardRequest body usecases.CreateCardRequest true "Card data"
// @Success 201 {object} entities.Card
// @Failure 400 {object} ErrorResponse
// @Router /cards [post]
func (h *CardHandler) CreateCard(c *fiber.Ctx) error {
	var req usecases.CreateCardRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	card, err := h.cardUsecase.CreateCard(c.Context(), &req, userID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "Card created successfully",
		Data:    card,
	})
}

// GetCard handles getting a specific card
// @Summary Get card
// @Description Get a card by ID with all relations
// @Tags cards
// @Security BearerAuth
// @Param id path string true "Card ID"
// @Produce json
// @Success 200 {object} entities.Card
// @Failure 404 {object} ErrorResponse
// @Router /cards/{id} [get]
func (h *CardHandler) GetCard(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid card ID",
		})
	}

	card, err := h.cardUsecase.GetCard(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
			Error: "Card not found",
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Card retrieved successfully",
		Data:    card,
	})
}

// UpdateCard handles card updates
// @Summary Update card
// @Description Update an existing card
// @Tags cards
// @Security BearerAuth
// @Param id path string true "Card ID"
// @Accept json
// @Produce json
// @Param cardRequest body usecases.UpdateCardRequest true "Card update data"
// @Success 200 {object} entities.Card
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /cards/{id} [put]
func (h *CardHandler) UpdateCard(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid card ID",
		})
	}

	var req usecases.UpdateCardRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Fields: getValidationErrors(err),
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	card, err := h.cardUsecase.UpdateCard(c.Context(), id, &req, userID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Card updated successfully",
		Data:    card,
	})
}

// DeleteCard handles card deletion
// @Summary Delete card
// @Description Delete a card by ID
// @Tags cards
// @Security BearerAuth
// @Param id path string true "Card ID"
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 404 {object} ErrorResponse
// @Router /cards/{id} [delete]
func (h *CardHandler) DeleteCard(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid card ID",
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	if err := h.cardUsecase.DeleteCard(c.Context(), id, userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Card deleted successfully",
		Data:    map[string]string{"id": id.String()},
	})
}

// ListCards handles listing cards with filters
// @Summary List cards
// @Description Get a paginated list of cards with optional filters
// @Tags cards
// @Security BearerAuth
// @Param limit query int false "Limit" default(10)
// @Param offset query int false "Offset" default(0)
// @Param stage_id query string false "Stage ID"
// @Param contact_id query string false "Contact ID"
// @Param company_id query string false "Company ID"
// @Param assigned_to_id query string false "Assigned To ID"
// @Param priority query string false "Priority"
// @Param search query string false "Search term"
// @Produce json
// @Success 200 {object} PaginatedResponse
// @Router /cards [get]
func (h *CardHandler) ListCards(c *fiber.Ctx) error {
	limit := c.QueryInt("limit", 10)
	offset := c.QueryInt("offset", 0)

	var filters repositories.CardFilters

	// Parse stage_id
	if stageIDStr := c.Query("stage_id"); stageIDStr != "" {
		if stageID, err := uuid.Parse(stageIDStr); err == nil {
			filters.StageID = &stageID
		}
	}

	// Parse contact_id
	if contactIDStr := c.Query("contact_id"); contactIDStr != "" {
		if contactID, err := uuid.Parse(contactIDStr); err == nil {
			filters.ContactID = &contactID
		}
	}

	// Parse company_id
	if companyIDStr := c.Query("company_id"); companyIDStr != "" {
		if companyID, err := uuid.Parse(companyIDStr); err == nil {
			filters.CompanyID = &companyID
		}
	}

	// Parse assigned_to_id
	if assignedToIDStr := c.Query("assigned_to_id"); assignedToIDStr != "" {
		if assignedToID, err := uuid.Parse(assignedToIDStr); err == nil {
			filters.AssignedToID = &assignedToID
		}
	}

	// Parse priority
	if priorityStr := c.Query("priority"); priorityStr != "" {
		priority := entities.CardPriority(priorityStr)
		filters.Priority = &priority
	}

	// Parse search
	filters.Search = c.Query("search", "")

	cards, total, err := h.cardUsecase.ListCards(c.Context(), filters, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to retrieve cards",
		})
	}

	return c.JSON(PaginatedResponse{
		Message: "Cards retrieved successfully",
		Data:    cards,
		Meta:    calculateMetaData(total, limit, offset),
	})
}

// MoveCardToStage handles moving a card to a different stage
// @Summary Move card to stage
// @Description Move a card to a different stage
// @Tags cards
// @Security BearerAuth
// @Param id path string true "Card ID"
// @Param stage_id path string true "Stage ID"
// @Produce json
// @Success 200 {object} entities.Card
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /cards/{id}/move/{stage_id} [patch]
func (h *CardHandler) MoveCardToStage(c *fiber.Ctx) error {
	cardIDParam := c.Params("id")
	cardID, err := uuid.Parse(cardIDParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid card ID",
		})
	}

	stageIDParam := c.Params("stage_id")
	stageID, err := uuid.Parse(stageIDParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid stage ID",
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	card, err := h.cardUsecase.MoveCardToStage(c.Context(), cardID, stageID, userID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Card moved successfully",
		Data:    card,
	})
}

// GetOverdueCards handles getting overdue cards
// @Summary Get overdue cards
// @Description Get a list of overdue cards
// @Tags cards
// @Security BearerAuth
// @Param limit query int false "Limit" default(10)
// @Param offset query int false "Offset" default(0)
// @Produce json
// @Success 200 {object} PaginatedResponse
// @Router /cards/overdue [get]
func (h *CardHandler) GetOverdueCards(c *fiber.Ctx) error {
	limit := c.QueryInt("limit", 10)
	offset := c.QueryInt("offset", 0)

	cards, total, err := h.cardUsecase.GetOverdueCards(c.Context(), limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to retrieve overdue cards",
		})
	}

	return c.JSON(PaginatedResponse{
		Message: "Overdue cards retrieved successfully",
		Data:    cards,
		Meta:    calculateMetaData(total, limit, offset),
	})
}

// UpdateCustomFields handles updating card custom fields
// @Summary Update card custom fields
// @Description Update custom fields of a card
// @Tags cards
// @Security BearerAuth
// @Param id path string true "Card ID"
// @Accept json
// @Produce json
// @Param fields body map[string]interface{} true "Custom fields"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Router /cards/{id}/custom-fields [patch]
func (h *CardHandler) UpdateCustomFields(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid card ID",
		})
	}

	var fields map[string]interface{}
	if err := c.BodyParser(&fields); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
		})
	}

	userID := middleware.MustGetUserIDFromContext(c)
	if err := h.cardUsecase.UpdateCustomFields(c.Context(), id, fields, userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.JSON(SuccessResponse{
		Message: "Custom fields updated successfully",
		Data:    map[string]string{"id": id.String()},
	})
}