package middleware

import (
	"context"
	"strings"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/usecases"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// UserContextKey is the key for user in context
type contextKey string

const UserContextKey contextKey = "user"

// AuthMiddleware handles JWT authentication
type AuthMiddleware struct {
	authUsecase *usecases.AuthUsecase
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(authUsecase *usecases.AuthUsecase) *AuthMiddleware {
	return &AuthMiddleware{
		authUsecase: authUsecase,
	}
}

// RequireAuth middleware to protect routes
func (m *AuthMiddleware) RequireAuth() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var tokenString string

		// Check for token in Authorization header first
		authHeader := c.Get("Authorization")
		if authHeader != "" {
			// Extract token from header
			tokenParts := strings.SplitN(authHeader, " ", 2)
			if len(tokenParts) == 2 && strings.ToLower(tokenParts[0]) == "bearer" {
				tokenString = tokenParts[1]
			}
		}

		// If no token in header, check query parameter (for SSE connections)
		if tokenString == "" {
			tokenString = c.Query("token")
		}

		// If still no token found, return error
		if tokenString == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "Authentication required - provide token in Authorization header or query parameter",
			})
		}

		// Validate token
		user, err := m.authUsecase.ValidateToken(tokenString)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "Invalid or expired token",
			})
		}

		// Store user in context
		ctx := context.WithValue(c.Context(), UserContextKey, user)
		c.SetUserContext(ctx)

		return c.Next()
	}
}

// RequireRole middleware to check user role
func (m *AuthMiddleware) RequireRole(roles ...string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		user := GetUserFromContext(c)
		if user == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "Authentication required",
			})
		}

		// Check if user has required role
		hasRole := false
		for _, role := range roles {
			if user.LegacyRole == role {
				hasRole = true
				break
			}
		}

		if !hasRole {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"error": "Insufficient permissions",
			})
		}

		return c.Next()
	}
}

// OptionalAuth middleware for optional authentication
func (m *AuthMiddleware) OptionalAuth() fiber.Handler {
	return func(c *fiber.Ctx) error {
		authHeader := c.Get("Authorization")
		if authHeader == "" {
			return c.Next()
		}

		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) != 2 || strings.ToLower(tokenParts[0]) != "bearer" {
			return c.Next()
		}

		tokenString := tokenParts[1]
		user, err := m.authUsecase.ValidateToken(tokenString)
		if err == nil {
			ctx := context.WithValue(c.Context(), UserContextKey, user)
			c.SetUserContext(ctx)
		}

		return c.Next()
	}
}

// GetUserFromContext extracts user from fiber context
func GetUserFromContext(c *fiber.Ctx) *entities.User {
	if user := c.UserContext().Value(UserContextKey); user != nil {
		if u, ok := user.(*entities.User); ok {
			return u
		}
	}
	return nil
}

// GetUserIDFromContext extracts user ID from fiber context
func GetUserIDFromContext(c *fiber.Ctx) *uuid.UUID {
	user := GetUserFromContext(c)
	if user != nil {
		return &user.ID
	}
	return nil
}

// MustGetUserFromContext extracts user from context, panics if not found
func MustGetUserFromContext(c *fiber.Ctx) *entities.User {
	user := GetUserFromContext(c)
	if user == nil {
		panic("user not found in context")
	}
	return user
}

// MustGetUserIDFromContext extracts user ID from context, panics if not found
func MustGetUserIDFromContext(c *fiber.Ctx) uuid.UUID {
	user := MustGetUserFromContext(c)
	return user.ID
}