package middleware

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	
	"github.com/gofiber/fiber/v2"
)

// SecurityHeaders adds comprehensive security headers to all responses
func SecurityHeaders() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Generate a nonce for CSP
		nonce := generateNonce()
		c.Locals("csp-nonce", nonce)
		
		// HSTS - Enforce HTTPS for 1 year
		c.Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload")
		
		// Prevent clickjacking
		c.Set("X-Frame-Options", "DENY")
		
		// Prevent MIME type sniffing
		c.Set("X-Content-Type-Options", "nosniff")
		
		// Enable XSS protection (legacy browsers)
		c.Set("X-XSS-Protection", "1; mode=block")
		
		// Content Security Policy
		csp := fmt.Sprintf(
			"default-src 'self'; "+
				"script-src 'self' 'nonce-%s' https://cdn.jsdelivr.net; "+
				"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "+
				"font-src 'self' https://fonts.gstatic.com; "+
				"img-src 'self' data: https:; "+
				"connect-src 'self' ws: wss:; "+
				"frame-ancestors 'none'; "+
				"base-uri 'self'; "+
				"form-action 'self'",
			nonce,
		)
		c.Set("Content-Security-Policy", csp)
		
		// Referrer Policy
		c.Set("Referrer-Policy", "strict-origin-when-cross-origin")
		
		// Permissions Policy (formerly Feature Policy)
		c.Set("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
		
		// Remove server header
		c.Set("Server", "")
		
		// Remove X-Powered-By header
		c.Set("X-Powered-By", "")
		
		return c.Next()
	}
}

// generateNonce generates a random nonce for CSP
func generateNonce() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to a static nonce if random generation fails
		return "static-nonce-fallback"
	}
	return base64.URLEncoding.EncodeToString(bytes)
}

// CSRF middleware for CSRF protection
type CSRFConfig struct {
	TokenLength    int
	TokenLookup    string
	ContextKey     string
	CookieName     string
	CookieDomain   string
	CookiePath     string
	CookieSecure   bool
	CookieHTTPOnly bool
	CookieSameSite string
	Expiration     int
}

// DefaultCSRFConfig is the default CSRF configuration
var DefaultCSRFConfig = CSRFConfig{
	TokenLength:    32,
	TokenLookup:    "header:X-CSRF-Token",
	ContextKey:     "csrf",
	CookieName:     "_csrf",
	CookiePath:     "/",
	CookieSecure:   true,
	CookieHTTPOnly: true,
	CookieSameSite: "Strict",
	Expiration:     86400, // 24 hours
}

// CSRF provides CSRF protection
func CSRF(config ...CSRFConfig) fiber.Handler {
	cfg := DefaultCSRFConfig
	if len(config) > 0 {
		cfg = config[0]
	}
	
	return func(c *fiber.Ctx) error {
		// Skip CSRF for safe methods
		if c.Method() == "GET" || c.Method() == "HEAD" || c.Method() == "OPTIONS" {
			return c.Next()
		}
		
		// Get token from cookie
		cookieToken := c.Cookies(cfg.CookieName)
		
		// Get token from request (header or form)
		var requestToken string
		if cfg.TokenLookup == "header:X-CSRF-Token" {
			requestToken = c.Get("X-CSRF-Token")
		} else if cfg.TokenLookup == "form:csrf_token" {
			requestToken = c.FormValue("csrf_token")
		}
		
		// Generate new token if cookie doesn't exist
		if cookieToken == "" {
			token := generateCSRFToken(cfg.TokenLength)
			c.Cookie(&fiber.Cookie{
				Name:     cfg.CookieName,
				Value:    token,
				Path:     cfg.CookiePath,
				Domain:   cfg.CookieDomain,
				Secure:   cfg.CookieSecure,
				HTTPOnly: cfg.CookieHTTPOnly,
				SameSite: cfg.CookieSameSite,
				MaxAge:   cfg.Expiration,
			})
			
			// For GET requests, just set the cookie and continue
			if c.Method() == "GET" {
				c.Locals(cfg.ContextKey, token)
				return c.Next()
			}
			
			// For non-GET first requests, reject (no valid token yet)
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"error": "CSRF token missing",
			})
		}
		
		// Validate token for non-safe methods
		if cookieToken != requestToken {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"error": "Invalid CSRF token",
			})
		}
		
		// Store token in context for use in templates
		c.Locals(cfg.ContextKey, cookieToken)
		
		return c.Next()
	}
}

// generateCSRFToken generates a random CSRF token
func generateCSRFToken(length int) string {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		panic("Failed to generate CSRF token")
	}
	return base64.URLEncoding.EncodeToString(bytes)
}

// RateLimiter provides rate limiting middleware
type RateLimiterConfig struct {
	Max        int    // Maximum number of requests
	Expiration int    // Time window in seconds
	KeyFunc    func(*fiber.Ctx) string // Function to generate key for rate limiting
	Message    string // Error message
}

// DefaultRateLimiterConfig is the default rate limiter configuration
var DefaultRateLimiterConfig = RateLimiterConfig{
	Max:        100,
	Expiration: 60, // 100 requests per minute
	Message:    "Too many requests, please try again later",
	KeyFunc: func(c *fiber.Ctx) string {
		return c.IP() // Rate limit by IP address
	},
}

// RequestValidator provides input validation middleware
func RequestValidator() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Limit request body size (10MB default)
		if c.Request().Header.ContentLength() > 10*1024*1024 {
			return c.Status(fiber.StatusRequestEntityTooLarge).JSON(fiber.Map{
				"error": "Request body too large",
			})
		}
		
		// Check for suspicious patterns in query parameters
		query := string(c.Request().URI().QueryString())
		if containsSQLInjection(query) || containsXSS(query) {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid request parameters",
			})
		}
		
		return c.Next()
	}
}

// containsSQLInjection checks for common SQL injection patterns
func containsSQLInjection(input string) bool {
	// Basic SQL injection pattern detection
	patterns := []string{
		"--", ";--", "/*", "*/", "xp_", "sp_", "0x",
		" or ", " and ", "union ", "select ", "insert ",
		"delete ", "update ", "drop ", "exec ", "execute ",
		"script", "javascript:", "onload=", "onerror=",
	}
	
	lowerInput := string(input)
	for _, pattern := range patterns {
		if contains(lowerInput, pattern) {
			return true
		}
	}
	
	return false
}

// containsXSS checks for common XSS patterns
func containsXSS(input string) bool {
	patterns := []string{
		"<script", "</script", "javascript:", "onerror=",
		"onload=", "onclick=", "onmouseover=", "<iframe",
		"<object", "<embed", "<applet", "alert(",
		"confirm(", "prompt(", "document.cookie",
		"window.location", "document.write",
	}
	
	lowerInput := string(input)
	for _, pattern := range patterns {
		if contains(lowerInput, pattern) {
			return true
		}
	}
	
	return false
}

// contains checks if string contains substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && containsHelper(s, substr)
}

func containsHelper(s, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(s) < len(substr) {
		return false
	}
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}