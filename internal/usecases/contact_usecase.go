package usecases

import (
	"context"
	"errors"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

// ContactUsecase handles contact business logic
type ContactUsecase struct {
	contactRepo  repositories.ContactRepository
	activityRepo repositories.ActivityRepository
	eventBus     EventBus
}

// NewContactUsecase creates a new contact usecase
func NewContactUsecase(
	contactRepo repositories.ContactRepository,
	activityRepo repositories.ActivityRepository,
	eventBus EventBus,
) *ContactUsecase {
	return &ContactUsecase{
		contactRepo:  contactRepo,
		activityRepo: activityRepo,
		eventBus:     eventBus,
	}
}

// CreateContactRequest represents contact creation request
type CreateContactRequest struct {
	FirstName    string                 `json:"first_name" validate:"required"`
	LastName     string                 `json:"last_name" validate:"required"`
	Email        string                 `json:"email" validate:"omitempty,email"`
	Phone        string                 `json:"phone"`
	JobTitle     string                 `json:"job_title"`
	CompanyID    *uuid.UUID             `json:"company_id,omitempty"`
	CustomFields map[string]interface{} `json:"custom_fields,omitempty"`
}

// CreateContact creates a new contact
func (uc *ContactUsecase) CreateContact(ctx context.Context, req *CreateContactRequest, userID uuid.UUID) (*entities.Contact, error) {
	// Check if contact with email already exists
	if req.Email != "" {
		existingContact, _ := uc.contactRepo.GetByEmail(ctx, req.Email)
		if existingContact != nil {
			return nil, errors.New("contact already exists with this email")
		}
	}

	contact := &entities.Contact{
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		Email:        req.Email,
		Phone:        req.Phone,
		JobTitle:     req.JobTitle,
		CompanyID:    req.CompanyID,
		CustomFields: entities.CustomFields(req.CustomFields),
	}

	if err := uc.contactRepo.Create(ctx, contact); err != nil {
		return nil, err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeCreated,
		Title:       "Contact created",
		Description: "Contact '" + contact.FullName() + "' was created",
		ContactID:   &contact.ID,
		CompanyID:   contact.CompanyID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishContactCreated(contact)

	return contact, nil
}

// UpdateContactRequest represents contact update request
type UpdateContactRequest struct {
	FirstName    *string                `json:"first_name,omitempty"`
	LastName     *string                `json:"last_name,omitempty"`
	Email        *string                `json:"email,omitempty" validate:"omitempty,email"`
	Phone        *string                `json:"phone,omitempty"`
	JobTitle     *string                `json:"job_title,omitempty"`
	CompanyID    *uuid.UUID             `json:"company_id,omitempty"`
	CustomFields map[string]interface{} `json:"custom_fields,omitempty"`
}

// UpdateContact updates an existing contact
func (uc *ContactUsecase) UpdateContact(ctx context.Context, contactID uuid.UUID, req *UpdateContactRequest, userID uuid.UUID) (*entities.Contact, error) {
	contact, err := uc.contactRepo.GetByID(ctx, contactID)
	if err != nil {
		return nil, err
	}

	// Check if email is being changed and if it's already taken
	if req.Email != nil && *req.Email != contact.Email && *req.Email != "" {
		existingContact, _ := uc.contactRepo.GetByEmail(ctx, *req.Email)
		if existingContact != nil && existingContact.ID != contactID {
			return nil, errors.New("contact already exists with this email")
		}
	}

	if req.FirstName != nil {
		contact.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		contact.LastName = *req.LastName
	}
	if req.Email != nil {
		contact.Email = *req.Email
	}
	if req.Phone != nil {
		contact.Phone = *req.Phone
	}
	if req.JobTitle != nil {
		contact.JobTitle = *req.JobTitle
	}
	if req.CompanyID != nil {
		contact.CompanyID = req.CompanyID
	}

	// Update custom fields
	if req.CustomFields != nil {
		if contact.CustomFields == nil {
			contact.CustomFields = make(entities.CustomFields)
		}
		for key, value := range req.CustomFields {
			contact.CustomFields[key] = value
		}
	}

	if err := uc.contactRepo.Update(ctx, contact); err != nil {
		return nil, err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeUpdated,
		Title:       "Contact updated",
		Description: "Contact '" + contact.FullName() + "' was updated",
		ContactID:   &contact.ID,
		CompanyID:   contact.CompanyID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishContactUpdated(contact)

	return contact, nil
}

// GetContact retrieves a contact with all relations
func (uc *ContactUsecase) GetContact(ctx context.Context, contactID uuid.UUID) (*entities.Contact, error) {
	return uc.contactRepo.GetByIDWithRelations(ctx, contactID)
}

// ListContacts lists contacts with filtering
func (uc *ContactUsecase) ListContacts(ctx context.Context, filters repositories.ContactFilters, limit, offset int) ([]entities.Contact, int64, error) {
	return uc.contactRepo.List(ctx, filters, limit, offset)
}

// SearchContacts searches contacts by query
func (uc *ContactUsecase) SearchContacts(ctx context.Context, query string, limit, offset int) ([]entities.Contact, int64, error) {
	return uc.contactRepo.Search(ctx, query, limit, offset)
}

// DeleteContact deletes a contact
func (uc *ContactUsecase) DeleteContact(ctx context.Context, contactID uuid.UUID, userID uuid.UUID) error {
	contact, err := uc.contactRepo.GetByID(ctx, contactID)
	if err != nil {
		return err
	}

	if err := uc.contactRepo.Delete(ctx, contactID); err != nil {
		return err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeUpdated,
		Title:       "Contact deleted",
		Description: "Contact '" + contact.FullName() + "' was deleted",
		CompanyID:   contact.CompanyID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishContactDeleted(contact)

	return nil
}

// UpdateCustomFields updates only custom fields of a contact
func (uc *ContactUsecase) UpdateCustomFields(ctx context.Context, contactID uuid.UUID, fields map[string]interface{}, userID uuid.UUID) error {
	customFields := entities.CustomFields(fields)
	
	if err := uc.contactRepo.UpdateCustomFields(ctx, contactID, customFields); err != nil {
		return err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeUpdated,
		Title:       "Contact custom fields updated",
		Description: "Custom fields were updated",
		ContactID:   &contactID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	return nil
}

// GetContactsByCompany retrieves contacts for a specific company
func (uc *ContactUsecase) GetContactsByCompany(ctx context.Context, companyID uuid.UUID, limit, offset int) ([]entities.Contact, int64, error) {
	return uc.contactRepo.ListByCompanyID(ctx, companyID, limit, offset)
}