package usecases

import (
	"context"
	"fmt"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

// PipelineUsecase handles pipeline business logic
type PipelineUsecase struct {
	pipelineRepo repositories.PipelineRepository
	stageRepo    repositories.StageRepository
	eventBus     EventBus
}

// NewPipelineUsecase creates a new pipeline use case
func NewPipelineUsecase(
	pipelineRepo repositories.PipelineRepository,
	stageRepo repositories.StageRepository,
	eventBus EventBus,
) *PipelineUsecase {
	return &PipelineUsecase{
		pipelineRepo: pipelineRepo,
		stageRepo:    stageRepo,
		eventBus:     eventBus,
	}
}

// ListPipelines returns all active pipelines
func (uc *PipelineUsecase) ListPipelines(ctx context.Context, limit, offset int) ([]entities.Pipeline, int64, error) {
	return uc.pipelineRepo.List(ctx, limit, offset)
}

// ListPipelinesWithStages returns all active pipelines with their stages and cards
func (uc *PipelineUsecase) ListPipelinesWithStages(ctx context.Context, limit, offset int) ([]entities.Pipeline, int64, error) {
	return uc.pipelineRepo.ListWithStages(ctx, limit, offset)
}

// GetPipeline returns a pipeline by ID
func (uc *PipelineUsecase) GetPipeline(ctx context.Context, id uuid.UUID) (*entities.Pipeline, error) {
	return uc.pipelineRepo.GetByID(ctx, id)
}

// GetPipelineWithStages returns a pipeline by ID with its stages and cards
func (uc *PipelineUsecase) GetPipelineWithStages(ctx context.Context, id uuid.UUID) (*entities.Pipeline, error) {
	pipeline, err := uc.pipelineRepo.GetByIDWithStages(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get pipeline with stages: %w", err)
	}
	return pipeline, nil
}

// GetDefaultPipeline returns the default pipeline
func (uc *PipelineUsecase) GetDefaultPipeline(ctx context.Context) (*entities.Pipeline, error) {
	pipeline, err := uc.pipelineRepo.GetDefault(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get default pipeline: %w", err)
	}
	return pipeline, nil
}

// CreatePipeline creates a new pipeline
func (uc *PipelineUsecase) CreatePipeline(ctx context.Context, pipeline *entities.Pipeline) error {
	// Validate pipeline
	if err := uc.validatePipeline(pipeline); err != nil {
		return fmt.Errorf("pipeline validation failed: %w", err)
	}

	// Create pipeline
	if err := uc.pipelineRepo.Create(ctx, pipeline); err != nil {
		return fmt.Errorf("failed to create pipeline: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		uc.eventBus.PublishPipelineCreated(pipeline)
	}

	return nil
}

// UpdatePipeline updates an existing pipeline
func (uc *PipelineUsecase) UpdatePipeline(ctx context.Context, pipeline *entities.Pipeline) error {
	// Validate pipeline
	if err := uc.validatePipeline(pipeline); err != nil {
		return fmt.Errorf("pipeline validation failed: %w", err)
	}

	// Update pipeline
	if err := uc.pipelineRepo.Update(ctx, pipeline); err != nil {
		return fmt.Errorf("failed to update pipeline: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		uc.eventBus.PublishPipelineUpdated(pipeline)
	}

	return nil
}

// DeletePipeline soft deletes a pipeline
func (uc *PipelineUsecase) DeletePipeline(ctx context.Context, id uuid.UUID) error {
	// Get pipeline first
	pipeline, err := uc.pipelineRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get pipeline: %w", err)
	}

	// Check if it's the default pipeline
	if pipeline.IsDefault {
		return fmt.Errorf("cannot delete default pipeline")
	}

	// Soft delete by setting is_active to false
	pipeline.IsActive = false
	if err := uc.pipelineRepo.Update(ctx, pipeline); err != nil {
		return fmt.Errorf("failed to delete pipeline: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		uc.eventBus.PublishPipelineDeleted(pipeline)
	}

	return nil
}

// SetDefaultPipeline sets a pipeline as the default
func (uc *PipelineUsecase) SetDefaultPipeline(ctx context.Context, id uuid.UUID) error {
	// Verify pipeline exists
	pipeline, err := uc.pipelineRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("pipeline not found: %w", err)
	}

	if !pipeline.IsActive {
		return fmt.Errorf("cannot set inactive pipeline as default")
	}

	// Set as default
	if err := uc.pipelineRepo.SetDefault(ctx, id); err != nil {
		return fmt.Errorf("failed to set default pipeline: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		pipeline.IsDefault = true
		uc.eventBus.PublishPipelineUpdated(pipeline)
	}

	return nil
}

// GetStagesByPipelineID returns all stages for a pipeline
func (uc *PipelineUsecase) GetStagesByPipelineID(ctx context.Context, pipelineID uuid.UUID) ([]entities.Stage, error) {
	stages, err := uc.stageRepo.ListByPipelineID(ctx, pipelineID)
	if err != nil {
		return nil, fmt.Errorf("failed to get stages for pipeline: %w", err)
	}
	return stages, nil
}

// ReorderStages reorders stages in a pipeline
func (uc *PipelineUsecase) ReorderStages(ctx context.Context, pipelineID uuid.UUID, stageOrders []struct {
	ID    uuid.UUID
	Order int
}) error {
	// Verify pipeline exists
	_, err := uc.pipelineRepo.GetByID(ctx, pipelineID)
	if err != nil {
		return fmt.Errorf("pipeline not found: %w", err)
	}

	// Reorder stages
	if err := uc.stageRepo.ReorderStages(ctx, pipelineID, stageOrders); err != nil {
		return fmt.Errorf("failed to reorder stages: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		// You might want to create a specific event for this
		// uc.eventBus.PublishStagesReordered(pipelineID, stageOrders)
	}

	return nil
}

// CreatePipelineWithStages creates a new pipeline with stages
func (uc *PipelineUsecase) CreatePipelineWithStages(ctx context.Context, pipeline *entities.Pipeline) error {
	// Validate pipeline
	if err := uc.validatePipeline(pipeline); err != nil {
		return fmt.Errorf("pipeline validation failed: %w", err)
	}

	// Create pipeline with stages in a transaction
	if err := uc.pipelineRepo.CreateWithStages(ctx, pipeline); err != nil {
		return fmt.Errorf("failed to create pipeline with stages: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		uc.eventBus.PublishPipelineCreated(pipeline)
	}

	return nil
}

// CreateStage creates a new stage for a pipeline
func (uc *PipelineUsecase) CreateStage(ctx context.Context, stage *entities.Stage) error {
	// Verify pipeline exists
	_, err := uc.pipelineRepo.GetByID(ctx, stage.PipelineID)
	if err != nil {
		return fmt.Errorf("pipeline not found: %w", err)
	}

	// Get max sort order
	stages, err := uc.stageRepo.ListByPipelineID(ctx, stage.PipelineID)
	if err != nil {
		return fmt.Errorf("failed to get stages: %w", err)
	}

	maxOrder := -1
	for _, s := range stages {
		if s.SortOrder > maxOrder {
			maxOrder = s.SortOrder
		}
	}
	stage.SortOrder = maxOrder + 1

	// Create stage
	if err := uc.stageRepo.Create(ctx, stage); err != nil {
		return fmt.Errorf("failed to create stage: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		// uc.eventBus.PublishStageCreated(stage)
	}

	return nil
}

// GetStage returns a stage by ID
func (uc *PipelineUsecase) GetStage(ctx context.Context, id uuid.UUID) (*entities.Stage, error) {
	return uc.stageRepo.GetByID(ctx, id)
}

// UpdateStage updates an existing stage
func (uc *PipelineUsecase) UpdateStage(ctx context.Context, stage *entities.Stage) error {
	// Update stage
	if err := uc.stageRepo.Update(ctx, stage); err != nil {
		return fmt.Errorf("failed to update stage: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		// uc.eventBus.PublishStageUpdated(stage)
	}

	return nil
}

// DeleteStage deletes a stage
func (uc *PipelineUsecase) DeleteStage(ctx context.Context, id uuid.UUID) error {
	// Get stage first
	stage, err := uc.stageRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get stage: %w", err)
	}

	// Check if stage has cards
	// TODO: Add check for cards in stage

	// Delete stage
	if err := uc.stageRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete stage: %w", err)
	}

	// Reorder remaining stages
	stages, err := uc.stageRepo.ListByPipelineID(ctx, stage.PipelineID)
	if err == nil {
		var stageOrders []struct {
			ID    uuid.UUID
			Order int
		}
		for i, s := range stages {
			stageOrders = append(stageOrders, struct {
				ID    uuid.UUID
				Order int
			}{
				ID:    s.ID,
				Order: i,
			})
		}
		uc.stageRepo.ReorderStages(ctx, stage.PipelineID, stageOrders)
	}

	// Publish event
	if uc.eventBus != nil {
		// uc.eventBus.PublishStageDeleted(stage)
	}

	return nil
}

// UpdatePipelineSettings updates pipeline settings
func (uc *PipelineUsecase) UpdatePipelineSettings(ctx context.Context, pipelineID uuid.UUID, settings *entities.PipelineSettings) error {
	// Get pipeline
	pipeline, err := uc.pipelineRepo.GetByID(ctx, pipelineID)
	if err != nil {
		return fmt.Errorf("pipeline not found: %w", err)
	}

	// Update settings
	pipeline.Settings = *settings
	if err := uc.pipelineRepo.Update(ctx, pipeline); err != nil {
		return fmt.Errorf("failed to update pipeline settings: %w", err)
	}

	// Publish event
	if uc.eventBus != nil {
		uc.eventBus.PublishPipelineUpdated(pipeline)
	}

	return nil
}

// validatePipeline validates pipeline data
func (uc *PipelineUsecase) validatePipeline(pipeline *entities.Pipeline) error {
	if pipeline.Name == "" {
		return fmt.Errorf("pipeline name is required")
	}

	if len(pipeline.Name) > 255 {
		return fmt.Errorf("pipeline name too long (max 255 characters)")
	}

	if pipeline.Color != "" && len(pipeline.Color) != 7 {
		return fmt.Errorf("invalid color format (use hex format #RRGGBB)")
	}

	// Validate pipeline type
	validTypes := map[entities.PipelineType]bool{
		entities.PipelineTypeSales:    true,
		entities.PipelineTypeHR:       true,
		entities.PipelineTypeProjects: true,
		entities.PipelineTypeSupport:  true,
		entities.PipelineTypeCustom:   true,
	}

	if pipeline.Type != "" && !validTypes[pipeline.Type] {
		return fmt.Errorf("invalid pipeline type")
	}

	return nil
}