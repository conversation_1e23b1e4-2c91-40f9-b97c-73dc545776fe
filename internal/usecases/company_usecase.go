package usecases

import (
	"context"
	"errors"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

// CompanyUsecase handles company business logic
type CompanyUsecase struct {
	companyRepo  repositories.CompanyRepository
	contactRepo  repositories.ContactRepository
	activityRepo repositories.ActivityRepository
	eventBus     EventBus
}

// NewCompanyUsecase creates a new company usecase
func NewCompanyUsecase(
	companyRepo repositories.CompanyRepository,
	contactRepo repositories.ContactRepository,
	activityRepo repositories.ActivityRepository,
	eventBus EventBus,
) *CompanyUsecase {
	return &CompanyUsecase{
		companyRepo:  companyRepo,
		contactRepo:  contactRepo,
		activityRepo: activityRepo,
		eventBus:     eventBus,
	}
}

// CreateCompanyRequest represents company creation request
type CreateCompanyRequest struct {
	Name         string                 `json:"name" validate:"required"`
	Website      string                 `json:"website" validate:"omitempty,url"`
	Industry     string                 `json:"industry"`
	Size         string                 `json:"size"`
	Address      string                 `json:"address"`
	City         string                 `json:"city"`
	State        string                 `json:"state"`
	Country      string                 `json:"country"`
	PostalCode   string                 `json:"postal_code"`
	Phone        string                 `json:"phone"`
	Email        string                 `json:"email" validate:"omitempty,email"`
	CustomFields map[string]interface{} `json:"custom_fields,omitempty"`
}

// CreateCompany creates a new company
func (uc *CompanyUsecase) CreateCompany(ctx context.Context, req *CreateCompanyRequest, userID uuid.UUID) (*entities.Company, error) {
	// Check if company with name already exists
	existingCompany, _ := uc.companyRepo.GetByName(ctx, req.Name)
	if existingCompany != nil {
		return nil, errors.New("company already exists with this name")
	}

	company := &entities.Company{
		Name:         req.Name,
		Website:      req.Website,
		Industry:     req.Industry,
		Size:         req.Size,
		Address:      req.Address,
		City:         req.City,
		State:        req.State,
		Country:      req.Country,
		PostalCode:   req.PostalCode,
		Phone:        req.Phone,
		Email:        req.Email,
		CustomFields: entities.CustomFields(req.CustomFields),
	}

	if err := uc.companyRepo.Create(ctx, company); err != nil {
		return nil, err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeCreated,
		Title:       "Company created",
		Description: "Company '" + company.Name + "' was created",
		CompanyID:   &company.ID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishCompanyCreated(company)

	return company, nil
}

// UpdateCompanyRequest represents company update request
type UpdateCompanyRequest struct {
	Name         *string                `json:"name,omitempty"`
	Website      *string                `json:"website,omitempty" validate:"omitempty,url"`
	Industry     *string                `json:"industry,omitempty"`
	Size         *string                `json:"size,omitempty"`
	Address      *string                `json:"address,omitempty"`
	City         *string                `json:"city,omitempty"`
	State        *string                `json:"state,omitempty"`
	Country      *string                `json:"country,omitempty"`
	PostalCode   *string                `json:"postal_code,omitempty"`
	Phone        *string                `json:"phone,omitempty"`
	Email        *string                `json:"email,omitempty" validate:"omitempty,email"`
	CustomFields map[string]interface{} `json:"custom_fields,omitempty"`
}

// UpdateCompany updates an existing company
func (uc *CompanyUsecase) UpdateCompany(ctx context.Context, companyID uuid.UUID, req *UpdateCompanyRequest, userID uuid.UUID) (*entities.Company, error) {
	company, err := uc.companyRepo.GetByID(ctx, companyID)
	if err != nil {
		return nil, err
	}

	// Check if name is being changed and if it's already taken
	if req.Name != nil && *req.Name != company.Name && *req.Name != "" {
		existingCompany, _ := uc.companyRepo.GetByName(ctx, *req.Name)
		if existingCompany != nil && existingCompany.ID != companyID {
			return nil, errors.New("company already exists with this name")
		}
	}

	// Update fields
	if req.Name != nil {
		company.Name = *req.Name
	}
	if req.Website != nil {
		company.Website = *req.Website
	}
	if req.Industry != nil {
		company.Industry = *req.Industry
	}
	if req.Size != nil {
		company.Size = *req.Size
	}
	if req.Address != nil {
		company.Address = *req.Address
	}
	if req.City != nil {
		company.City = *req.City
	}
	if req.State != nil {
		company.State = *req.State
	}
	if req.Country != nil {
		company.Country = *req.Country
	}
	if req.PostalCode != nil {
		company.PostalCode = *req.PostalCode
	}
	if req.Phone != nil {
		company.Phone = *req.Phone
	}
	if req.Email != nil {
		company.Email = *req.Email
	}

	// Update custom fields
	if req.CustomFields != nil {
		if company.CustomFields == nil {
			company.CustomFields = make(entities.CustomFields)
		}
		for key, value := range req.CustomFields {
			company.CustomFields[key] = value
		}
	}

	if err := uc.companyRepo.Update(ctx, company); err != nil {
		return nil, err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeUpdated,
		Title:       "Company updated",
		Description: "Company '" + company.Name + "' was updated",
		CompanyID:   &company.ID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishCompanyUpdated(company)

	return company, nil
}

// GetCompany retrieves a company with all relations
func (uc *CompanyUsecase) GetCompany(ctx context.Context, companyID uuid.UUID) (*entities.Company, error) {
	return uc.companyRepo.GetByIDWithRelations(ctx, companyID)
}

// ListCompanies lists companies with filtering
func (uc *CompanyUsecase) ListCompanies(ctx context.Context, filters repositories.CompanyFilters, limit, offset int) ([]entities.Company, int64, error) {
	return uc.companyRepo.List(ctx, filters, limit, offset)
}

// SearchCompanies searches companies by query
func (uc *CompanyUsecase) SearchCompanies(ctx context.Context, query string, limit, offset int) ([]entities.Company, int64, error) {
	return uc.companyRepo.Search(ctx, query, limit, offset)
}

// DeleteCompany deletes a company and handles cascading effects
func (uc *CompanyUsecase) DeleteCompany(ctx context.Context, companyID uuid.UUID, userID uuid.UUID) error {
	company, err := uc.companyRepo.GetByID(ctx, companyID)
	if err != nil {
		return err
	}

	// Check if company has contacts - we'll set their company_id to null instead of deleting
	contacts, _, err := uc.contactRepo.ListByCompanyID(ctx, companyID, 1000, 0)
	if err != nil {
		return err
	}

	// Update contacts to remove company association
	for _, contact := range contacts {
		contact.CompanyID = nil
		if err := uc.contactRepo.Update(ctx, &contact); err != nil {
			return err
		}
	}

	if err := uc.companyRepo.Delete(ctx, companyID); err != nil {
		return err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeUpdated,
		Title:       "Company deleted",
		Description: "Company '" + company.Name + "' was deleted",
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishCompanyDeleted(company)

	return nil
}

// UpdateCustomFields updates only custom fields of a company
func (uc *CompanyUsecase) UpdateCustomFields(ctx context.Context, companyID uuid.UUID, fields map[string]interface{}, userID uuid.UUID) error {
	customFields := entities.CustomFields(fields)
	
	if err := uc.companyRepo.UpdateCustomFields(ctx, companyID, customFields); err != nil {
		return err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeUpdated,
		Title:       "Company custom fields updated",
		Description: "Custom fields were updated",
		CompanyID:   &companyID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	return nil
}

// GetCompaniesByIndustry retrieves companies for a specific industry
func (uc *CompanyUsecase) GetCompaniesByIndustry(ctx context.Context, industry string, limit, offset int) ([]entities.Company, int64, error) {
	return uc.companyRepo.GetByIndustry(ctx, industry, limit, offset)
}

// GetCompanyContacts retrieves contacts for a specific company
func (uc *CompanyUsecase) GetCompanyContacts(ctx context.Context, companyID uuid.UUID, limit, offset int) ([]entities.Contact, int64, error) {
	// Verify company exists
	_, err := uc.companyRepo.GetByID(ctx, companyID)
	if err != nil {
		return nil, 0, err
	}

	return uc.contactRepo.ListByCompanyID(ctx, companyID, limit, offset)
}