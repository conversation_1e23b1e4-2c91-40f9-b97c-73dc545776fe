package usecases

import (
	"context"
	"errors"
	"fmt"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

type CommentUseCase struct {
	commentRepo  repositories.CommentRepository
	cardRepo     repositories.CardRepository
	contactRepo  repositories.ContactRepository
	companyRepo  repositories.CompanyRepository
	userRepo     repositories.UserRepository
	activityRepo repositories.ActivityRepository
}

func NewCommentUseCase(
	commentRepo repositories.CommentRepository,
	cardRepo repositories.CardRepository,
	contactRepo repositories.ContactRepository,
	companyRepo repositories.CompanyRepository,
	userRepo repositories.UserRepository,
	activityRepo repositories.ActivityRepository,
) *CommentUseCase {
	return &CommentUseCase{
		commentRepo:  commentRepo,
		cardRepo:     cardRepo,
		contactRepo:  contactRepo,
		companyRepo:  companyRepo,
		userRepo:     userRepo,
		activityRepo: activityRepo,
	}
}

type CreateCommentInput struct {
	Content        string    `json:"content" validate:"required"`
	EntityType     string    `json:"entity_type" validate:"required,oneof=card contact company"`
	EntityID       uuid.UUID `json:"entity_id" validate:"required"`
	IsInternal     bool      `json:"is_internal"`
	ParentID       *uuid.UUID `json:"parent_id,omitempty"`
	MentionedUsers []uuid.UUID `json:"mentioned_users,omitempty"`
}

func (uc *CommentUseCase) CreateComment(ctx context.Context, userID uuid.UUID, input CreateCommentInput) (*entities.Comment, error) {
	// Verify entity exists
	if err := uc.verifyEntityExists(ctx, input.EntityType, input.EntityID); err != nil {
		return nil, err
	}

	// Create comment
	comment := &entities.Comment{
		Content:    input.Content,
		IsInternal: input.IsInternal,
		UserID:     userID,
		ParentID:   input.ParentID,
	}
	comment.SetEntity(input.EntityType, input.EntityID)

	// Set mentioned users if provided
	if len(input.MentionedUsers) > 0 {
		users := make([]entities.User, 0, len(input.MentionedUsers))
		for _, uid := range input.MentionedUsers {
			user, err := uc.userRepo.GetByID(ctx, uid)
			if err == nil && user != nil {
				users = append(users, *user)
			}
		}
		comment.MentionedUsers = users
	}

	// Create the comment
	if err := uc.commentRepo.Create(ctx, comment); err != nil {
		return nil, fmt.Errorf("failed to create comment: %w", err)
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeNote,
		Title:       fmt.Sprintf("Comment added on %s", input.EntityType),
		Description: fmt.Sprintf("Added comment on %s", input.EntityType),
		UserID:      &userID,
		Metadata: entities.CustomFields{
			"comment_id": comment.ID.String(),
			"content":    comment.Content,
			"entity_type": input.EntityType,
			"entity_id":   input.EntityID.String(),
		},
	}
	
	switch input.EntityType {
	case "card":
		activity.CardID = &input.EntityID
	case "contact":
		activity.ContactID = &input.EntityID
	case "company":
		activity.CompanyID = &input.EntityID
	}

	_ = uc.activityRepo.Create(ctx, activity)

	// Reload with associations
	return uc.commentRepo.GetByID(ctx, comment.ID)
}

func (uc *CommentUseCase) GetComment(ctx context.Context, id uuid.UUID) (*entities.Comment, error) {
	return uc.commentRepo.GetByID(ctx, id)
}

func (uc *CommentUseCase) GetCardComments(ctx context.Context, cardID uuid.UUID) ([]*entities.Comment, error) {
	return uc.commentRepo.GetByCardID(ctx, cardID)
}

func (uc *CommentUseCase) GetEntityComments(ctx context.Context, entityType string, entityID uuid.UUID) ([]*entities.Comment, error) {
	return uc.commentRepo.GetByEntityWithReplies(ctx, entityType, entityID)
}

type UpdateCommentInput struct {
	Content string `json:"content" validate:"required"`
}

func (uc *CommentUseCase) UpdateComment(ctx context.Context, id uuid.UUID, userID uuid.UUID, input UpdateCommentInput) (*entities.Comment, error) {
	comment, err := uc.commentRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("comment not found: %w", err)
	}

	// Check if user can edit (must be the author)
	if comment.UserID != userID {
		return nil, errors.New("only the author can edit this comment")
	}

	// Create edit history
	if comment.Content != input.Content {
		editHistory := &entities.CommentEdit{
			CommentID:  comment.ID,
			OldContent: comment.Content,
			NewContent: input.Content,
			EditedByID: userID,
		}
		_ = uc.commentRepo.CreateEditHistory(ctx, editHistory)

		// Update comment
		comment.Content = input.Content
		now := time.Now()
		comment.EditedAt = &now
		comment.EditCount++

		if err := uc.commentRepo.Update(ctx, comment); err != nil {
			return nil, fmt.Errorf("failed to update comment: %w", err)
		}
	}

	return comment, nil
}

func (uc *CommentUseCase) DeleteComment(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	comment, err := uc.commentRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("comment not found: %w", err)
	}

	// Check if user can delete (must be the author)
	if comment.UserID != userID {
		return errors.New("only the author can delete this comment")
	}

	// Delete all replies first
	replies, _ := uc.commentRepo.GetReplies(ctx, id)
	for _, reply := range replies {
		_ = uc.commentRepo.Delete(ctx, reply.ID)
	}

	return uc.commentRepo.Delete(ctx, id)
}

func (uc *CommentUseCase) PinComment(ctx context.Context, id uuid.UUID) error {
	return uc.commentRepo.PinComment(ctx, id)
}

func (uc *CommentUseCase) UnpinComment(ctx context.Context, id uuid.UUID) error {
	return uc.commentRepo.UnpinComment(ctx, id)
}

func (uc *CommentUseCase) AddReaction(ctx context.Context, commentID uuid.UUID, userID uuid.UUID, reaction string) error {
	return uc.commentRepo.AddReaction(ctx, commentID, userID.String(), reaction)
}

func (uc *CommentUseCase) RemoveReaction(ctx context.Context, commentID uuid.UUID, userID uuid.UUID, reaction string) error {
	return uc.commentRepo.RemoveReaction(ctx, commentID, userID.String(), reaction)
}

func (uc *CommentUseCase) GetCommentEditHistory(ctx context.Context, commentID uuid.UUID) ([]*entities.CommentEdit, error) {
	return uc.commentRepo.GetEditHistory(ctx, commentID)
}

func (uc *CommentUseCase) verifyEntityExists(ctx context.Context, entityType string, entityID uuid.UUID) error {
	switch entityType {
	case "card":
		_, err := uc.cardRepo.GetByID(ctx, entityID)
		if err != nil {
			return fmt.Errorf("card not found: %w", err)
		}
	case "contact":
		_, err := uc.contactRepo.GetByID(ctx, entityID)
		if err != nil {
			return fmt.Errorf("contact not found: %w", err)
		}
	case "company":
		_, err := uc.companyRepo.GetByID(ctx, entityID)
		if err != nil {
			return fmt.Errorf("company not found: %w", err)
		}
	default:
		return fmt.Errorf("invalid entity type: %s", entityType)
	}
	return nil
}