package usecases

import (
	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// EventBus defines the interface for publishing domain events
type EventBus interface {
	// Card events
	PublishCardCreated(card *entities.Card)
	PublishCardUpdated(card *entities.Card)
	PublishCardDeleted(card *entities.Card)
	PublishCardStageChanged(card *entities.Card, oldStageID *uuid.UUID, newStageID *uuid.UUID)

	// Contact events
	PublishContactCreated(contact *entities.Contact)
	PublishContactUpdated(contact *entities.Contact)
	PublishContactDeleted(contact *entities.Contact)

	// Company events
	PublishCompanyCreated(company *entities.Company)
	PublishCompanyUpdated(company *entities.Company)
	PublishCompanyDeleted(company *entities.Company)

	// Pipeline events
	PublishPipelineCreated(pipeline *entities.Pipeline)
	PublishPipelineUpdated(pipeline *entities.Pipeline)
	PublishPipelineDeleted(pipeline *entities.Pipeline)

	// Activity events
	PublishActivityCreated(activity *entities.Activity)
	PublishActivityUpdated(activity *entities.Activity)
	PublishActivityCompleted(activity *entities.Activity)
}