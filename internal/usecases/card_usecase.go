package usecases

import (
	"context"
	"errors"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

// CardUsecase handles card business logic
type CardUsecase struct {
	cardRepo     repositories.CardRepository
	stageRepo    repositories.StageRepository
	activityRepo repositories.ActivityRepository
	eventBus     EventBus
}

// NewCardUsecase creates a new card usecase
func NewCardUsecase(
	cardRepo repositories.CardRepository,
	stageRepo repositories.StageRepository,
	activityRepo repositories.ActivityRepository,
	eventBus EventBus,
) *CardUsecase {
	return &CardUsecase{
		cardRepo:     cardRepo,
		stageRepo:    stageRepo,
		activityRepo: activityRepo,
		eventBus:     eventBus,
	}
}

// CreateCardRequest represents card creation request
type CreateCardRequest struct {
	Title             string                 `json:"title" validate:"required"`
	Description       string                 `json:"description"`
	Value             float64                `json:"value"`
	Priority          entities.CardPriority  `json:"priority" validate:"omitempty,oneof=low medium high urgent"`
	StageID           *uuid.UUID             `json:"stage_id,omitempty"`
	ContactID         *uuid.UUID             `json:"contact_id,omitempty"`
	CompanyID         *uuid.UUID             `json:"company_id,omitempty"`
	AssignedToID      *uuid.UUID             `json:"assigned_to_id,omitempty"`
	ExpectedCloseDate *string                `json:"expected_close_date,omitempty"`
	CustomFields      map[string]interface{} `json:"custom_fields,omitempty"`
}

// CreateCard creates a new card
func (uc *CardUsecase) CreateCard(ctx context.Context, req *CreateCardRequest, userID uuid.UUID) (*entities.Card, error) {
	card := &entities.Card{
		Title:        req.Title,
		Description:  req.Description,
		Value:        req.Value,
		Priority:     req.Priority,
		StageID:      req.StageID,
		ContactID:    req.ContactID,
		CompanyID:    req.CompanyID,
		AssignedToID: req.AssignedToID,
		CustomFields: entities.CustomFields(req.CustomFields),
	}

	// Set default priority if not provided
	if card.Priority == "" {
		card.Priority = entities.PriorityMedium
	}

	if err := uc.cardRepo.Create(ctx, card); err != nil {
		return nil, err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeCreated,
		Title:       "Card created",
		Description: "Card '" + card.Title + "' was created",
		CardID:      &card.ID,
		ContactID:   card.ContactID,
		CompanyID:   card.CompanyID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishCardCreated(card)

	return card, nil
}

// UpdateCardRequest represents card update request
type UpdateCardRequest struct {
	Title             *string                `json:"title,omitempty"`
	Description       *string                `json:"description,omitempty"`
	Value             *float64               `json:"value,omitempty"`
	Priority          *entities.CardPriority `json:"priority,omitempty" validate:"omitempty,oneof=low medium high urgent"`
	ContactID         *uuid.UUID             `json:"contact_id,omitempty"`
	CompanyID         *uuid.UUID             `json:"company_id,omitempty"`
	AssignedToID      *uuid.UUID             `json:"assigned_to_id,omitempty"`
	ExpectedCloseDate *string                `json:"expected_close_date,omitempty"`
	CustomFields      map[string]interface{} `json:"custom_fields,omitempty"`
}

// UpdateCard updates an existing card
func (uc *CardUsecase) UpdateCard(ctx context.Context, cardID uuid.UUID, req *UpdateCardRequest, userID uuid.UUID) (*entities.Card, error) {
	card, err := uc.cardRepo.GetByID(ctx, cardID)
	if err != nil {
		return nil, err
	}

	var changes []string
	
	if req.Title != nil && *req.Title != card.Title {
		changes = append(changes, "title")
		card.Title = *req.Title
	}
	
	if req.Description != nil {
		card.Description = *req.Description
	}
	
	if req.Value != nil && *req.Value != card.Value {
		changes = append(changes, "value")
		card.Value = *req.Value
	}
	
	if req.Priority != nil {
		card.Priority = *req.Priority
	}
	
	if req.ContactID != nil {
		card.ContactID = req.ContactID
	}
	
	if req.CompanyID != nil {
		card.CompanyID = req.CompanyID
	}
	
	if req.AssignedToID != nil {
		card.AssignedToID = req.AssignedToID
	}

	// Update custom fields
	if req.CustomFields != nil {
		if card.CustomFields == nil {
			card.CustomFields = make(entities.CustomFields)
		}
		for key, value := range req.CustomFields {
			card.CustomFields[key] = value
		}
	}

	if err := uc.cardRepo.Update(ctx, card); err != nil {
		return nil, err
	}

	// Create activity record if there were changes
	if len(changes) > 0 {
		activity := &entities.Activity{
			Type:        entities.ActivityTypeUpdated,
			Title:       "Card updated",
			Description: "Card '" + card.Title + "' was updated",
			CardID:      &card.ID,
			ContactID:   card.ContactID,
			CompanyID:   card.CompanyID,
			UserID:      &userID,
		}
		uc.activityRepo.Create(ctx, activity)
	}

	// Publish event
	uc.eventBus.PublishCardUpdated(card)

	return card, nil
}

// MoveCardToStage moves a card to a different stage
func (uc *CardUsecase) MoveCardToStage(ctx context.Context, cardID, stageID uuid.UUID, userID uuid.UUID) (*entities.Card, error) {
	card, err := uc.cardRepo.GetByID(ctx, cardID)
	if err != nil {
		return nil, err
	}

	// Verify stage exists
	stage, err := uc.stageRepo.GetByID(ctx, stageID)
	if err != nil {
		return nil, errors.New("stage not found")
	}

	oldStageID := card.StageID
	
	if err := uc.cardRepo.MoveToStage(ctx, cardID, stageID); err != nil {
		return nil, err
	}

	card.StageID = &stageID

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeStageChange,
		Title:       "Card moved to " + stage.Name,
		Description: "Card '" + card.Title + "' was moved to stage '" + stage.Name + "'",
		CardID:      &card.ID,
		ContactID:   card.ContactID,
		CompanyID:   card.CompanyID,
		UserID:      &userID,
	}
	activity.SetMetadata("old_stage_id", oldStageID)
	activity.SetMetadata("new_stage_id", stageID)
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishCardStageChanged(card, oldStageID, &stageID)

	return card, nil
}

// GetCard retrieves a card with all relations
func (uc *CardUsecase) GetCard(ctx context.Context, cardID uuid.UUID) (*entities.Card, error) {
	return uc.cardRepo.GetByIDWithRelations(ctx, cardID)
}

// ListCards lists cards with filtering
func (uc *CardUsecase) ListCards(ctx context.Context, filters repositories.CardFilters, limit, offset int) ([]entities.Card, int64, error) {
	return uc.cardRepo.List(ctx, filters, limit, offset)
}

// DeleteCard deletes a card
func (uc *CardUsecase) DeleteCard(ctx context.Context, cardID uuid.UUID, userID uuid.UUID) error {
	card, err := uc.cardRepo.GetByID(ctx, cardID)
	if err != nil {
		return err
	}

	if err := uc.cardRepo.Delete(ctx, cardID); err != nil {
		return err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeUpdated,
		Title:       "Card deleted",
		Description: "Card '" + card.Title + "' was deleted",
		ContactID:   card.ContactID,
		CompanyID:   card.CompanyID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	// Publish event
	uc.eventBus.PublishCardDeleted(card)

	return nil
}

// GetOverdueCards retrieves overdue cards
func (uc *CardUsecase) GetOverdueCards(ctx context.Context, limit, offset int) ([]entities.Card, int64, error) {
	return uc.cardRepo.GetOverdueCards(ctx, limit, offset)
}

// UpdateCustomFields updates only custom fields of a card
func (uc *CardUsecase) UpdateCustomFields(ctx context.Context, cardID uuid.UUID, fields map[string]interface{}, userID uuid.UUID) error {
	customFields := entities.CustomFields(fields)
	
	if err := uc.cardRepo.UpdateCustomFields(ctx, cardID, customFields); err != nil {
		return err
	}

	// Create activity record
	activity := &entities.Activity{
		Type:        entities.ActivityTypeUpdated,
		Title:       "Card custom fields updated",
		Description: "Custom fields were updated",
		CardID:      &cardID,
		UserID:      &userID,
	}
	uc.activityRepo.Create(ctx, activity)

	return nil
}