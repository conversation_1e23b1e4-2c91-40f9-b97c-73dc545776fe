package usecases

import (
	"context"
	"errors"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

var (
	ErrInvalidCredentials = errors.New("invalid credentials")
	ErrUserNotFound      = errors.New("user not found")
	ErrUserInactive      = errors.New("user is inactive")
)

// AuthUsecase handles authentication business logic
type AuthUsecase struct {
	userRepo    repositories.UserRepository
	jwtSecret   string
	redisClient RedisClient // Interface for Redis operations
}

// RedisClient interface for Redis operations
type RedisClient interface {
	BlacklistToken(token string, expiration time.Duration) error
	IsTokenBlacklisted(token string) (bool, error)
}

// NewAuthUsecase creates a new auth usecase
func NewAuthUsecase(userRepo repositories.UserRepository, jwtSecret string) *AuthUsecase {
	return &AuthUsecase{
		userRepo:  userRepo,
		jwtSecret: jwtSecret,
	}
}

// NewAuthUsecaseWithRedis creates a new auth usecase with Redis support
func NewAuthUsecaseWithRedis(userRepo repositories.UserRepository, jwtSecret string, redisClient RedisClient) *AuthUsecase {
	return &AuthUsecase{
		userRepo:    userRepo,
		jwtSecret:   jwtSecret,
		redisClient: redisClient,
	}
}

// LoginRequest represents login request data
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse represents login response data
type LoginResponse struct {
	Token string         `json:"token"`
	User  *entities.User `json:"user"`
}

// Login authenticates a user and returns a JWT token
func (uc *AuthUsecase) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
	user, err := uc.userRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	if !user.IsActive {
		return nil, ErrUserInactive
	}

	if !user.CheckPassword(req.Password) {
		return nil, ErrInvalidCredentials
	}

	token, err := uc.generateToken(user)
	if err != nil {
		return nil, err
	}

	return &LoginResponse{
		Token: token,
		User:  user,
	}, nil
}

// RegisterRequest represents registration request data
type RegisterRequest struct {
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required,min=8"`
	FirstName string `json:"first_name" validate:"required"`
	LastName  string `json:"last_name" validate:"required"`
	Role      string `json:"role,omitempty" validate:"omitempty,oneof=admin user"`
}

// Register creates a new user account
func (uc *AuthUsecase) Register(ctx context.Context, req *RegisterRequest) (*entities.User, error) {
	// Check if user already exists
	existingUser, _ := uc.userRepo.GetByEmail(ctx, req.Email)
	if existingUser != nil {
		return nil, errors.New("user already exists with this email")
	}

	// Set default role if not provided
	if req.Role == "" {
		req.Role = "user"
	}

	user := &entities.User{
		Email:       req.Email,
		Password:    req.Password,
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		LegacyRole:  req.Role,
		IsActive:    true,
	}

	if err := uc.userRepo.Create(ctx, user); err != nil {
		return nil, err
	}

	return user, nil
}

// ValidateToken validates a JWT token and returns the user
func (uc *AuthUsecase) ValidateToken(tokenString string) (*entities.User, error) {
	// Check if token is blacklisted (if Redis is available)
	if uc.redisClient != nil {
		blacklisted, err := uc.redisClient.IsTokenBlacklisted(tokenString)
		if err != nil {
			// Log error but continue validation (fail open for availability)
			// In production, you might want to fail closed for security
		} else if blacklisted {
			return nil, errors.New("token has been revoked")
		}
	}
	
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("invalid signing method")
		}
		return []byte(uc.jwtSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	userIDStr, ok := claims["user_id"].(string)
	if !ok {
		return nil, errors.New("invalid user ID in token")
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, errors.New("invalid user ID format")
	}

	user, err := uc.userRepo.GetByID(context.Background(), userID)
	if err != nil {
		return nil, ErrUserNotFound
	}

	if !user.IsActive {
		return nil, ErrUserInactive
	}

	return user, nil
}

// generateToken generates a JWT token for the user
func (uc *AuthUsecase) generateToken(user *entities.User) (string, error) {
	claims := jwt.MapClaims{
		"user_id":    user.ID.String(),
		"email":      user.Email,
		"role":       user.LegacyRole,
		"first_name": user.FirstName,
		"last_name":  user.LastName,
		"exp":        time.Now().Add(time.Hour * 24 * 7).Unix(), // 7 days
		"iat":        time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(uc.jwtSecret))
}

// RefreshToken refreshes an existing JWT token
func (uc *AuthUsecase) RefreshToken(ctx context.Context, tokenString string) (string, error) {
	user, err := uc.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}

	return uc.generateToken(user)
}

// Logout blacklists a token to invalidate it
func (uc *AuthUsecase) Logout(ctx context.Context, tokenString string) error {
	if uc.redisClient == nil {
		// If Redis is not available, we can't blacklist tokens
		// In a production system, you might want to return an error here
		return nil
	}
	
	// Parse token to get expiration time
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("invalid signing method")
		}
		return []byte(uc.jwtSecret), nil
	})
	
	if err != nil {
		// Even if token parsing fails, we might want to blacklist it
		// Default to 7 days expiration
		return uc.redisClient.BlacklistToken(tokenString, time.Hour*24*7)
	}
	
	// Get expiration from token claims
	if claims, ok := token.Claims.(jwt.MapClaims); ok {
		if exp, ok := claims["exp"].(float64); ok {
			expTime := time.Unix(int64(exp), 0)
			remaining := time.Until(expTime)
			if remaining > 0 {
				return uc.redisClient.BlacklistToken(tokenString, remaining)
			}
		}
	}
	
	// Default to 7 days if we can't determine expiration
	return uc.redisClient.BlacklistToken(tokenString, time.Hour*24*7)
}