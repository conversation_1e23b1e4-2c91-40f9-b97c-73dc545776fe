package usecases

import (
	"context"
	"errors"
	"fmt"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

type UserManagementUsecase struct {
	userRepo repositories.UserRepository
	roleRepo repositories.RoleRepository
}

func NewUserManagementUsecase(
	userRepo repositories.UserRepository,
	roleRepo repositories.RoleRepository,
) *UserManagementUsecase {
	return &UserManagementUsecase{
		userRepo: userRepo,
		roleRepo: roleRepo,
	}
}

// C<PERSON><PERSON><PERSON> creates a new user with a role
func (u *UserManagementUsecase) CreateUser(ctx context.Context, user *entities.User, roleID *uuid.UUID) (*entities.User, error) {
	// Check if email exists
	existing, err := u.userRepo.GetByEmail(ctx, user.Email)
	if err == nil && existing != nil {
		return nil, errors.New("email already exists")
	}

	// Validate role if provided
	if roleID != nil {
		role, err := u.roleRepo.GetByID(ctx, *roleID)
		if err != nil {
			return nil, fmt.Errorf("failed to get role: %w", err)
		}
		if role == nil {
			return nil, errors.New("role not found")
		}
		if !role.IsActive {
			return nil, errors.New("role is not active")
		}
		user.RoleID = roleID
	}

	// Create user
	if err := u.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Reload user with role
	return u.userRepo.GetByID(ctx, user.ID)
}

// UpdateUser updates user information
func (u *UserManagementUsecase) UpdateUser(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*entities.User, error) {
	// Get existing user
	user, err := u.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// Apply updates
	if email, ok := updates["email"].(string); ok {
		// Check if new email is unique
		existing, err := u.userRepo.GetByEmail(ctx, email)
		if err == nil && existing != nil && existing.ID != id {
			return nil, errors.New("email already exists")
		}
		user.Email = email
	}

	if firstName, ok := updates["first_name"].(string); ok {
		user.FirstName = firstName
	}

	if lastName, ok := updates["last_name"].(string); ok {
		user.LastName = lastName
	}

	if phone, ok := updates["phone"].(string); ok {
		user.Phone = phone
	}

	if department, ok := updates["department"].(string); ok {
		user.Department = department
	}

	if position, ok := updates["position"].(string); ok {
		user.Position = position
	}

	if location, ok := updates["location"].(string); ok {
		user.Location = location
	}

	if timezone, ok := updates["timezone"].(string); ok {
		user.Timezone = timezone
	}

	if language, ok := updates["language"].(string); ok {
		user.Language = language
	}

	if avatar, ok := updates["avatar"].(string); ok {
		user.Avatar = avatar
	}

	if isActive, ok := updates["is_active"].(bool); ok {
		user.IsActive = isActive
	}

	if emailVerified, ok := updates["email_verified"].(bool); ok {
		user.EmailVerified = emailVerified
	}

	if twoFactorEnabled, ok := updates["two_factor_enabled"].(bool); ok {
		user.TwoFactorEnabled = twoFactorEnabled
	}

	// Handle role update
	if roleIDStr, ok := updates["role_id"].(string); ok {
		if roleIDStr == "" {
			user.RoleID = nil
		} else {
			roleID, err := uuid.Parse(roleIDStr)
			if err != nil {
				return nil, fmt.Errorf("invalid role ID: %w", err)
			}

			// Validate role
			role, err := u.roleRepo.GetByID(ctx, roleID)
			if err != nil {
				return nil, fmt.Errorf("failed to get role: %w", err)
			}
			if role == nil {
				return nil, errors.New("role not found")
			}
			if !role.IsActive {
				return nil, errors.New("role is not active")
			}

			user.RoleID = &roleID
		}
	}

	// Update user
	if err := u.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Reload user with role
	return u.userRepo.GetByID(ctx, id)
}

// DeleteUser deletes a user
func (u *UserManagementUsecase) DeleteUser(ctx context.Context, id uuid.UUID, currentUserID uuid.UUID) error {
	// Prevent self-deletion
	if id == currentUserID {
		return errors.New("cannot delete your own account")
	}

	// Get user
	user, err := u.userRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}

	// Delete user
	return u.userRepo.Delete(ctx, id)
}

// GetUser gets a user by ID
func (u *UserManagementUsecase) GetUser(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	user, err := u.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("user not found")
	}
	return user, nil
}

// ListUsers lists users with pagination and filters
func (u *UserManagementUsecase) ListUsers(ctx context.Context, limit, offset int, filters map[string]interface{}) ([]entities.User, int64, error) {
	// TODO: Implement filtering in repository
	return u.userRepo.List(ctx, limit, offset)
}

// AssignRole assigns a role to a user
func (u *UserManagementUsecase) AssignRole(ctx context.Context, userID, roleID uuid.UUID) error {
	// Get user
	user, err := u.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}

	// Get role
	role, err := u.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return errors.New("role not found")
	}
	if !role.IsActive {
		return errors.New("role is not active")
	}

	// Assign role
	return u.roleRepo.AssignUserToRole(ctx, userID, roleID)
}

// RemoveRole removes role from a user
func (u *UserManagementUsecase) RemoveRole(ctx context.Context, userID uuid.UUID) error {
	// Get user
	user, err := u.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}

	// Remove role
	return u.roleRepo.RemoveUserFromRole(ctx, userID)
}

// ToggleUserStatus toggles user active status
func (u *UserManagementUsecase) ToggleUserStatus(ctx context.Context, userID uuid.UUID) (*entities.User, error) {
	// Get user
	user, err := u.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// Toggle status
	user.IsActive = !user.IsActive
	if err := u.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

// UpdatePassword updates user password
func (u *UserManagementUsecase) UpdatePassword(ctx context.Context, userID uuid.UUID, newPassword string) error {
	// Get user
	user, err := u.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}

	// Validate password
	if len(newPassword) < 8 {
		return errors.New("password must be at least 8 characters")
	}

	// Set new password (will be hashed by BeforeCreate hook)
	user.SetPassword(newPassword)

	// Update user
	return u.userRepo.Update(ctx, user)
}

// UpdateLastLogin updates user's last login time
func (u *UserManagementUsecase) UpdateLastLogin(ctx context.Context, userID uuid.UUID) error {
	// Get user
	user, err := u.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}

	// Update last login
	user.UpdateLastLogin()

	// Update user
	return u.userRepo.Update(ctx, user)
}

// UpdateLastActivity updates user's last activity time
func (u *UserManagementUsecase) UpdateLastActivity(ctx context.Context, userID uuid.UUID) error {
	// Get user
	user, err := u.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}

	// Update activity
	user.UpdateActivity()

	// Update user
	return u.userRepo.Update(ctx, user)
}

// GetUsersByRole gets all users with a specific role
func (u *UserManagementUsecase) GetUsersByRole(ctx context.Context, roleID uuid.UUID) ([]entities.User, error) {
	// Get role
	role, err := u.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return nil, errors.New("role not found")
	}

	return u.roleRepo.GetRoleUsers(ctx, roleID)
}

// BulkAssignRole assigns a role to multiple users
func (u *UserManagementUsecase) BulkAssignRole(ctx context.Context, userIDs []uuid.UUID, roleID uuid.UUID) error {
	// Get role
	role, err := u.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return errors.New("role not found")
	}
	if !role.IsActive {
		return errors.New("role is not active")
	}

	// Assign role to each user
	for _, userID := range userIDs {
		if err := u.roleRepo.AssignUserToRole(ctx, userID, roleID); err != nil {
			return fmt.Errorf("failed to assign role to user %s: %w", userID, err)
		}
	}

	return nil
}

// BulkDeleteUsers deletes multiple users
func (u *UserManagementUsecase) BulkDeleteUsers(ctx context.Context, userIDs []uuid.UUID, currentUserID uuid.UUID) error {
	// Check if current user is in the list
	for _, id := range userIDs {
		if id == currentUserID {
			return errors.New("cannot delete your own account")
		}
	}

	// Delete each user
	for _, id := range userIDs {
		if err := u.userRepo.Delete(ctx, id); err != nil {
			return fmt.Errorf("failed to delete user %s: %w", id, err)
		}
	}

	return nil
}

// GetUserStatistics gets user statistics
func (u *UserManagementUsecase) GetUserStatistics(ctx context.Context) (map[string]interface{}, error) {
	// Get total users
	users, total, err := u.userRepo.List(ctx, 0, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// Count active users
	activeCount := 0
	verifiedCount := 0
	twoFactorCount := 0
	recentlyActiveCount := 0
	
	now := time.Now()
	weekAgo := now.AddDate(0, 0, -7)

	for _, user := range users {
		if user.IsActive {
			activeCount++
		}
		if user.EmailVerified {
			verifiedCount++
		}
		if user.TwoFactorEnabled {
			twoFactorCount++
		}
		if user.LastActivityAt != nil && user.LastActivityAt.After(weekAgo) {
			recentlyActiveCount++
		}
	}

	// Get role distribution
	roles, _, err := u.roleRepo.List(ctx, 0, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get roles: %w", err)
	}

	roleDistribution := make(map[string]int64)
	for _, role := range roles {
		count, _ := u.roleRepo.CountUsersInRole(ctx, role.ID)
		roleDistribution[role.Name] = count
	}

	return map[string]interface{}{
		"total_users":          total,
		"active_users":         activeCount,
		"verified_users":       verifiedCount,
		"two_factor_users":     twoFactorCount,
		"recently_active":      recentlyActiveCount,
		"role_distribution":    roleDistribution,
	}, nil
}