package usecases

import (
	"context"
	"errors"
	"fmt"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

type RoleUsecase struct {
	roleRepo       repositories.RoleRepository
	permissionRepo repositories.PermissionRepository
	userRepo       repositories.UserRepository
}

func NewRoleUsecase(
	roleRepo repositories.RoleRepository,
	permissionRepo repositories.PermissionRepository,
	userRepo repositories.UserRepository,
) *RoleUsecase {
	return &RoleUsecase{
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
		userRepo:       userRepo,
	}
}

// CreateRole creates a new role
func (u *RoleUsecase) CreateRole(ctx context.Context, role *entities.Role, permissionIDs []uuid.UUID) (*entities.Role, error) {
	// Check if role with same name exists
	existing, err := u.roleRepo.GetByName(ctx, role.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing role: %w", err)
	}
	if existing != nil {
		return nil, errors.New("role with this name already exists")
	}

	// Create the role
	if err := u.roleRepo.Create(ctx, role); err != nil {
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	// Assign permissions if provided
	if len(permissionIDs) > 0 {
		if err := u.roleRepo.AssignPermissions(ctx, role.ID, permissionIDs); err != nil {
			return nil, fmt.Errorf("failed to assign permissions: %w", err)
		}
	}

	// Reload role with permissions
	return u.roleRepo.GetByID(ctx, role.ID)
}

// UpdateRole updates an existing role
func (u *RoleUsecase) UpdateRole(ctx context.Context, id uuid.UUID, updates map[string]interface{}, permissionIDs []uuid.UUID) (*entities.Role, error) {
	// Get existing role
	role, err := u.roleRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return nil, errors.New("role not found")
	}

	// Check if system role
	if role.IsSystem {
		return nil, errors.New("cannot modify system role")
	}

	// Apply updates
	if name, ok := updates["name"].(string); ok {
		// Check if new name is unique
		existing, err := u.roleRepo.GetByName(ctx, name)
		if err != nil {
			return nil, fmt.Errorf("failed to check role name: %w", err)
		}
		if existing != nil && existing.ID != id {
			return nil, errors.New("role name already exists")
		}
		role.Name = name
	}

	if desc, ok := updates["description"].(string); ok {
		role.Description = desc
	}

	if color, ok := updates["color"].(string); ok {
		role.Color = color
	}

	if active, ok := updates["is_active"].(bool); ok {
		role.IsActive = active
	}

	// Update role
	if err := u.roleRepo.Update(ctx, role); err != nil {
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	// Update permissions if provided
	if permissionIDs != nil {
		if err := u.roleRepo.ReplacePermissions(ctx, id, permissionIDs); err != nil {
			return nil, fmt.Errorf("failed to update permissions: %w", err)
		}
	}

	// Reload role with permissions
	return u.roleRepo.GetByID(ctx, id)
}

// DeleteRole deletes a role
func (u *RoleUsecase) DeleteRole(ctx context.Context, id uuid.UUID) error {
	// Get role
	role, err := u.roleRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return errors.New("role not found")
	}

	// Check if system role
	if role.IsSystem {
		return errors.New("cannot delete system role")
	}

	// Check if role has users
	count, err := u.roleRepo.CountUsersInRole(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to count users: %w", err)
	}
	if count > 0 {
		return fmt.Errorf("cannot delete role: %d users are assigned to this role", count)
	}

	// Delete role
	return u.roleRepo.Delete(ctx, id)
}

// GetRole gets a role by ID
func (u *RoleUsecase) GetRole(ctx context.Context, id uuid.UUID) (*entities.Role, error) {
	role, err := u.roleRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return nil, errors.New("role not found")
	}
	return role, nil
}

// ListRoles lists all roles with pagination
func (u *RoleUsecase) ListRoles(ctx context.Context, limit, offset int) ([]entities.Role, int64, error) {
	return u.roleRepo.List(ctx, limit, offset)
}

// AssignPermissionsToRole assigns permissions to a role
func (u *RoleUsecase) AssignPermissionsToRole(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error {
	// Get role
	role, err := u.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return errors.New("role not found")
	}

	// Check if system role
	if role.IsSystem {
		return errors.New("cannot modify permissions for system role")
	}

	// Verify all permissions exist
	permissions, err := u.permissionRepo.GetByIDs(ctx, permissionIDs)
	if err != nil {
		return fmt.Errorf("failed to get permissions: %w", err)
	}
	if len(permissions) != len(permissionIDs) {
		return errors.New("some permissions do not exist")
	}

	// Replace permissions
	return u.roleRepo.ReplacePermissions(ctx, roleID, permissionIDs)
}

// GetRolePermissions gets all permissions for a role
func (u *RoleUsecase) GetRolePermissions(ctx context.Context, roleID uuid.UUID) ([]entities.Permission, error) {
	// Get role
	role, err := u.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return nil, errors.New("role not found")
	}

	return u.roleRepo.GetRolePermissions(ctx, roleID)
}

// CheckUserPermission checks if a user has a specific permission
func (u *RoleUsecase) CheckUserPermission(ctx context.Context, userID uuid.UUID, resource, action string) (bool, error) {
	// Get user with role
	user, err := u.userRepo.GetByID(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return false, errors.New("user not found")
	}

	return user.HasPermission(resource, action), nil
}

// GetUserPermissions gets all permissions for a user based on their role
func (u *RoleUsecase) GetUserPermissions(ctx context.Context, userID uuid.UUID) ([]entities.Permission, error) {
	// Get user with role
	user, err := u.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	if user.RoleID == nil {
		return []entities.Permission{}, nil
	}

	return u.roleRepo.GetRolePermissions(ctx, *user.RoleID)
}

// DuplicateRole creates a copy of an existing role with a new name
func (u *RoleUsecase) DuplicateRole(ctx context.Context, roleID uuid.UUID, newName string) (*entities.Role, error) {
	// Get existing role
	sourceRole, err := u.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source role: %w", err)
	}
	if sourceRole == nil {
		return nil, errors.New("source role not found")
	}

	// Create new role
	newRole := &entities.Role{
		Name:        newName,
		Description: fmt.Sprintf("Copy of %s", sourceRole.Name),
		Color:       sourceRole.Color,
		IsSystem:    false,
		IsActive:    true,
	}

	// Create the role
	if err := u.roleRepo.Create(ctx, newRole); err != nil {
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	// Copy permissions
	permissions, err := u.roleRepo.GetRolePermissions(ctx, roleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source permissions: %w", err)
	}

	if len(permissions) > 0 {
		permissionIDs := make([]uuid.UUID, len(permissions))
		for i, p := range permissions {
			permissionIDs[i] = p.ID
		}
		if err := u.roleRepo.AssignPermissions(ctx, newRole.ID, permissionIDs); err != nil {
			return nil, fmt.Errorf("failed to copy permissions: %w", err)
		}
	}

	// Reload role with permissions
	return u.roleRepo.GetByID(ctx, newRole.ID)
}

// GetAllPermissions gets all available permissions
func (u *RoleUsecase) GetAllPermissions(ctx context.Context) ([]entities.Permission, error) {
	return u.permissionRepo.List(ctx)
}

// GetPermissionsByResource gets permissions for a specific resource
func (u *RoleUsecase) GetPermissionsByResource(ctx context.Context, resource string) ([]entities.Permission, error) {
	return u.permissionRepo.ListByResource(ctx, resource)
}