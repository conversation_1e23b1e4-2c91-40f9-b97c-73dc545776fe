package email

import (
	"bytes"
	"fmt"
	"html/template"
	"net/smtp"
	"strings"
)

type SMTPConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	From     string
	FromName string
}

type SMTPService struct {
	config *SMTPConfig
}

func NewSMTPService(config *SMTPConfig) *SMTPService {
	return &SMTPService{
		config: config,
	}
}

type EmailMessage struct {
	To      []string
	CC      []string
	BCC     []string
	Subject string
	Body    string
	HTML    string
	ReplyTo string
}

// SendEmail sends an email via SMTP
func (s *SMTPService) SendEmail(msg *EmailMessage) error {
	auth := smtp.PlainAuth("", s.config.Username, s.config.Password, s.config.Host)

	// Build the email message
	var emailBytes bytes.Buffer

	// Headers
	emailBytes.WriteString(fmt.Sprintf("From: %s <%s>\r\n", s.config.FromName, s.config.From))
	emailBytes.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(msg.To, ", ")))

	if len(msg.CC) > 0 {
		emailBytes.WriteString(fmt.Sprintf("Cc: %s\r\n", strings.Join(msg.CC, ", ")))
	}

	if msg.ReplyTo != "" {
		emailBytes.WriteString(fmt.Sprintf("Reply-To: %s\r\n", msg.ReplyTo))
	}

	emailBytes.WriteString(fmt.Sprintf("Subject: %s\r\n", msg.Subject))
	emailBytes.WriteString("MIME-Version: 1.0\r\n")

	// Multipart message for both plain text and HTML
	if msg.HTML != "" {
		boundary := "boundary123"
		emailBytes.WriteString(fmt.Sprintf("Content-Type: multipart/alternative; boundary=\"%s\"\r\n", boundary))
		emailBytes.WriteString("\r\n")

		// Plain text part
		emailBytes.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		emailBytes.WriteString("Content-Type: text/plain; charset=\"UTF-8\"\r\n")
		emailBytes.WriteString("\r\n")
		emailBytes.WriteString(msg.Body)
		emailBytes.WriteString("\r\n")

		// HTML part
		emailBytes.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		emailBytes.WriteString("Content-Type: text/html; charset=\"UTF-8\"\r\n")
		emailBytes.WriteString("\r\n")
		emailBytes.WriteString(msg.HTML)
		emailBytes.WriteString("\r\n")

		emailBytes.WriteString(fmt.Sprintf("--%s--\r\n", boundary))
	} else {
		// Plain text only
		emailBytes.WriteString("Content-Type: text/plain; charset=\"UTF-8\"\r\n")
		emailBytes.WriteString("\r\n")
		emailBytes.WriteString(msg.Body)
	}

	// Combine all recipients
	allRecipients := append(msg.To, msg.CC...)
	allRecipients = append(allRecipients, msg.BCC...)

	// Send the email
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)
	err := smtp.SendMail(addr, auth, s.config.From, allRecipients, emailBytes.Bytes())

	return err
}

// SendTemplatedEmail sends an email using a template
func (s *SMTPService) SendTemplatedEmail(msg *EmailMessage, templateName string, data interface{}) error {
	// Parse and execute template for HTML
	if msg.HTML != "" {
		tmpl, err := template.New(templateName).Parse(msg.HTML)
		if err != nil {
			return fmt.Errorf("failed to parse HTML template: %w", err)
		}

		var htmlBuffer bytes.Buffer
		if err := tmpl.Execute(&htmlBuffer, data); err != nil {
			return fmt.Errorf("failed to execute HTML template: %w", err)
		}
		msg.HTML = htmlBuffer.String()
	}

	// Parse and execute template for plain text
	if msg.Body != "" {
		tmpl, err := template.New(templateName + "_text").Parse(msg.Body)
		if err != nil {
			return fmt.Errorf("failed to parse text template: %w", err)
		}

		var textBuffer bytes.Buffer
		if err := tmpl.Execute(&textBuffer, data); err != nil {
			return fmt.Errorf("failed to execute text template: %w", err)
		}
		msg.Body = textBuffer.String()
	}

	return s.SendEmail(msg)
}

// GetFromAddress returns the configured from address
func (s *SMTPService) GetFromAddress() string {
	if s.config.FromName != "" {
		return fmt.Sprintf("%s <%s>", s.config.FromName, s.config.From)
	}
	return s.config.From
}

// TestConnection tests the SMTP connection
func (s *SMTPService) TestConnection() error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)
	client, err := smtp.Dial(addr)
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer client.Close()

	// Try to authenticate
	auth := smtp.PlainAuth("", s.config.Username, s.config.Password, s.config.Host)
	if err := client.Auth(auth); err != nil {
		return fmt.Errorf("SMTP authentication failed: %w", err)
	}

	return nil
}
