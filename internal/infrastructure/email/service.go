package email

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"html/template"
	"log"
	"net/smtp"
	"strings"
	"sync"
	"time"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
	"golang.org/x/time/rate"
)

// Service provides email sending capabilities
type Service struct {
	config         *Config
	emailRepo      repositories.EmailRepository
	templateRepo   repositories.EmailTemplateRepository
	templates      map[string]*template.Template
	templateMutex  sync.RWMutex
	smtpAuth       smtp.Auth
	rateLimiter    *rate.Limiter
	queue          chan *EmailJob
	workers        sync.WaitGroup
	ctx            context.Context
	cancel         context.CancelFunc
}

// EmailJob represents an email sending job
type EmailJob struct {
	Email    *entities.Email
	Template *entities.EmailTemplate
	Data     map[string]interface{}
	Retries  int
}

// NewService creates a new email service
func NewService(
	config *Config,
	emailRepo repositories.EmailRepository,
	templateRepo repositories.EmailTemplateRepository,
) (*Service, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid email config: %w", err)
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	s := &Service{
		config:       config,
		emailRepo:    emailRepo,
		templateRepo: templateRepo,
		templates:    make(map[string]*template.Template),
		rateLimiter:  rate.NewLimiter(rate.Every(time.Minute/time.Duration(config.RateLimit)), config.RateLimit),
		ctx:          ctx,
		cancel:       cancel,
	}
	
	// Setup SMTP authentication
	if config.SMTPUsername != "" && config.SMTPPassword != "" {
		s.smtpAuth = smtp.PlainAuth("", config.SMTPUsername, config.SMTPPassword, config.SMTPHost)
	}
	
	// Initialize queue if enabled
	if config.EnableQueue {
		s.queue = make(chan *EmailJob, config.QueueBufferSize)
		s.startWorkers()
	}
	
	return s, nil
}

// SendEmail sends an email
func (s *Service) SendEmail(ctx context.Context, email *entities.Email) error {
	// Save email to database first
	email.Status = entities.EmailStatusDraft
	if err := s.emailRepo.Create(ctx, email); err != nil {
		return fmt.Errorf("failed to save email: %w", err)
	}
	
	// Add tracking if enabled
	if s.config.EnableTracking {
		email = s.addTracking(email)
	}
	
	// Send via queue or directly
	if s.config.EnableQueue {
		return s.queueEmail(email, nil, nil)
	}
	
	return s.sendEmailDirect(ctx, email)
}

// SendTemplatedEmail sends an email using a template
func (s *Service) SendTemplatedEmail(ctx context.Context, email *entities.Email, templateID uuid.UUID, data map[string]interface{}) error {
	// Get template
	tmpl, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}
	
	// Apply template
	if err := s.applyTemplate(email, tmpl, data); err != nil {
		return fmt.Errorf("failed to apply template: %w", err)
	}
	
	// Save email
	email.TemplateID = &templateID
	email.Status = entities.EmailStatusDraft
	if err := s.emailRepo.Create(ctx, email); err != nil {
		return fmt.Errorf("failed to save email: %w", err)
	}
	
	// Add tracking if enabled
	if s.config.EnableTracking {
		email = s.addTracking(email)
	}
	
	// Send via queue or directly
	if s.config.EnableQueue {
		return s.queueEmail(email, tmpl, data)
	}
	
	return s.sendEmailDirect(ctx, email)
}

// sendEmailDirect sends an email directly via SMTP
func (s *Service) sendEmailDirect(ctx context.Context, email *entities.Email) error {
	// Rate limiting
	if err := s.rateLimiter.Wait(ctx); err != nil {
		return fmt.Errorf("rate limit error: %w", err)
	}
	
	// Build message
	message := s.buildMessage(email)
	
	// Connect to SMTP server
	addr := fmt.Sprintf("%s:%d", s.config.SMTPHost, s.config.SMTPPort)
	
	var err error
	if s.config.UseTLS {
		err = s.sendWithTLS(addr, message, email.To)
	} else if s.config.UseStartTLS {
		err = s.sendWithStartTLS(addr, message, email.To)
	} else {
		err = smtp.SendMail(addr, s.smtpAuth, s.config.SMTPFrom, email.To, []byte(message))
	}
	
	if err != nil {
		email.MarkAsFailed()
		s.emailRepo.Update(ctx, email)
		return fmt.Errorf("failed to send email: %w", err)
	}
	
	// Update email status
	email.MarkAsSent()
	if err := s.emailRepo.Update(ctx, email); err != nil {
		log.Printf("Failed to update email status: %v", err)
	}
	
	// Create activity record
	s.createEmailActivity(ctx, email)
	
	return nil
}

// sendWithTLS sends email using TLS
func (s *Service) sendWithTLS(addr string, message string, to []string) error {
	tlsConfig := &tls.Config{
		ServerName: s.config.SMTPHost,
	}
	
	conn, err := tls.Dial("tcp", addr, tlsConfig)
	if err != nil {
		return err
	}
	defer conn.Close()
	
	client, err := smtp.NewClient(conn, s.config.SMTPHost)
	if err != nil {
		return err
	}
	defer client.Close()
	
	if s.smtpAuth != nil {
		if err := client.Auth(s.smtpAuth); err != nil {
			return err
		}
	}
	
	if err := client.Mail(s.config.SMTPFrom); err != nil {
		return err
	}
	
	for _, recipient := range to {
		if err := client.Rcpt(recipient); err != nil {
			return err
		}
	}
	
	w, err := client.Data()
	if err != nil {
		return err
	}
	
	_, err = w.Write([]byte(message))
	if err != nil {
		return err
	}
	
	err = w.Close()
	if err != nil {
		return err
	}
	
	return client.Quit()
}

// sendWithStartTLS sends email using STARTTLS
func (s *Service) sendWithStartTLS(addr string, message string, to []string) error {
	client, err := smtp.Dial(addr)
	if err != nil {
		return err
	}
	defer client.Close()
	
	if err := client.StartTLS(&tls.Config{ServerName: s.config.SMTPHost}); err != nil {
		return err
	}
	
	if s.smtpAuth != nil {
		if err := client.Auth(s.smtpAuth); err != nil {
			return err
		}
	}
	
	if err := client.Mail(s.config.SMTPFrom); err != nil {
		return err
	}
	
	for _, recipient := range to {
		if err := client.Rcpt(recipient); err != nil {
			return err
		}
	}
	
	w, err := client.Data()
	if err != nil {
		return err
	}
	
	_, err = w.Write([]byte(message))
	if err != nil {
		return err
	}
	
	err = w.Close()
	if err != nil {
		return err
	}
	
	return client.Quit()
}

// buildMessage builds the email message
func (s *Service) buildMessage(email *entities.Email) string {
	var msg bytes.Buffer
	
	// Headers
	msg.WriteString(fmt.Sprintf("From: %s <%s>\r\n", s.config.SMTPFromName, s.config.SMTPFrom))
	msg.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(email.To, ", ")))
	
	if len(email.CC) > 0 {
		msg.WriteString(fmt.Sprintf("Cc: %s\r\n", strings.Join(email.CC, ", ")))
	}
	
	msg.WriteString(fmt.Sprintf("Subject: %s\r\n", email.Subject))
	msg.WriteString(fmt.Sprintf("Date: %s\r\n", time.Now().Format(time.RFC1123Z)))
	msg.WriteString(fmt.Sprintf("Message-ID: <%s@%s>\r\n", email.ID.String(), s.config.SMTPHost))
	
	if email.ThreadID != "" {
		msg.WriteString(fmt.Sprintf("In-Reply-To: <%s>\r\n", email.ThreadID))
		msg.WriteString(fmt.Sprintf("References: <%s>\r\n", email.ThreadID))
	}
	
	// MIME headers
	msg.WriteString("MIME-Version: 1.0\r\n")
	
	if email.BodyHTML != "" {
		// Multipart message
		boundary := fmt.Sprintf("boundary_%d", time.Now().UnixNano())
		msg.WriteString(fmt.Sprintf("Content-Type: multipart/alternative; boundary=\"%s\"\r\n", boundary))
		msg.WriteString("\r\n")
		
		// Plain text part
		msg.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		msg.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
		msg.WriteString("Content-Transfer-Encoding: quoted-printable\r\n")
		msg.WriteString("\r\n")
		msg.WriteString(email.Body)
		msg.WriteString("\r\n")
		
		// HTML part
		msg.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		msg.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		msg.WriteString("Content-Transfer-Encoding: quoted-printable\r\n")
		msg.WriteString("\r\n")
		msg.WriteString(email.BodyHTML)
		msg.WriteString("\r\n")
		
		// End boundary
		msg.WriteString(fmt.Sprintf("--%s--\r\n", boundary))
	} else {
		// Plain text only
		msg.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
		msg.WriteString("Content-Transfer-Encoding: quoted-printable\r\n")
		msg.WriteString("\r\n")
		msg.WriteString(email.Body)
	}
	
	return msg.String()
}

// addTracking adds tracking pixels and links to email
func (s *Service) addTracking(email *entities.Email) *entities.Email {
	if email.BodyHTML == "" || s.config.TrackingDomain == "" {
		return email
	}
	
	// Add open tracking pixel
	trackingPixel := fmt.Sprintf(
		`<img src="https://%s/track/open/%s" width="1" height="1" style="display:none;">`,
		s.config.TrackingDomain,
		base64.URLEncoding.EncodeToString([]byte(email.ID.String())),
	)
	
	// Insert before closing body tag
	email.BodyHTML = strings.Replace(
		email.BodyHTML,
		"</body>",
		trackingPixel+"</body>",
		1,
	)
	
	// Add click tracking to links (simplified version)
	// In production, you'd want more sophisticated link replacement
	
	return email
}

// applyTemplate applies a template to an email
func (s *Service) applyTemplate(email *entities.Email, tmpl *entities.EmailTemplate, data map[string]interface{}) error {
	// Parse and execute subject template
	subjectTmpl, err := template.New("subject").Parse(tmpl.Subject)
	if err != nil {
		return fmt.Errorf("failed to parse subject template: %w", err)
	}
	
	var subjectBuf bytes.Buffer
	if err := subjectTmpl.Execute(&subjectBuf, data); err != nil {
		return fmt.Errorf("failed to execute subject template: %w", err)
	}
	email.Subject = subjectBuf.String()
	
	// Parse and execute body template
	bodyTmpl, err := template.New("body").Parse(tmpl.Body)
	if err != nil {
		return fmt.Errorf("failed to parse body template: %w", err)
	}
	
	var bodyBuf bytes.Buffer
	if err := bodyTmpl.Execute(&bodyBuf, data); err != nil {
		return fmt.Errorf("failed to execute body template: %w", err)
	}
	email.Body = bodyBuf.String()
	
	// Parse and execute HTML body template if exists
	if tmpl.BodyHTML != "" {
		htmlTmpl, err := template.New("html").Parse(tmpl.BodyHTML)
		if err != nil {
			return fmt.Errorf("failed to parse HTML template: %w", err)
		}
		
		var htmlBuf bytes.Buffer
		if err := htmlTmpl.Execute(&htmlBuf, data); err != nil {
			return fmt.Errorf("failed to execute HTML template: %w", err)
		}
		email.BodyHTML = htmlBuf.String()
	}
	
	return nil
}

// Queue operations
func (s *Service) queueEmail(email *entities.Email, tmpl *entities.EmailTemplate, data map[string]interface{}) error {
	job := &EmailJob{
		Email:    email,
		Template: tmpl,
		Data:     data,
		Retries:  0,
	}
	
	select {
	case s.queue <- job:
		return nil
	case <-time.After(5 * time.Second):
		return fmt.Errorf("email queue is full")
	}
}

func (s *Service) startWorkers() {
	for i := 0; i < s.config.QueueWorkers; i++ {
		s.workers.Add(1)
		go s.worker(i)
	}
}

func (s *Service) worker(id int) {
	defer s.workers.Done()
	
	for {
		select {
		case job := <-s.queue:
			s.processJob(job)
		case <-s.ctx.Done():
			return
		}
	}
}

func (s *Service) processJob(job *EmailJob) {
	ctx, cancel := context.WithTimeout(s.ctx, time.Duration(s.config.SendTimeout)*time.Second)
	defer cancel()
	
	err := s.sendEmailDirect(ctx, job.Email)
	if err != nil {
		job.Retries++
		if job.Retries < s.config.MaxRetries {
			// Retry with exponential backoff
			time.Sleep(time.Duration(s.config.RetryDelay*job.Retries) * time.Second)
			s.queue <- job
		} else {
			log.Printf("Failed to send email after %d retries: %v", s.config.MaxRetries, err)
		}
	}
}

// createEmailActivity creates an activity record for the sent email
func (s *Service) createEmailActivity(ctx context.Context, email *entities.Email) {
	// This would typically use an activity repository
	// For now, we'll just log it
	log.Printf("Email sent: %s to %s", email.Subject, strings.Join(email.To, ", "))
}

// Shutdown gracefully shuts down the email service
func (s *Service) Shutdown() error {
	s.cancel()
	close(s.queue)
	s.workers.Wait()
	return nil
}