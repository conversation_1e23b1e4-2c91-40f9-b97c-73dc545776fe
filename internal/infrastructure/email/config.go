package email

import (
	"fmt"
	"os"
	"strconv"
)

// Config holds the email service configuration
type Config struct {
	// SMTP settings
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	SMTPFrom     string
	SMTPFromName string
	UseTLS       bool
	UseStartTLS  bool
	
	// Service settings
	MaxRetries       int
	RetryDelay       int // in seconds
	SendTimeout      int // in seconds
	MaxBatchSize     int
	RateLimit        int // emails per minute
	
	// Template settings
	TemplateDir      string
	EnableTracking   bool
	TrackingDomain   string
	UnsubscribeURL   string
	
	// Queue settings
	EnableQueue      bool
	QueueWorkers     int
	QueueBufferSize  int
}

// NewConfig creates a new email configuration from environment variables
func NewConfig() *Config {
	return &Config{
		SMTPHost:        getEnv("CRM_SMTP_HOST", "smtp.gmail.com"),
		SMTPPort:        getEnvAsInt("CRM_SMTP_PORT", 587),
		SMTPUsername:    getEnv("CRM_SMTP_USERNAME", ""),
		SMTPPassword:    getEnv("CRM_SMTP_PASSWORD", ""),
		SMTPFrom:        getEnv("CRM_SMTP_FROM", "<EMAIL>"),
		SMTPFromName:    getEnv("CRM_SMTP_FROM_NAME", "CRM System"),
		UseTLS:          getEnvAsBool("CRM_SMTP_USE_TLS", false),
		UseStartTLS:     getEnvAsBool("CRM_SMTP_USE_STARTTLS", true),
		
		MaxRetries:      getEnvAsInt("CRM_EMAIL_MAX_RETRIES", 3),
		RetryDelay:      getEnvAsInt("CRM_EMAIL_RETRY_DELAY", 5),
		SendTimeout:     getEnvAsInt("CRM_EMAIL_SEND_TIMEOUT", 30),
		MaxBatchSize:    getEnvAsInt("CRM_EMAIL_MAX_BATCH_SIZE", 100),
		RateLimit:       getEnvAsInt("CRM_EMAIL_RATE_LIMIT", 60),
		
		TemplateDir:     getEnv("CRM_EMAIL_TEMPLATE_DIR", "./templates/email"),
		EnableTracking:  getEnvAsBool("CRM_EMAIL_ENABLE_TRACKING", true),
		TrackingDomain:  getEnv("CRM_EMAIL_TRACKING_DOMAIN", ""),
		UnsubscribeURL:  getEnv("CRM_EMAIL_UNSUBSCRIBE_URL", ""),
		
		EnableQueue:     getEnvAsBool("CRM_EMAIL_ENABLE_QUEUE", true),
		QueueWorkers:    getEnvAsInt("CRM_EMAIL_QUEUE_WORKERS", 4),
		QueueBufferSize: getEnvAsInt("CRM_EMAIL_QUEUE_BUFFER_SIZE", 1000),
	}
}

// Validate checks if the configuration is valid
func (c *Config) Validate() error {
	if c.SMTPHost == "" {
		return fmt.Errorf("SMTP host is required")
	}
	
	if c.SMTPPort <= 0 || c.SMTPPort > 65535 {
		return fmt.Errorf("invalid SMTP port: %d", c.SMTPPort)
	}
	
	if c.SMTPFrom == "" {
		return fmt.Errorf("SMTP from address is required")
	}
	
	if c.EnableTracking && c.TrackingDomain == "" {
		return fmt.Errorf("tracking domain is required when tracking is enabled")
	}
	
	return nil
}

// Helper functions
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.Atoi(value); err == nil {
			return intVal
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolVal, err := strconv.ParseBool(value); err == nil {
			return boolVal
		}
	}
	return defaultValue
}