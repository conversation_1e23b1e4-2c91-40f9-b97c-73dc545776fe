package email

import (
	"context"
	"fmt"
	"log"
	"sync"

	"crm-backend/internal/domain/entities"
	"crm-backend/internal/domain/repositories"
	"github.com/google/uuid"
)

// DynamicService provides email sending with dynamic configuration from database
type DynamicService struct {
	integrationRepo repositories.IntegrationRepository
	emailRepo       repositories.EmailRepository
	templateRepo    repositories.EmailTemplateRepository
	logRepo         repositories.IntegrationLogRepository
	
	// Cache of email services by integration ID
	services        map[uuid.UUID]*Service
	servicesMutex   sync.RWMutex
	
	// Default service (if any)
	defaultService  *Service
	defaultIntegrationID uuid.UUID
	
	ctx             context.Context
	cancel          context.CancelFunc
}

// NewDynamicService creates a new dynamic email service
func NewDynamicService(
	ctx context.Context,
	integrationRepo repositories.IntegrationRepository,
	emailRepo repositories.EmailRepository,
	templateRepo repositories.EmailTemplateRepository,
	logRepo repositories.IntegrationLogRepository,
) (*DynamicService, error) {
	ctx, cancel := context.WithCancel(ctx)
	
	ds := &DynamicService{
		integrationRepo: integrationRepo,
		emailRepo:       emailRepo,
		templateRepo:    templateRepo,
		logRepo:         logRepo,
		services:        make(map[uuid.UUID]*Service),
		ctx:            ctx,
		cancel:         cancel,
	}
	
	// Load default email integration
	if err := ds.loadDefaultIntegration(); err != nil {
		log.Printf("Warning: failed to load default email integration: %v", err)
		// Not a fatal error - system can work without email
	}
	
	return ds, nil
}

// loadDefaultIntegration loads the default email integration
func (ds *DynamicService) loadDefaultIntegration() error {
	integration, err := ds.integrationRepo.GetDefault(ds.ctx, entities.IntegrationTypeEmail)
	if err != nil {
		return fmt.Errorf("failed to get default email integration: %w", err)
	}
	
	if integration == nil {
		return fmt.Errorf("no default email integration configured")
	}
	
	if !integration.IsEnabled {
		return fmt.Errorf("default email integration is disabled")
	}
	
	service, err := ds.createServiceFromIntegration(integration)
	if err != nil {
		return fmt.Errorf("failed to create email service: %w", err)
	}
	
	ds.defaultService = service
	ds.defaultIntegrationID = integration.ID
	ds.services[integration.ID] = service
	
	log.Printf("Loaded default email integration: %s (%s)", integration.Name, integration.Provider)
	return nil
}

// createServiceFromIntegration creates an email service from integration config
func (ds *DynamicService) createServiceFromIntegration(integration *entities.Integration) (*Service, error) {
	if integration.Type != entities.IntegrationTypeEmail {
		return nil, fmt.Errorf("integration is not email type: %s", integration.Type)
	}
	
	if !integration.IsConfigured() {
		return nil, fmt.Errorf("integration is not properly configured")
	}
	
	config := ds.convertIntegrationToConfig(integration)
	
	service, err := NewService(config, ds.emailRepo, ds.templateRepo)
	if err != nil {
		return nil, fmt.Errorf("failed to create email service: %w", err)
	}
	
	return service, nil
}

// convertIntegrationToConfig converts integration settings to email config
func (ds *DynamicService) convertIntegrationToConfig(integration *entities.Integration) *Config {
	cfg := &Config{
		// Default values
		MaxRetries:      3,
		RetryDelay:      5,
		SendTimeout:     30,
		MaxBatchSize:    100,
		RateLimit:       60,
		EnableQueue:     true,
		QueueWorkers:    4,
		QueueBufferSize: 1000,
	}
	
	// Extract configuration based on provider
	switch integration.Provider {
	case entities.EmailProviderSMTP:
		ds.applySMTPConfig(cfg, integration.Config)
	case entities.EmailProviderSendGrid:
		ds.applySendGridConfig(cfg, integration.Config)
	case entities.EmailProviderMailgun:
		ds.applyMailgunConfig(cfg, integration.Config)
	case entities.EmailProviderAWS_SES:
		ds.applyAWSSESConfig(cfg, integration.Config)
	}
	
	// Apply common settings
	if val, ok := integration.Config["max_retries"].(float64); ok {
		cfg.MaxRetries = int(val)
	}
	if val, ok := integration.Config["retry_delay"].(float64); ok {
		cfg.RetryDelay = int(val)
	}
	if val, ok := integration.Config["send_timeout"].(float64); ok {
		cfg.SendTimeout = int(val)
	}
	if val, ok := integration.Config["rate_limit"].(float64); ok {
		cfg.RateLimit = int(val)
	}
	if val, ok := integration.Config["enable_tracking"].(bool); ok {
		cfg.EnableTracking = val
	}
	if val, ok := integration.Config["tracking_domain"].(string); ok {
		cfg.TrackingDomain = val
	}
	if val, ok := integration.Config["unsubscribe_url"].(string); ok {
		cfg.UnsubscribeURL = val
	}
	if val, ok := integration.Config["enable_queue"].(bool); ok {
		cfg.EnableQueue = val
	}
	if val, ok := integration.Config["queue_workers"].(float64); ok {
		cfg.QueueWorkers = int(val)
	}
	
	return cfg
}

// applySMTPConfig applies SMTP-specific configuration
func (ds *DynamicService) applySMTPConfig(cfg *Config, config map[string]interface{}) {
	if val, ok := config["smtp_host"].(string); ok {
		cfg.SMTPHost = val
	}
	if val, ok := config["smtp_port"].(float64); ok {
		cfg.SMTPPort = int(val)
	}
	if val, ok := config["smtp_username"].(string); ok {
		cfg.SMTPUsername = val
	}
	if val, ok := config["smtp_password"].(string); ok {
		cfg.SMTPPassword = val
	}
	if val, ok := config["smtp_from"].(string); ok {
		cfg.SMTPFrom = val
	}
	if val, ok := config["smtp_from_name"].(string); ok {
		cfg.SMTPFromName = val
	}
	if val, ok := config["use_tls"].(bool); ok {
		cfg.UseTLS = val
	}
	if val, ok := config["use_starttls"].(bool); ok {
		cfg.UseStartTLS = val
	}
}

// applySendGridConfig applies SendGrid-specific configuration
func (ds *DynamicService) applySendGridConfig(cfg *Config, config map[string]interface{}) {
	// For SendGrid, we would need to implement a different service
	// This is a placeholder showing the configuration extraction
	if val, ok := config["sendgrid_api_key"].(string); ok {
		// Store API key for SendGrid client
		cfg.SMTPPassword = val // Temporary storage
	}
	if val, ok := config["smtp_from"].(string); ok {
		cfg.SMTPFrom = val
	}
	if val, ok := config["smtp_from_name"].(string); ok {
		cfg.SMTPFromName = val
	}
}

// applyMailgunConfig applies Mailgun-specific configuration
func (ds *DynamicService) applyMailgunConfig(cfg *Config, config map[string]interface{}) {
	// Placeholder for Mailgun configuration
	if val, ok := config["mailgun_api_key"].(string); ok {
		cfg.SMTPPassword = val
	}
	if val, ok := config["mailgun_domain"].(string); ok {
		cfg.SMTPHost = val
	}
}

// applyAWSSESConfig applies AWS SES-specific configuration
func (ds *DynamicService) applyAWSSESConfig(cfg *Config, config map[string]interface{}) {
	// Placeholder for AWS SES configuration
	// Would need AWS SDK integration
}

// SendEmail sends an email using the default or specified integration
func (ds *DynamicService) SendEmail(ctx context.Context, email *entities.Email, integrationID *uuid.UUID) error {
	// Get the service to use
	service, integration, err := ds.getService(integrationID)
	if err != nil {
		return fmt.Errorf("failed to get email service: %w", err)
	}
	
	// Log the attempt
	logEntry := &entities.IntegrationLog{
		IntegrationID: integration.ID,
		Action:        "send_email",
		Status:        "pending",
		Request:       entities.CustomFields{
			"to":      email.To,
			"subject": email.Subject,
			"type":    email.Type,
		},
	}
	
	// Send the email
	err = service.SendEmail(ctx, email)
	
	// Update log entry
	if err != nil {
		logEntry.Status = "failure"
		logEntry.Error = err.Error()
	} else {
		logEntry.Status = "success"
		logEntry.Response = entities.CustomFields{
			"message_id": email.MessageID,
			"sent_at":    email.SentAt,
		}
	}
	
	// Save log entry
	if ds.logRepo != nil {
		if logErr := ds.logRepo.Create(ctx, logEntry); logErr != nil {
			log.Printf("Failed to log email action: %v", logErr)
		}
	}
	
	// Update integration usage
	if err == nil {
		ds.integrationRepo.IncrementUsageCount(ctx, integration.ID)
		ds.integrationRepo.UpdateLastUsed(ctx, integration.ID)
	}
	
	return err
}

// SendTemplatedEmail sends a templated email
func (ds *DynamicService) SendTemplatedEmail(
	ctx context.Context,
	email *entities.Email,
	templateID uuid.UUID,
	data map[string]interface{},
	integrationID *uuid.UUID,
) error {
	// Get the service to use
	service, integration, err := ds.getService(integrationID)
	if err != nil {
		return fmt.Errorf("failed to get email service: %w", err)
	}
	
	// Log the attempt
	logEntry := &entities.IntegrationLog{
		IntegrationID: integration.ID,
		Action:        "send_templated_email",
		Status:        "pending",
		Request:       entities.CustomFields{
			"to":          email.To,
			"template_id": templateID.String(),
			"type":        email.Type,
		},
	}
	
	// Send the email
	err = service.SendTemplatedEmail(ctx, email, templateID, data)
	
	// Update log entry
	if err != nil {
		logEntry.Status = "failure"
		logEntry.Error = err.Error()
	} else {
		logEntry.Status = "success"
		logEntry.Response = entities.CustomFields{
			"message_id": email.MessageID,
			"sent_at":    email.SentAt,
		}
	}
	
	// Save log entry
	if ds.logRepo != nil {
		if logErr := ds.logRepo.Create(ctx, logEntry); logErr != nil {
			log.Printf("Failed to log templated email action: %v", logErr)
		}
	}
	
	// Update integration usage
	if err == nil {
		ds.integrationRepo.IncrementUsageCount(ctx, integration.ID)
		ds.integrationRepo.UpdateLastUsed(ctx, integration.ID)
	}
	
	return err
}

// getService gets the email service to use
func (ds *DynamicService) getService(integrationID *uuid.UUID) (*Service, *entities.Integration, error) {
	// If specific integration requested
	if integrationID != nil {
		// Check cache first
		ds.servicesMutex.RLock()
		if service, exists := ds.services[*integrationID]; exists {
			ds.servicesMutex.RUnlock()
			
			// Get integration details
			integration, err := ds.integrationRepo.GetByID(ds.ctx, *integrationID)
			if err != nil {
				return nil, nil, err
			}
			
			return service, integration, nil
		}
		ds.servicesMutex.RUnlock()
		
		// Load integration from database
		integration, err := ds.integrationRepo.GetByID(ds.ctx, *integrationID)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get integration: %w", err)
		}
		
		if !integration.IsEnabled {
			return nil, nil, fmt.Errorf("integration is disabled: %s", integration.Name)
		}
		
		// Create service
		service, err := ds.createServiceFromIntegration(integration)
		if err != nil {
			return nil, nil, err
		}
		
		// Cache it
		ds.servicesMutex.Lock()
		ds.services[integration.ID] = service
		ds.servicesMutex.Unlock()
		
		return service, integration, nil
	}
	
	// Use default service
	if ds.defaultService == nil {
		return nil, nil, fmt.Errorf("no email service configured")
	}
	
	integration, err := ds.integrationRepo.GetByID(ds.ctx, ds.defaultIntegrationID)
	if err != nil {
		return nil, nil, err
	}
	
	return ds.defaultService, integration, nil
}

// ReloadIntegration reloads a specific integration's configuration
func (ds *DynamicService) ReloadIntegration(ctx context.Context, integrationID uuid.UUID) error {
	// Get integration from database
	integration, err := ds.integrationRepo.GetByID(ctx, integrationID)
	if err != nil {
		return fmt.Errorf("failed to get integration: %w", err)
	}
	
	if !integration.IsEnabled {
		// Remove from cache if disabled
		ds.servicesMutex.Lock()
		if service, exists := ds.services[integrationID]; exists {
			service.Shutdown()
			delete(ds.services, integrationID)
		}
		ds.servicesMutex.Unlock()
		
		// If this was the default, clear it
		if ds.defaultIntegrationID == integrationID {
			ds.defaultService = nil
			ds.defaultIntegrationID = uuid.Nil
		}
		
		return nil
	}
	
	// Create new service
	service, err := ds.createServiceFromIntegration(integration)
	if err != nil {
		return err
	}
	
	ds.servicesMutex.Lock()
	// Shutdown old service if exists
	if oldService, exists := ds.services[integrationID]; exists {
		oldService.Shutdown()
	}
	
	// Replace with new service
	ds.services[integrationID] = service
	
	// Update default if necessary
	if integration.IsDefault {
		if ds.defaultService != nil && ds.defaultIntegrationID != integrationID {
			// Shutdown old default
			ds.defaultService.Shutdown()
		}
		ds.defaultService = service
		ds.defaultIntegrationID = integrationID
	}
	ds.servicesMutex.Unlock()
	
	log.Printf("Reloaded email integration: %s", integration.Name)
	return nil
}

// TestIntegration tests an email integration
func (ds *DynamicService) TestIntegration(ctx context.Context, integrationID uuid.UUID) error {
	integration, err := ds.integrationRepo.GetByID(ctx, integrationID)
	if err != nil {
		return fmt.Errorf("failed to get integration: %w", err)
	}
	
	// Create a temporary service
	service, err := ds.createServiceFromIntegration(integration)
	if err != nil {
		ds.integrationRepo.UpdateTestResult(ctx, integrationID, false, err.Error())
		return err
	}
	defer service.Shutdown()
	
	// Send a test email
	testEmail := &entities.Email{
		Subject:  "CRM Email Integration Test",
		Body:     "This is a test email to verify your email integration is working correctly.",
		BodyHTML: "<p>This is a test email to verify your email integration is working correctly.</p>",
		To:       []string{integration.Config["smtp_from"].(string)}, // Send to self
		Type:     entities.EmailTypeManual,
	}
	
	err = service.SendEmail(ctx, testEmail)
	if err != nil {
		ds.integrationRepo.UpdateTestResult(ctx, integrationID, false, err.Error())
		return err
	}
	
	ds.integrationRepo.UpdateTestResult(ctx, integrationID, true, "")
	return nil
}

// Shutdown gracefully shuts down all email services
func (ds *DynamicService) Shutdown() error {
	ds.cancel()
	
	ds.servicesMutex.Lock()
	defer ds.servicesMutex.Unlock()
	
	for id, service := range ds.services {
		log.Printf("Shutting down email service: %s", id)
		service.Shutdown()
	}
	
	ds.services = make(map[uuid.UUID]*Service)
	ds.defaultService = nil
	
	return nil
}