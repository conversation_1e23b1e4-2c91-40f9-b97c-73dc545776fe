package config

import (
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"log"
	
	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Auth     AuthConfig     `mapstructure:"auth"`
	Storage  StorageConfig  `mapstructure:"storage"`
	Redis    RedisConfig    `mapstructure:"redis"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port         string `mapstructure:"port" default:"8080"`
	Host         string `mapstructure:"host" default:"0.0.0.0"`
	ReadTimeout  int    `mapstructure:"read_timeout" default:"60"`
	WriteTimeout int    `mapstructure:"write_timeout" default:"60"`
	IdleTimeout  int    `mapstructure:"idle_timeout" default:"60"`
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Host     string `mapstructure:"host" default:"localhost"`
	Port     string `mapstructure:"port" default:"5432"`
	User     string `mapstructure:"user" default:"postgres"`
	Password string `mapstructure:"password" default:"postgres"`
	DBName   string `mapstructure:"dbname" default:"crm"`
	SSLMode  string `mapstructure:"sslmode" default:"disable"`
	TimeZone string `mapstructure:"timezone" default:"UTC"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	JWTSecret     string `mapstructure:"jwt_secret"`
	TokenDuration int    `mapstructure:"token_duration" default:"168"` // hours
}

// StorageConfig holds file storage configuration
type StorageConfig struct {
	UploadPath string `mapstructure:"upload_path" default:"./uploads"`
	MaxSize    int64  `mapstructure:"max_size" default:"10485760"` // 10MB
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Host     string `mapstructure:"host" default:"localhost"`
	Port     string `mapstructure:"port" default:"6379"`
	Password string `mapstructure:"password" default:""`
	DB       int    `mapstructure:"db" default:"0"`
}

// LoadConfig loads configuration from environment variables and config file
func LoadConfig(path string) (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(path)
	viper.AddConfigPath(".")

	// Set environment variable prefix
	viper.SetEnvPrefix("CRM")
	viper.AutomaticEnv()

	// Set default values
	setDefaults()

	// Try to read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	// Validate critical security settings
	if err := validateConfig(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", "8080")
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.read_timeout", 60)
	viper.SetDefault("server.write_timeout", 60)
	viper.SetDefault("server.idle_timeout", 60)

	// Database defaults
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", "5432")
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.dbname", "crm")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.timezone", "UTC")

	// Auth defaults - JWT secret MUST be set via environment variable
	// No default JWT secret for security - will panic if not set
	viper.SetDefault("auth.token_duration", 168) // 7 days

	// Storage defaults
	viper.SetDefault("storage.upload_path", "./uploads")
	viper.SetDefault("storage.max_size", 10485760) // 10MB

	// Redis defaults
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", "6379")
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)
}

// GetDSN returns the database DSN
func (dc *DatabaseConfig) GetDSN() string {
	return "host=" + dc.Host + " user=" + dc.User + " password=" + dc.Password + 
		   " dbname=" + dc.DBName + " port=" + dc.Port + " sslmode=" + dc.SSLMode + 
		   " TimeZone=" + dc.TimeZone
}

// validateConfig validates critical configuration settings
func validateConfig(config *Config) error {
	// Check JWT secret is set and secure
	if config.Auth.JWTSecret == "" {
		return errors.New("JWT secret is not set. Please set CRM_AUTH_JWT_SECRET environment variable")
	}
	
	// Warn if JWT secret is too short (less than 32 characters)
	if len(config.Auth.JWTSecret) < 32 {
		log.Printf("⚠️  WARNING: JWT secret is too short (%d chars). Recommended minimum is 32 characters", len(config.Auth.JWTSecret))
	}
	
	// Check for default/weak JWT secrets
	weakSecrets := []string{"your-secret-key", "secret", "password", "123456", "admin"}
	for _, weak := range weakSecrets {
		if config.Auth.JWTSecret == weak {
			return fmt.Errorf("JWT secret '%s' is insecure. Please use a strong, random secret", weak)
		}
	}
	
	return nil
}

// GenerateSecureJWTSecret generates a cryptographically secure JWT secret
func GenerateSecureJWTSecret() (string, error) {
	// Generate 64 bytes of random data
	bytes := make([]byte, 64)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	
	// Encode to base64 for a string representation
	return base64.URLEncoding.EncodeToString(bytes), nil
}