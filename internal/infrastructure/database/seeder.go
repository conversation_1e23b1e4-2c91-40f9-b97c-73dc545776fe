package database

import (
	"fmt"
	"log"

	"github.com/google/uuid"
	"crm-backend/internal/domain/entities"
	"gorm.io/gorm"
)

type Seeder struct {
	db *gorm.DB
}

func NewSeeder(db *gorm.DB) *Seeder {
	return &Seeder{db: db}
}

func (s *Seeder) Seed() error {
	log.Println("Starting database seeding...")
	
	// Seed in order
	if err := s.seedPermissions(); err != nil {
		return fmt.Errorf("failed to seed permissions: %w", err)
	}
	
	if err := s.seedRoles(); err != nil {
		return fmt.Errorf("failed to seed roles: %w", err)
	}
	
	if err := s.seedDefaultUsers(); err != nil {
		return fmt.Errorf("failed to seed users: %w", err)
	}
	
	log.Println("Database seeding completed successfully!")
	return nil
}

func (s *Seeder) seedPermissions() error {
	log.Println("Seeding permissions...")
	
	// Check if permissions already exist
	var count int64
	s.db.Model(&entities.Permission{}).Count(&count)
	if count > 0 {
		log.Println("Permissions already seeded, skipping...")
		return nil
	}
	
	permissions := []entities.Permission{
		// Pipelines
		{Resource: "pipelines", Action: "read", Description: "View pipelines"},
		{Resource: "pipelines", Action: "create", Description: "Create pipelines"},
		{Resource: "pipelines", Action: "update", Description: "Update pipelines"},
		{Resource: "pipelines", Action: "delete", Description: "Delete pipelines"},
		{Resource: "pipelines", Action: "manage", Description: "Full pipeline management"},
		
		// Cards
		{Resource: "cards", Action: "read", Description: "View cards"},
		{Resource: "cards", Action: "create", Description: "Create cards"},
		{Resource: "cards", Action: "update", Description: "Update cards"},
		{Resource: "cards", Action: "delete", Description: "Delete cards"},
		{Resource: "cards", Action: "manage", Description: "Full card management"},
		{Resource: "cards", Action: "export", Description: "Export cards data"},
		{Resource: "cards", Action: "import", Description: "Import cards data"},
		
		// Contacts
		{Resource: "contacts", Action: "read", Description: "View contacts"},
		{Resource: "contacts", Action: "create", Description: "Create contacts"},
		{Resource: "contacts", Action: "update", Description: "Update contacts"},
		{Resource: "contacts", Action: "delete", Description: "Delete contacts"},
		{Resource: "contacts", Action: "manage", Description: "Full contact management"},
		{Resource: "contacts", Action: "export", Description: "Export contacts"},
		{Resource: "contacts", Action: "import", Description: "Import contacts"},
		
		// Companies
		{Resource: "companies", Action: "read", Description: "View companies"},
		{Resource: "companies", Action: "create", Description: "Create companies"},
		{Resource: "companies", Action: "update", Description: "Update companies"},
		{Resource: "companies", Action: "delete", Description: "Delete companies"},
		{Resource: "companies", Action: "manage", Description: "Full company management"},
		
		// Users
		{Resource: "users", Action: "read", Description: "View users"},
		{Resource: "users", Action: "create", Description: "Create users"},
		{Resource: "users", Action: "update", Description: "Update users"},
		{Resource: "users", Action: "delete", Description: "Delete users"},
		{Resource: "users", Action: "manage", Description: "Full user management"},
		
		// Roles
		{Resource: "roles", Action: "read", Description: "View roles"},
		{Resource: "roles", Action: "create", Description: "Create roles"},
		{Resource: "roles", Action: "update", Description: "Update roles"},
		{Resource: "roles", Action: "delete", Description: "Delete roles"},
		{Resource: "roles", Action: "manage", Description: "Full role management"},
		
		// Settings
		{Resource: "settings", Action: "read", Description: "View settings"},
		{Resource: "settings", Action: "update", Description: "Update settings"},
		{Resource: "settings", Action: "manage", Description: "Full settings management"},
		
		// Reports
		{Resource: "reports", Action: "read", Description: "View reports"},
		{Resource: "reports", Action: "create", Description: "Create reports"},
		{Resource: "reports", Action: "export", Description: "Export reports"},
		{Resource: "reports", Action: "manage", Description: "Full report management"},
		
		// Dashboard
		{Resource: "dashboard", Action: "read", Description: "View dashboard"},
		{Resource: "dashboard", Action: "manage", Description: "Manage dashboard widgets"},
		
		// System-wide
		{Resource: "*", Action: "*", Description: "Full system access"},
	}
	
	for i := range permissions {
		permissions[i].ID = uuid.New()
	}
	
	if err := s.db.CreateInBatches(permissions, 100).Error; err != nil {
		return err
	}
	
	log.Printf("Created %d permissions", len(permissions))
	return nil
}

func (s *Seeder) seedRoles() error {
	log.Println("Seeding roles...")
	
	// Check if roles already exist
	var count int64
	s.db.Model(&entities.Role{}).Count(&count)
	if count > 0 {
		log.Println("Roles already seeded, skipping...")
		return nil
	}
	
	// Get all permissions for admin
	var allPermissions []entities.Permission
	s.db.Find(&allPermissions)
	
	// Create Administrator role with all permissions
	adminRole := entities.Role{
		BaseEntity:  entities.BaseEntity{ID: uuid.New()},
		Name:        "Administrator",
		Description: "Full system access",
		Color:       "#EF4444",
		IsSystem:    true,
		IsActive:    true,
	}
	
	if err := s.db.Create(&adminRole).Error; err != nil {
		return err
	}
	
	// Assign all permissions to admin
	for _, perm := range allPermissions {
		rolePermission := entities.RolePermission{
			RoleID:       adminRole.ID,
			PermissionID: perm.ID,
		}
		s.db.Create(&rolePermission)
	}
	
	// Create Manager role with limited permissions
	managerPermissions := []string{
		"pipelines.read", "pipelines.create", "pipelines.update",
		"cards.read", "cards.create", "cards.update", "cards.export",
		"contacts.read", "contacts.create", "contacts.update", "contacts.export",
		"companies.read", "companies.create", "companies.update",
		"reports.read", "reports.create", "reports.export",
		"dashboard.read",
	}
	
	managerRole := entities.Role{
		BaseEntity:  entities.BaseEntity{ID: uuid.New()},
		Name:        "Manager",
		Description: "Manage sales and customer data",
		Color:       "#3B82F6",
		IsSystem:    true,
		IsActive:    true,
	}
	
	if err := s.db.Create(&managerRole).Error; err != nil {
		return err
	}
	
	// Assign manager permissions
	for _, permStr := range managerPermissions {
		var perm entities.Permission
		resource, action := parsePermissionString(permStr)
		if err := s.db.Where("resource = ? AND action = ?", resource, action).First(&perm).Error; err == nil {
			rolePermission := entities.RolePermission{
				RoleID:       managerRole.ID,
				PermissionID: perm.ID,
			}
			s.db.Create(&rolePermission)
		}
	}
	
	// Create Sales role
	salesPermissions := []string{
		"pipelines.read",
		"cards.read", "cards.create", "cards.update",
		"contacts.read", "contacts.create", "contacts.update",
		"companies.read",
		"dashboard.read",
	}
	
	salesRole := entities.Role{
		BaseEntity:  entities.BaseEntity{ID: uuid.New()},
		Name:        "Sales",
		Description: "Sales team member",
		Color:       "#10B981",
		IsSystem:    false,
		IsActive:    true,
	}
	
	if err := s.db.Create(&salesRole).Error; err != nil {
		return err
	}
	
	// Assign sales permissions
	for _, permStr := range salesPermissions {
		var perm entities.Permission
		resource, action := parsePermissionString(permStr)
		if err := s.db.Where("resource = ? AND action = ?", resource, action).First(&perm).Error; err == nil {
			rolePermission := entities.RolePermission{
				RoleID:       salesRole.ID,
				PermissionID: perm.ID,
			}
			s.db.Create(&rolePermission)
		}
	}
	
	// Create Support role
	supportPermissions := []string{
		"contacts.read", "contacts.update",
		"companies.read",
		"cards.read", "cards.update",
		"dashboard.read",
	}
	
	supportRole := entities.Role{
		BaseEntity:  entities.BaseEntity{ID: uuid.New()},
		Name:        "Support",
		Description: "Customer support team",
		Color:       "#8B5CF6",
		IsSystem:    false,
		IsActive:    true,
	}
	
	if err := s.db.Create(&supportRole).Error; err != nil {
		return err
	}
	
	// Assign support permissions
	for _, permStr := range supportPermissions {
		var perm entities.Permission
		resource, action := parsePermissionString(permStr)
		if err := s.db.Where("resource = ? AND action = ?", resource, action).First(&perm).Error; err == nil {
			rolePermission := entities.RolePermission{
				RoleID:       supportRole.ID,
				PermissionID: perm.ID,
			}
			s.db.Create(&rolePermission)
		}
	}
	
	log.Println("Created 4 roles with permissions")
	return nil
}

func (s *Seeder) seedDefaultUsers() error {
	log.Println("Updating default users with roles...")
	
	// Get roles
	var adminRole, managerRole, salesRole, supportRole entities.Role
	s.db.Where("name = ?", "Administrator").First(&adminRole)
	s.db.Where("name = ?", "Manager").First(&managerRole)
	s.db.Where("name = ?", "Sales").First(&salesRole)
	s.db.Where("name = ?", "Support").First(&supportRole)
	
	// Update existing users with roles
	userRoles := map[string]uuid.UUID{
		"<EMAIL>":   adminRole.ID,
		"<EMAIL>": managerRole.ID,
		"<EMAIL>":   salesRole.ID,
		"<EMAIL>": supportRole.ID,
		"<EMAIL>":    salesRole.ID,
	}
	
	for email, roleID := range userRoles {
		var user entities.User
		if err := s.db.Where("email = ?", email).First(&user).Error; err == nil {
			user.RoleID = &roleID
			user.LegacyRole = "" // Clear legacy role
			if email == "<EMAIL>" {
				user.LegacyRole = "admin" // Keep backward compatibility for admin
			}
			s.db.Save(&user)
			log.Printf("Updated user %s with role", email)
		}
	}
	
	log.Println("User roles updated successfully")
	return nil
}

func parsePermissionString(permStr string) (string, string) {
	// Parse "resource.action" format
	for i := len(permStr) - 1; i >= 0; i-- {
		if permStr[i] == '.' {
			return permStr[:i], permStr[i+1:]
		}
	}
	return permStr, ""
}