package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"crm-backend/internal/infrastructure/config"

	"github.com/redis/go-redis/v9"
)

// Client wraps the Redis client
type Client struct {
	client *redis.Client
	ctx    context.Context
}

// NewClient creates a new Redis client
func NewClient(cfg *config.RedisConfig) (*Client, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
		// Connection pool settings
		PoolSize:     10,
		MinIdleConns: 5,
		MaxRetries:   3,
	})

	ctx := context.Background()

	// Test connection
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &Client{
		client: client,
		ctx:    ctx,
	}, nil
}

// Close closes the Redis connection
func (c *Client) Close() error {
	return c.client.Close()
}

// TokenBlacklist operations

// BlacklistToken adds a token to the blacklist with expiration
func (c *Client) BlacklistToken(token string, expiration time.Duration) error {
	key := fmt.Sprintf("blacklist:token:%s", token)
	return c.client.Set(c.ctx, key, true, expiration).Err()
}

// IsTokenBlacklisted checks if a token is blacklisted
func (c *Client) IsTokenBlacklisted(token string) (bool, error) {
	key := fmt.Sprintf("blacklist:token:%s", token)
	exists, err := c.client.Exists(c.ctx, key).Result()
	if err != nil {
		return false, err
	}
	return exists > 0, nil
}

// Cache operations

// Set sets a value in cache with expiration
func (c *Client) Set(key string, value interface{}, expiration time.Duration) error {
	return c.client.Set(c.ctx, key, value, expiration).Err()
}

// Get gets a value from cache
func (c *Client) Get(key string) (string, error) {
	return c.client.Get(c.ctx, key).Result()
}

// Delete deletes a key from cache
func (c *Client) Delete(key string) error {
	return c.client.Del(c.ctx, key).Err()
}

// Exists checks if a key exists
func (c *Client) Exists(key string) (bool, error) {
	exists, err := c.client.Exists(c.ctx, key).Result()
	if err != nil {
		return false, err
	}
	return exists > 0, nil
}

// SetJSON sets a JSON value in cache
func (c *Client) SetJSON(key string, value interface{}, expiration time.Duration) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return c.client.Set(c.ctx, key, jsonData, expiration).Err()
}

// GetJSON gets a JSON value from cache
func (c *Client) GetJSON(key string, dest interface{}) error {
	val, err := c.client.Get(c.ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(val), dest)
}

// Session operations

// SetSession stores a user session
func (c *Client) SetSession(sessionID string, userID string, expiration time.Duration) error {
	key := fmt.Sprintf("session:%s", sessionID)
	return c.client.Set(c.ctx, key, userID, expiration).Err()
}

// GetSession retrieves a user session
func (c *Client) GetSession(sessionID string) (string, error) {
	key := fmt.Sprintf("session:%s", sessionID)
	return c.client.Get(c.ctx, key).Result()
}

// DeleteSession removes a user session
func (c *Client) DeleteSession(sessionID string) error {
	key := fmt.Sprintf("session:%s", sessionID)
	return c.client.Del(c.ctx, key).Err()
}

// ExtendSession extends the expiration of a session
func (c *Client) ExtendSession(sessionID string, expiration time.Duration) error {
	key := fmt.Sprintf("session:%s", sessionID)
	return c.client.Expire(c.ctx, key, expiration).Err()
}

// Rate limiting operations

// IncrementRateLimit increments the rate limit counter for a key
func (c *Client) IncrementRateLimit(key string, window time.Duration) (int64, error) {
	fullKey := fmt.Sprintf("ratelimit:%s", key)

	pipe := c.client.Pipeline()
	incr := pipe.Incr(c.ctx, fullKey)
	pipe.Expire(c.ctx, fullKey, window)

	if _, err := pipe.Exec(c.ctx); err != nil {
		return 0, err
	}

	return incr.Val(), nil
}

// GetRateLimit gets the current rate limit count for a key
func (c *Client) GetRateLimit(key string) (int64, error) {
	fullKey := fmt.Sprintf("ratelimit:%s", key)
	count, err := c.client.Get(c.ctx, fullKey).Int64()
	if err == redis.Nil {
		return 0, nil
	}
	return count, err
}

// Cache invalidation operations

// InvalidatePattern invalidates all keys matching a pattern
func (c *Client) InvalidatePattern(pattern string) error {
	keys, err := c.client.Keys(c.ctx, pattern).Result()
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	return c.client.Del(c.ctx, keys...).Err()
}

// InvalidateUserCache invalidates all cache entries for a user
func (c *Client) InvalidateUserCache(userID string) error {
	pattern := fmt.Sprintf("*:user:%s:*", userID)
	return c.InvalidatePattern(pattern)
}

// Pub/Sub operations for real-time events

// Publish publishes a message to a channel
func (c *Client) Publish(channel string, message interface{}) error {
	return c.client.Publish(c.ctx, channel, message).Err()
}

// Subscribe subscribes to a channel
func (c *Client) Subscribe(channel string) *redis.PubSub {
	return c.client.Subscribe(c.ctx, channel)
}
