package entities

import (
	"database/sql/driver"
	"encoding/json"
	"github.com/google/uuid"
)

// PipelineType represents the type of pipeline
type PipelineType string

const (
	PipelineTypeSales      PipelineType = "sales"
	PipelineTypeHR         PipelineType = "hr"
	PipelineTypeProjects   PipelineType = "projects"
	PipelineTypeSupport    PipelineType = "support"
	PipelineTypeCustom     PipelineType = "custom"
)

// PipelineSettings stores pipeline configuration
type PipelineSettings struct {
	AllowedUsers      []string               `json:"allowed_users,omitempty"`      // User IDs who can access
	AllowedRoles      []string               `json:"allowed_roles,omitempty"`      // Roles that can access
	RequiredFields    []string               `json:"required_fields,omitempty"`    // Required fields for cards
	AutomationEnabled bool                   `json:"automation_enabled"`
	Automations       map[string]interface{} `json:"automations,omitempty"`        // Automation rules
	Notifications     map[string]interface{} `json:"notifications,omitempty"`      // Notification settings
	SLAEnabled        bool                   `json:"sla_enabled"`
	SLASettings       map[string]interface{} `json:"sla_settings,omitempty"`       // SLA configuration
	Webhooks          []string               `json:"webhooks,omitempty"`            // Webhook URLs
}

// Scan implements the Scanner interface for GORM
func (ps *PipelineSettings) Scan(value interface{}) error {
	if value == nil {
		*ps = PipelineSettings{}
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, ps)
	case string:
		return json.Unmarshal([]byte(v), ps)
	default:
		*ps = PipelineSettings{}
		return nil
	}
}

// Value implements the driver Valuer interface for GORM
func (ps PipelineSettings) Value() (driver.Value, error) {
	return json.Marshal(ps)
}

// Pipeline represents a sales/business process pipeline
type Pipeline struct {
	BaseEntity
	Name         string            `json:"name" gorm:"not null" validate:"required"`
	Description  string            `json:"description"`
	Type         PipelineType      `json:"type" gorm:"default:'sales'" validate:"oneof=sales hr projects support custom"`
	Color        string            `json:"color" gorm:"default:'#3B82F6'"`
	Icon         string            `json:"icon"`
	IsDefault    bool              `json:"is_default" gorm:"default:false"`
	IsActive     bool              `json:"is_active" gorm:"default:true"`
	IsArchived   bool              `json:"is_archived" gorm:"default:false"`
	SortOrder    int               `json:"sort_order" gorm:"default:0"`
	Settings     PipelineSettings  `json:"settings" gorm:"type:jsonb;default:'{}'"`
	CustomFields CustomFields      `json:"custom_fields" gorm:"type:jsonb;default:'{}'"`
	CreatedByID  *uuid.UUID        `json:"created_by_id" gorm:"type:uuid"`
	CreatedBy    *User             `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	Stages       []Stage           `json:"stages,omitempty" gorm:"foreignKey:PipelineID;constraint:OnDelete:CASCADE"`
	Cards        []Card            `json:"cards,omitempty" gorm:"foreignKey:PipelineID"`
}

// StageSettings stores stage-specific configuration
type StageSettings struct {
	RequiredFields    []string               `json:"required_fields,omitempty"`    // Fields required to move to this stage
	AutoMoveCondition map[string]interface{} `json:"auto_move_condition,omitempty"` // Conditions for auto-movement
	OnEnterActions    []StageAction          `json:"on_enter_actions,omitempty"`   // Actions when card enters stage
	OnExitActions     []StageAction          `json:"on_exit_actions,omitempty"`    // Actions when card leaves stage
	TimeLimit         *int                   `json:"time_limit,omitempty"`         // Max days in stage
	Notifications     []StageNotification    `json:"notifications,omitempty"`      // Stage notifications
}

// Scan implements the Scanner interface for GORM
func (ss *StageSettings) Scan(value interface{}) error {
	if value == nil {
		*ss = StageSettings{}
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, ss)
	case string:
		return json.Unmarshal([]byte(v), ss)
	default:
		*ss = StageSettings{}
		return nil
	}
}

// Value implements the driver Valuer interface for GORM
func (ss StageSettings) Value() (driver.Value, error) {
	return json.Marshal(ss)
}

// StageAction represents an action to perform on stage transition
type StageAction struct {
	Type       string                 `json:"type"`       // email, webhook, task, field_update
	Config     map[string]interface{} `json:"config"`     // Action configuration
	Conditions map[string]interface{} `json:"conditions,omitempty"` // Optional conditions
}

// StageNotification represents a notification setting
type StageNotification struct {
	Event      string   `json:"event"`      // overdue, entered, exited
	Recipients []string `json:"recipients"` // User IDs or emails
	Template   string   `json:"template"`   // Notification template
}

// Stage represents a pipeline stage
type Stage struct {
	BaseEntity
	PipelineID        uuid.UUID       `json:"pipeline_id" gorm:"type:uuid;not null;index"`
	Pipeline          *Pipeline       `json:"pipeline,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	Name              string          `json:"name" gorm:"not null" validate:"required"`
	Description       string          `json:"description"`
	Color             string          `json:"color" gorm:"default:'#6B7280'"`
	Icon              string          `json:"icon"`
	IsClosedWon       bool            `json:"is_closed_won" gorm:"default:false"`
	IsClosedLost      bool            `json:"is_closed_lost" gorm:"default:false"`
	IsFinal           bool            `json:"is_final" gorm:"default:false"` // Cannot move cards from here
	Probability       float64         `json:"probability" gorm:"default:0.00"`
	SortOrder         int             `json:"sort_order" gorm:"default:0"`
	AutoMoveAfterDays *int            `json:"auto_move_after_days,omitempty"`
	NextStageID       *uuid.UUID      `json:"next_stage_id,omitempty" gorm:"type:uuid"` // Default next stage
	IsActive          bool            `json:"is_active" gorm:"default:true"`
	Settings          StageSettings   `json:"settings" gorm:"type:jsonb;default:'{}'"`
	CustomFields      CustomFields    `json:"custom_fields" gorm:"type:jsonb;default:'{}'"`
	Cards             []Card          `json:"cards,omitempty" gorm:"foreignKey:StageID;constraint:OnDelete:SET NULL"`
}

// GetStageByOrder returns the stage at the given order
func (p *Pipeline) GetStageByOrder(order int) *Stage {
	for _, stage := range p.Stages {
		if stage.SortOrder == order {
			return &stage
		}
	}
	return nil
}

// GetNextStage returns the next stage in order
func (p *Pipeline) GetNextStage(currentOrder int) *Stage {
	return p.GetStageByOrder(currentOrder + 1)
}

// GetPreviousStage returns the previous stage in order
func (p *Pipeline) GetPreviousStage(currentOrder int) *Stage {
	return p.GetStageByOrder(currentOrder - 1)
}