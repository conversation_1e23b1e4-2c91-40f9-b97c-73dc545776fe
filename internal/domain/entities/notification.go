package entities

import (
	"time"

	"github.com/google/uuid"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	NotificationTypeInfo     NotificationType = "info"
	NotificationTypeSuccess  NotificationType = "success"
	NotificationTypeWarning  NotificationType = "warning"
	NotificationTypeError    NotificationType = "error"
	NotificationTypeActivity NotificationType = "activity"
	NotificationTypeMention  NotificationType = "mention"
	NotificationTypeReminder NotificationType = "reminder"
	NotificationTypeDeadline NotificationType = "deadline"
	NotificationTypeAssignment NotificationType = "assignment"
)

// NotificationChannel represents the delivery channel for notifications
type NotificationChannel string

const (
	NotificationChannelInApp    NotificationChannel = "in_app"
	NotificationChannelEmail     NotificationChannel = "email"
	NotificationChannelSMS       NotificationChannel = "sms"
	NotificationChannelPush      NotificationChannel = "push"
	NotificationChannelWebhook   NotificationChannel = "webhook"
)

// NotificationPriority represents the priority of a notification
type NotificationPriority string

const (
	NotificationPriorityLow    NotificationPriority = "low"
	NotificationPriorityNormal NotificationPriority = "normal"
	NotificationPriorityHigh   NotificationPriority = "high"
	NotificationPriorityUrgent NotificationPriority = "urgent"
)

// Notification represents a notification to a user
type Notification struct {
	BaseEntity
	
	// Notification content
	Title       string               `json:"title" gorm:"not null" validate:"required"`
	Message     string               `json:"message" gorm:"type:text" validate:"required"`
	Type        NotificationType     `json:"type" gorm:"not null;default:'info'" validate:"required"`
	Priority    NotificationPriority `json:"priority" gorm:"not null;default:'normal'" validate:"required"`
	Icon        string               `json:"icon"`
	ActionURL   string               `json:"action_url"`
	ActionLabel string               `json:"action_label"`
	
	// Recipient
	UserID      uuid.UUID            `json:"user_id" gorm:"type:uuid;not null;index"`
	User        *User                `json:"user,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	
	// Delivery channels and status
	Channels    []NotificationChannel `json:"channels" gorm:"type:jsonb" validate:"required,dive,oneof=in_app email sms push webhook"`
	IsRead      bool                 `json:"is_read" gorm:"default:false;index"`
	ReadAt      *time.Time           `json:"read_at,omitempty"`
	IsSent      bool                 `json:"is_sent" gorm:"default:false;index"`
	SentAt      *time.Time           `json:"sent_at,omitempty"`
	
	// Delivery status per channel
	DeliveryStatus map[string]NotificationDeliveryStatus `json:"delivery_status" gorm:"type:jsonb;default:'{}'"`
	
	// Related entities
	EntityType  string               `json:"entity_type"`
	EntityID    *uuid.UUID           `json:"entity_id,omitempty" gorm:"type:uuid"`
	
	// Direct associations for common entities
	CardID      *uuid.UUID           `json:"card_id,omitempty" gorm:"type:uuid;index"`
	Card        *Card                `json:"card,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	ContactID   *uuid.UUID           `json:"contact_id,omitempty" gorm:"type:uuid;index"`
	Contact     *Contact             `json:"contact,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	CompanyID   *uuid.UUID           `json:"company_id,omitempty" gorm:"type:uuid;index"`
	Company     *Company             `json:"company,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	CommentID   *uuid.UUID           `json:"comment_id,omitempty" gorm:"type:uuid;index"`
	Comment     *Comment             `json:"comment,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	
	// Sender (optional, for user-triggered notifications)
	SenderID    *uuid.UUID           `json:"sender_id,omitempty" gorm:"type:uuid"`
	Sender      *User                `json:"sender,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	
	// Scheduling
	ScheduledFor *time.Time          `json:"scheduled_for,omitempty"`
	ExpiresAt   *time.Time           `json:"expires_at,omitempty"`
	
	// Additional data
	Data        CustomFields         `json:"data" gorm:"type:jsonb;default:'{}'"`
	
	// Grouping
	GroupKey    string               `json:"group_key" gorm:"index"` // For grouping similar notifications
	GroupCount  int                  `json:"group_count" gorm:"default:1"`
}

// NotificationDeliveryStatus represents delivery status for a specific channel
type NotificationDeliveryStatus struct {
	Channel     NotificationChannel  `json:"channel"`
	Status      string              `json:"status"` // pending, sent, delivered, failed
	SentAt      *time.Time          `json:"sent_at,omitempty"`
	DeliveredAt *time.Time          `json:"delivered_at,omitempty"`
	FailedAt    *time.Time          `json:"failed_at,omitempty"`
	Error       string              `json:"error,omitempty"`
	Attempts    int                 `json:"attempts"`
}

// NotificationPreference represents user preferences for notifications
type NotificationPreference struct {
	BaseEntity
	UserID      uuid.UUID           `json:"user_id" gorm:"type:uuid;not null;unique"`
	User        *User               `json:"user,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	
	// Global settings
	EnableNotifications bool           `json:"enable_notifications" gorm:"default:true"`
	EnableEmail        bool            `json:"enable_email" gorm:"default:true"`
	EnableInApp        bool            `json:"enable_in_app" gorm:"default:true"`
	EnableSMS          bool            `json:"enable_sms" gorm:"default:false"`
	EnablePush         bool            `json:"enable_push" gorm:"default:true"`
	
	// Quiet hours
	QuietHoursEnabled  bool            `json:"quiet_hours_enabled" gorm:"default:false"`
	QuietHoursStart    string          `json:"quiet_hours_start"` // Format: "HH:MM"
	QuietHoursEnd      string          `json:"quiet_hours_end"`   // Format: "HH:MM"
	QuietHoursTimezone string          `json:"quiet_hours_timezone" gorm:"default:'UTC'"`
	
	// Type-specific preferences
	TypePreferences    map[NotificationType]TypePreference `json:"type_preferences" gorm:"type:jsonb;default:'{}'"`
	
	// Frequency settings
	DigestFrequency    string          `json:"digest_frequency" gorm:"default:'immediate'"` // immediate, hourly, daily, weekly
	LastDigestSentAt   *time.Time      `json:"last_digest_sent_at,omitempty"`
}

// TypePreference represents preferences for a specific notification type
type TypePreference struct {
	Enabled   bool                    `json:"enabled"`
	Channels  []NotificationChannel   `json:"channels"`
	Priority  NotificationPriority    `json:"min_priority"` // Minimum priority to notify
}

// MarkAsRead marks the notification as read
func (n *Notification) MarkAsRead() {
	if !n.IsRead {
		n.IsRead = true
		now := time.Now()
		n.ReadAt = &now
	}
}

// MarkAsSent marks the notification as sent
func (n *Notification) MarkAsSent() {
	if !n.IsSent {
		n.IsSent = true
		now := time.Now()
		n.SentAt = &now
	}
}

// SetDeliveryStatus sets the delivery status for a specific channel
func (n *Notification) SetDeliveryStatus(channel NotificationChannel, status NotificationDeliveryStatus) {
	if n.DeliveryStatus == nil {
		n.DeliveryStatus = make(map[string]NotificationDeliveryStatus)
	}
	n.DeliveryStatus[string(channel)] = status
}

// IsExpired checks if the notification has expired
func (n *Notification) IsExpired() bool {
	if n.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*n.ExpiresAt)
}

// ShouldSend checks if the notification should be sent now
func (n *Notification) ShouldSend() bool {
	if n.IsSent || n.IsExpired() {
		return false
	}
	
	if n.ScheduledFor != nil && time.Now().Before(*n.ScheduledFor) {
		return false
	}
	
	return true
}