package entities

import (
	"time"
	
	"github.com/google/uuid"
)

// Comment represents a comment or note on various entities
type Comment struct {
	BaseEntity
	Content     string       `json:"content" gorm:"type:text;not null" validate:"required"`
	IsInternal  bool         `json:"is_internal" gorm:"default:false"` // Internal notes vs customer-visible
	IsPinned    bool         `json:"is_pinned" gorm:"default:false"`
	
	// Author
	UserID      uuid.UUID    `json:"user_id" gorm:"type:uuid;not null"`
	User        *User        `json:"user,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	
	// Polymorphic associations (comment can be on card, contact, company, etc)
	EntityType  string       `json:"entity_type" gorm:"not null" validate:"required,oneof=card contact company pipeline"`
	EntityID    uuid.UUID    `json:"entity_id" gorm:"type:uuid;not null"`
	
	// Direct associations for easier querying
	CardID      *uuid.UUID   `json:"card_id,omitempty" gorm:"type:uuid;index"`
	Card        *Card        `json:"card,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	ContactID   *uuid.UUID   `json:"contact_id,omitempty" gorm:"type:uuid;index"`
	Contact     *Contact     `json:"contact,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	CompanyID   *uuid.UUID   `json:"company_id,omitempty" gorm:"type:uuid;index"`
	Company     *Company     `json:"company,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	
	// Parent comment for threading
	ParentID    *uuid.UUID   `json:"parent_id,omitempty" gorm:"type:uuid;index"`
	Parent      *Comment     `json:"parent,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	Replies     []Comment    `json:"replies,omitempty" gorm:"foreignKey:ParentID"`
	
	// Mentions
	MentionedUsers []User    `json:"mentioned_users,omitempty" gorm:"many2many:comment_mentions"`
	
	// Attachments
	Attachments []Attachment `json:"attachments,omitempty" gorm:"many2many:comment_attachments"`
	
	// Reactions (optional)
	Reactions   CustomFields `json:"reactions" gorm:"type:jsonb;default:'{}'"`
	
	// Edit history
	EditedAt    *time.Time   `json:"edited_at,omitempty"`
	EditCount   int          `json:"edit_count" gorm:"default:0"`
	EditHistory []CommentEdit `json:"edit_history,omitempty" gorm:"foreignKey:CommentID"`
}

// CommentEdit represents an edit history entry for a comment
type CommentEdit struct {
	BaseEntity
	CommentID   uuid.UUID    `json:"comment_id" gorm:"type:uuid;not null"`
	Comment     *Comment     `json:"comment,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	OldContent  string       `json:"old_content" gorm:"type:text"`
	NewContent  string       `json:"new_content" gorm:"type:text"`
	EditedByID  uuid.UUID    `json:"edited_by_id" gorm:"type:uuid;not null"`
	EditedBy    *User        `json:"edited_by,omitempty" gorm:"constraint:OnDelete:CASCADE"`
}

// SetEntity sets the entity type and ID based on the provided entity
func (c *Comment) SetEntity(entityType string, entityID uuid.UUID) {
	c.EntityType = entityType
	c.EntityID = entityID
	
	switch entityType {
	case "card":
		c.CardID = &entityID
	case "contact":
		c.ContactID = &entityID
	case "company":
		c.CompanyID = &entityID
	}
}

// AddReaction adds a reaction to the comment
func (c *Comment) AddReaction(userID string, reaction string) {
	if c.Reactions == nil {
		c.Reactions = make(CustomFields)
	}
	
	reactions, exists := c.Reactions[reaction].(map[string]interface{})
	if !exists {
		reactions = make(map[string]interface{})
	}
	
	reactions[userID] = true
	c.Reactions[reaction] = reactions
}

// RemoveReaction removes a reaction from the comment
func (c *Comment) RemoveReaction(userID string, reaction string) {
	if c.Reactions == nil {
		return
	}
	
	reactions, exists := c.Reactions[reaction].(map[string]interface{})
	if exists {
		delete(reactions, userID)
		if len(reactions) == 0 {
			delete(c.Reactions, reaction)
		} else {
			c.Reactions[reaction] = reactions
		}
	}
}

// IsEdited returns true if the comment has been edited
func (c *Comment) IsEdited() bool {
	return c.EditCount > 0
}