package entities

import (
	"time"
	
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// User represents a system user
type User struct {
	BaseEntity
	Email          string     `json:"email" gorm:"uniqueIndex;not null" validate:"required,email"`
	Password       string     `json:"-" validate:"required,min=8"`
	FirstName      string     `json:"first_name" validate:"required"`
	LastName       string     `json:"last_name" validate:"required"`
	RoleID         *uuid.UUID `json:"role_id" gorm:"type:uuid"`
	Role           *Role      `json:"role,omitempty" gorm:"foreignKey:RoleID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	LegacyRole     string     `json:"legacy_role,omitempty" gorm:"column:legacy_role"` // Keep for backward compatibility
	IsActive       bool       `json:"is_active" gorm:"default:true"`
	EmailVerified  bool       `json:"email_verified" gorm:"default:false"`
	TwoFactorEnabled bool     `json:"two_factor_enabled" gorm:"default:false"`
	LastLoginAt    *time.Time `json:"last_login_at,omitempty"`
	LastActivityAt *time.Time `json:"last_activity_at,omitempty"`
	Phone          string     `json:"phone,omitempty"`
	Avatar         string     `json:"avatar,omitempty"`
	Department     string     `json:"department,omitempty"`
	Position       string     `json:"position,omitempty"`
	Location       string     `json:"location,omitempty"`
	Timezone       string     `json:"timezone,omitempty" gorm:"default:'UTC'"`
	Language       string     `json:"language,omitempty" gorm:"default:'ru'"`
}

// BeforeCreate hashes the password before creating the user
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if err := u.BaseEntity.BeforeCreate(tx); err != nil {
		return err
	}
	
	if u.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		u.Password = string(hashedPassword)
	}
	return nil
}

// SetPassword sets the password for the user (will be hashed in BeforeCreate hook)
func (u *User) SetPassword(password string) {
	u.Password = password
}

// CheckPassword verifies the password
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// FullName returns the full name of the user
func (u *User) FullName() string {
	return u.FirstName + " " + u.LastName
}

// HasPermission checks if user has a specific permission
func (u *User) HasPermission(resource, action string) bool {
	if u.Role == nil {
		// Check legacy role for backward compatibility
		if u.LegacyRole == "admin" {
			return true
		}
		return false
	}
	return u.Role.HasPermission(resource, action)
}

// IsAdmin checks if user is an administrator
func (u *User) IsAdmin() bool {
	if u.Role != nil && u.Role.Name == "Administrator" {
		return true
	}
	// Backward compatibility
	return u.LegacyRole == "admin"
}

// UpdateActivity updates the last activity timestamp
func (u *User) UpdateActivity() {
	now := time.Now()
	u.LastActivityAt = &now
}

// UpdateLastLogin updates the last login timestamp
func (u *User) UpdateLastLogin() {
	now := time.Now()
	u.LastLoginAt = &now
	u.UpdateActivity()
}