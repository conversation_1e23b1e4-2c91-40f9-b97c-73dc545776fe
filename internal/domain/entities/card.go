package entities

import (
	"time"

	"github.com/google/uuid"
)

// CardPriority represents the priority of a card
type CardPriority string

const (
	PriorityLow    CardPriority = "low"
	PriorityMedium CardPriority = "medium"
	PriorityHigh   CardPriority = "high"
	PriorityUrgent CardPriority = "urgent"
)

// Card represents a deal/opportunity card in the CRM
type Card struct {
	BaseEntity
	Title        string        `json:"title" gorm:"not null" validate:"required"`
	Description  string        `json:"description"`
	Value        float64       `json:"value" gorm:"default:0"`
	Currency     string        `json:"currency" gorm:"default:'USD'"`
	Priority     CardPriority  `json:"priority" gorm:"default:'medium'" validate:"omitempty,oneof=low medium high urgent"`
	PipelineID   *uuid.UUID    `json:"pipeline_id,omitempty" gorm:"type:uuid;index"`
	Pipeline     *Pipeline     `json:"pipeline,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	StageID      *uuid.UUID    `json:"stage_id,omitempty" gorm:"type:uuid;index"`
	Stage        *Stage        `json:"stage,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	ContactID    *uuid.UUID    `json:"contact_id,omitempty" gorm:"type:uuid;index"`
	Contact      *Contact      `json:"contact,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	CompanyID    *uuid.UUID    `json:"company_id,omitempty" gorm:"type:uuid;index"`
	Company      *Company      `json:"company,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	AssignedToID *uuid.UUID    `json:"assigned_to_id,omitempty" gorm:"type:uuid;index"`
	AssignedTo   *User         `json:"assigned_to,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	ExpectedCloseDate *time.Time `json:"expected_close_date,omitempty"`
	ActualCloseDate   *time.Time `json:"actual_close_date,omitempty"`
	StageEnteredAt    *time.Time `json:"stage_entered_at,omitempty"` // When entered current stage
	LostReason        string     `json:"lost_reason,omitempty"`       // Reason if closed lost
	WonAmount         float64    `json:"won_amount,omitempty"`        // Final amount if closed won
	CustomFields CustomFields  `json:"custom_fields" gorm:"type:jsonb;default:'{}'"`
	Activities   []Activity    `json:"activities,omitempty" gorm:"foreignKey:CardID"`
	Tags         []Tag         `json:"tags,omitempty" gorm:"many2many:card_tags;"`
	Attachments  []Attachment  `json:"attachments,omitempty" gorm:"foreignKey:CardID"`
}

// IsOverdue returns true if the card is overdue
func (c *Card) IsOverdue() bool {
	if c.ExpectedCloseDate == nil {
		return false
	}
	return time.Now().After(*c.ExpectedCloseDate) && c.ActualCloseDate == nil
}

// IsClosed returns true if the card has been closed
func (c *Card) IsClosed() bool {
	return c.ActualCloseDate != nil
}

// SetCustomField sets a custom field value
func (c *Card) SetCustomField(key string, value interface{}) {
	if c.CustomFields == nil {
		c.CustomFields = make(CustomFields)
	}
	c.CustomFields[key] = value
}

// GetCustomField gets a custom field value
func (c *Card) GetCustomField(key string) (interface{}, bool) {
	if c.CustomFields == nil {
		return nil, false
	}
	val, exists := c.CustomFields[key]
	return val, exists
}

// GetPriorityWeight returns the numeric weight of the priority for sorting
func (c *Card) GetPriorityWeight() int {
	switch c.Priority {
	case PriorityUrgent:
		return 4
	case PriorityHigh:
		return 3
	case PriorityMedium:
		return 2
	case PriorityLow:
		return 1
	default:
		return 0
	}
}