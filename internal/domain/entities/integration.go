package entities

import (
	"time"

	"github.com/google/uuid"
)

// IntegrationType represents the type of integration
type IntegrationType string

const (
	IntegrationTypeEmail      IntegrationType = "email"
	IntegrationTypeSMS        IntegrationType = "sms"
	IntegrationTypeWebhook    IntegrationType = "webhook"
	IntegrationTypeCalendar   IntegrationType = "calendar"
	IntegrationTypeMessenger  IntegrationType = "messenger"
	IntegrationTypeWhatsApp   IntegrationType = "whatsapp"
	IntegrationTypeTelegram   IntegrationType = "telegram"
	IntegrationTypeSlack      IntegrationType = "slack"
	IntegrationTypeZapier     IntegrationType = "zapier"
	IntegrationTypeGoogleDrive IntegrationType = "google_drive"
	IntegrationTypeDropbox    IntegrationType = "dropbox"
	IntegrationTypeMailchimp  IntegrationType = "mailchimp"
	IntegrationTypeSendGrid   IntegrationType = "sendgrid"
	IntegrationTypeStripe     IntegrationType = "stripe"
)

// IntegrationProvider represents a specific provider for an integration type
type IntegrationProvider string

const (
	// Email providers
	EmailProviderSMTP      IntegrationProvider = "smtp"
	EmailProviderSendGrid  IntegrationProvider = "sendgrid"
	EmailProviderMailgun   IntegrationProvider = "mailgun"
	EmailProviderAWS_SES   IntegrationProvider = "aws_ses"
	EmailProviderPostmark  IntegrationProvider = "postmark"
	
	// SMS providers
	SMSProviderTwilio      IntegrationProvider = "twilio"
	SMSProviderVonage      IntegrationProvider = "vonage"
	SMSProviderAWS_SNS     IntegrationProvider = "aws_sns"
	
	// Calendar providers
	CalendarProviderGoogle IntegrationProvider = "google_calendar"
	CalendarProviderOutlook IntegrationProvider = "outlook_calendar"
	CalendarProviderCalDAV IntegrationProvider = "caldav"
)

// Integration represents an integration configuration
type Integration struct {
	BaseEntity
	
	// Basic info
	Name        string              `json:"name" gorm:"not null;unique" validate:"required"`
	Type        IntegrationType     `json:"type" gorm:"not null" validate:"required"`
	Provider    IntegrationProvider `json:"provider" gorm:"not null" validate:"required"`
	Description string              `json:"description"`
	Icon        string              `json:"icon"`
	IsEnabled   bool                `json:"is_enabled" gorm:"default:false"`
	IsDefault   bool                `json:"is_default" gorm:"default:false"` // Default for its type
	
	// Configuration (encrypted in database)
	Config      map[string]interface{} `json:"config" gorm:"type:jsonb;default:'{}'"`
	
	// Connection status
	IsConnected bool                `json:"is_connected" gorm:"default:false"`
	LastTestAt  *time.Time          `json:"last_test_at,omitempty"`
	LastTestOK  bool                `json:"last_test_ok" gorm:"default:false"`
	LastError   string              `json:"last_error"`
	
	// Usage statistics
	UsageCount  int64               `json:"usage_count" gorm:"default:0"`
	LastUsedAt  *time.Time          `json:"last_used_at,omitempty"`
	
	// Permissions
	AllowedRoles []string           `json:"allowed_roles" gorm:"type:jsonb"`
	AllowedUsers []uuid.UUID        `json:"allowed_users" gorm:"type:jsonb"`
	
	// Created by
	CreatedByID *uuid.UUID          `json:"created_by_id,omitempty" gorm:"type:uuid"`
	CreatedBy   *User               `json:"created_by,omitempty" gorm:"constraint:OnDelete:SET NULL"`
}

// EmailIntegrationConfig represents email-specific configuration
type EmailIntegrationConfig struct {
	// SMTP Configuration
	SMTPHost     string `json:"smtp_host" validate:"required_if=Provider smtp"`
	SMTPPort     int    `json:"smtp_port" validate:"required_if=Provider smtp,min=1,max=65535"`
	SMTPUsername string `json:"smtp_username"`
	SMTPPassword string `json:"smtp_password" sensitive:"true"`
	SMTPFrom     string `json:"smtp_from" validate:"required,email"`
	SMTPFromName string `json:"smtp_from_name"`
	UseTLS       bool   `json:"use_tls"`
	UseStartTLS  bool   `json:"use_starttls"`
	
	// SendGrid Configuration
	SendGridAPIKey string `json:"sendgrid_api_key" validate:"required_if=Provider sendgrid" sensitive:"true"`
	
	// Mailgun Configuration
	MailgunAPIKey    string `json:"mailgun_api_key" validate:"required_if=Provider mailgun" sensitive:"true"`
	MailgunDomain    string `json:"mailgun_domain" validate:"required_if=Provider mailgun"`
	MailgunEURegion  bool   `json:"mailgun_eu_region"`
	
	// AWS SES Configuration
	AWSRegion        string `json:"aws_region" validate:"required_if=Provider aws_ses"`
	AWSAccessKeyID   string `json:"aws_access_key_id" validate:"required_if=Provider aws_ses" sensitive:"true"`
	AWSSecretKey     string `json:"aws_secret_key" validate:"required_if=Provider aws_ses" sensitive:"true"`
	
	// Common settings
	MaxRetries       int    `json:"max_retries" validate:"min=0,max=10"`
	RetryDelay       int    `json:"retry_delay" validate:"min=1,max=60"`
	SendTimeout      int    `json:"send_timeout" validate:"min=5,max=300"`
	RateLimit        int    `json:"rate_limit" validate:"min=0,max=1000"`
	EnableTracking   bool   `json:"enable_tracking"`
	TrackingDomain   string `json:"tracking_domain"`
	UnsubscribeURL   string `json:"unsubscribe_url"`
	EnableQueue      bool   `json:"enable_queue"`
	QueueWorkers     int    `json:"queue_workers" validate:"min=1,max=10"`
}

// SMSIntegrationConfig represents SMS-specific configuration
type SMSIntegrationConfig struct {
	// Twilio Configuration
	TwilioAccountSID  string `json:"twilio_account_sid" validate:"required_if=Provider twilio" sensitive:"true"`
	TwilioAuthToken   string `json:"twilio_auth_token" validate:"required_if=Provider twilio" sensitive:"true"`
	TwilioPhoneNumber string `json:"twilio_phone_number" validate:"required_if=Provider twilio"`
	
	// Vonage Configuration
	VonageAPIKey    string `json:"vonage_api_key" validate:"required_if=Provider vonage" sensitive:"true"`
	VonageAPISecret string `json:"vonage_api_secret" validate:"required_if=Provider vonage" sensitive:"true"`
	VonageFrom      string `json:"vonage_from" validate:"required_if=Provider vonage"`
	
	// Common settings
	MaxRetries  int  `json:"max_retries" validate:"min=0,max=10"`
	RetryDelay  int  `json:"retry_delay" validate:"min=1,max=60"`
	SendTimeout int  `json:"send_timeout" validate:"min=5,max=60"`
	RateLimit   int  `json:"rate_limit" validate:"min=0,max=100"`
	EnableQueue bool `json:"enable_queue"`
}

// WebhookIntegrationConfig represents webhook-specific configuration
type WebhookIntegrationConfig struct {
	WebhookURL       string            `json:"webhook_url" validate:"required,url"`
	Method           string            `json:"method" validate:"required,oneof=GET POST PUT PATCH DELETE"`
	Headers          map[string]string `json:"headers"`
	AuthType         string            `json:"auth_type" validate:"oneof=none basic bearer api_key oauth2"`
	AuthUsername     string            `json:"auth_username" sensitive:"true"`
	AuthPassword     string            `json:"auth_password" sensitive:"true"`
	AuthToken        string            `json:"auth_token" sensitive:"true"`
	AuthAPIKey       string            `json:"auth_api_key" sensitive:"true"`
	AuthAPIKeyHeader string            `json:"auth_api_key_header"`
	
	// OAuth2 settings
	OAuth2ClientID     string `json:"oauth2_client_id" sensitive:"true"`
	OAuth2ClientSecret string `json:"oauth2_client_secret" sensitive:"true"`
	OAuth2TokenURL     string `json:"oauth2_token_url"`
	OAuth2Scopes       string `json:"oauth2_scopes"`
	
	// Retry settings
	MaxRetries       int  `json:"max_retries" validate:"min=0,max=10"`
	RetryDelay       int  `json:"retry_delay" validate:"min=1,max=60"`
	Timeout          int  `json:"timeout" validate:"min=5,max=300"`
	VerifySSL        bool `json:"verify_ssl"`
	
	// Event filtering
	EnabledEvents    []string `json:"enabled_events"`
	FilterExpression string   `json:"filter_expression"`
}

// IntegrationLog represents a log entry for integration activities
type IntegrationLog struct {
	BaseEntity
	
	IntegrationID uuid.UUID       `json:"integration_id" gorm:"type:uuid;not null;index"`
	Integration   *Integration    `json:"integration,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	
	Action        string          `json:"action" gorm:"not null"` // send_email, send_sms, webhook_call, etc.
	Status        string          `json:"status" gorm:"not null"` // success, failure, pending
	Request       CustomFields    `json:"request" gorm:"type:jsonb"`
	Response      CustomFields    `json:"response" gorm:"type:jsonb"`
	Error         string          `json:"error"`
	Duration      int             `json:"duration"` // in milliseconds
	
	// Related entities
	EntityType    string          `json:"entity_type"`
	EntityID      *uuid.UUID      `json:"entity_id,omitempty" gorm:"type:uuid"`
	
	UserID        *uuid.UUID      `json:"user_id,omitempty" gorm:"type:uuid"`
	User          *User           `json:"user,omitempty" gorm:"constraint:OnDelete:SET NULL"`
}

// IsConfigured checks if the integration has required configuration
func (i *Integration) IsConfigured() bool {
	if i.Config == nil || len(i.Config) == 0 {
		return false
	}
	
	// Check based on type and provider
	switch i.Type {
	case IntegrationTypeEmail:
		return i.validateEmailConfig()
	case IntegrationTypeSMS:
		return i.validateSMSConfig()
	case IntegrationTypeWebhook:
		return i.validateWebhookConfig()
	default:
		return true // Other types might not need configuration
	}
}

// validateEmailConfig validates email configuration
func (i *Integration) validateEmailConfig() bool {
	switch i.Provider {
	case EmailProviderSMTP:
		return i.Config["smtp_host"] != nil && i.Config["smtp_port"] != nil && i.Config["smtp_from"] != nil
	case EmailProviderSendGrid:
		return i.Config["sendgrid_api_key"] != nil
	case EmailProviderMailgun:
		return i.Config["mailgun_api_key"] != nil && i.Config["mailgun_domain"] != nil
	case EmailProviderAWS_SES:
		return i.Config["aws_region"] != nil && i.Config["aws_access_key_id"] != nil && i.Config["aws_secret_key"] != nil
	default:
		return false
	}
}

// validateSMSConfig validates SMS configuration
func (i *Integration) validateSMSConfig() bool {
	switch i.Provider {
	case SMSProviderTwilio:
		return i.Config["twilio_account_sid"] != nil && i.Config["twilio_auth_token"] != nil && i.Config["twilio_phone_number"] != nil
	case SMSProviderVonage:
		return i.Config["vonage_api_key"] != nil && i.Config["vonage_api_secret"] != nil
	default:
		return false
	}
}

// validateWebhookConfig validates webhook configuration
func (i *Integration) validateWebhookConfig() bool {
	return i.Config["webhook_url"] != nil && i.Config["method"] != nil
}

// GetSensitiveFields returns list of fields that should be encrypted
func (i *Integration) GetSensitiveFields() []string {
	switch i.Type {
	case IntegrationTypeEmail:
		return []string{
			"smtp_password", "sendgrid_api_key", "mailgun_api_key",
			"aws_access_key_id", "aws_secret_key",
		}
	case IntegrationTypeSMS:
		return []string{
			"twilio_account_sid", "twilio_auth_token",
			"vonage_api_key", "vonage_api_secret",
		}
	case IntegrationTypeWebhook:
		return []string{
			"auth_username", "auth_password", "auth_token", "auth_api_key",
			"oauth2_client_id", "oauth2_client_secret",
		}
	default:
		return []string{}
	}
}