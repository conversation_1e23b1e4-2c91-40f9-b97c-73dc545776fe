package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Role struct {
	BaseEntity
	Name         string       `json:"name" gorm:"uniqueIndex;not null" validate:"required,min=2,max=50"`
	Description  string       `json:"description" validate:"max=255"`
	Color        string       `json:"color" gorm:"default:'#3B82F6'"`
	IsSystem     bool         `json:"is_system" gorm:"default:false"`
	IsActive     bool         `json:"is_active" gorm:"default:true"`
	Permissions  []Permission `json:"permissions" gorm:"many2many:role_permissions;"`
	Users        []User       `json:"users,omitempty" gorm:"foreignKey:RoleID"`
	UserCount    int          `json:"user_count" gorm:"-"`
}

func (r *Role) BeforeCreate(tx *gorm.DB) error {
	if r.ID == uuid.Nil {
		r.ID = uuid.New()
	}
	return nil
}

func (r *Role) TableName() string {
	return "roles"
}

func (r *Role) CanDelete() bool {
	return !r.IsSystem
}

func (r *Role) HasPermission(resource, action string) bool {
	for _, permission := range r.Permissions {
		if permission.Resource == resource && permission.Action == action {
			return true
		}
		if permission.Resource == resource && permission.Action == "manage" {
			return true
		}
		if permission.Resource == "*" && permission.Action == "*" {
			return true
		}
	}
	return false
}

type RolePermission struct {
	RoleID       uuid.UUID `json:"role_id" gorm:"primaryKey;type:uuid"`
	PermissionID uuid.UUID `json:"permission_id" gorm:"primaryKey;type:uuid"`
}

func (RolePermission) TableName() string {
	return "role_permissions"
}