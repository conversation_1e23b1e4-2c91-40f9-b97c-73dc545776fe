package entities

import (
	"database/sql/driver"
	"encoding/json"
	"github.com/google/uuid"
)

// CustomFields represents JSONB custom fields
type CustomFields map[string]interface{}

// <PERSON><PERSON> implements the Scanner interface for GORM
func (cf *CustomFields) Scan(value interface{}) error {
	if value == nil {
		*cf = make(CustomFields)
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, cf)
	case string:
		return json.Unmarshal([]byte(v), cf)
	default:
		*cf = make(CustomFields)
		return nil
	}
}

// Value implements the driver Valuer interface for GORM
func (cf CustomFields) Value() (driver.Value, error) {
	if cf == nil {
		return nil, nil
	}
	return json.Marshal(cf)
}

// Contact represents a contact in the CRM
type Contact struct {
	BaseEntity
	FirstName    string       `json:"first_name" gorm:"not null" validate:"required"`
	LastName     string       `json:"last_name" gorm:"not null" validate:"required"`
	Email        string       `json:"email" gorm:"uniqueIndex" validate:"omitempty,email"`
	Phone        string       `json:"phone"`
	JobTitle     string       `json:"job_title"`
	CompanyID    *uuid.UUID   `json:"company_id,omitempty" gorm:"type:uuid;index"`
	Company      *Company     `json:"company,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	CustomFields CustomFields `json:"custom_fields" gorm:"type:jsonb;default:'{}'"`
	Cards        []Card       `json:"cards,omitempty" gorm:"foreignKey:ContactID"`
	Activities   []Activity   `json:"activities,omitempty" gorm:"foreignKey:ContactID"`
}

// FullName returns the full name of the contact
func (c *Contact) FullName() string {
	return c.FirstName + " " + c.LastName
}

// SetCustomField sets a custom field value
func (c *Contact) SetCustomField(key string, value interface{}) {
	if c.CustomFields == nil {
		c.CustomFields = make(CustomFields)
	}
	c.CustomFields[key] = value
}

// GetCustomField gets a custom field value
func (c *Contact) GetCustomField(key string) (interface{}, bool) {
	if c.CustomFields == nil {
		return nil, false
	}
	val, exists := c.CustomFields[key]
	return val, exists
}