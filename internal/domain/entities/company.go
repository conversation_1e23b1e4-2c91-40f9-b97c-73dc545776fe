package entities

// Company represents a company in the CRM
type Company struct {
	BaseEntity
	Name         string       `json:"name" gorm:"not null;uniqueIndex" validate:"required"`
	Website      string       `json:"website" validate:"omitempty,url"`
	Industry     string       `json:"industry"`
	Size         string       `json:"size"`
	Description  string       `json:"description" gorm:"type:text"`
	Address      string       `json:"address"`
	City         string       `json:"city"`
	State        string       `json:"state"`
	Country      string       `json:"country"`
	PostalCode   string       `json:"postal_code"`
	Phone        string       `json:"phone"`
	Email        string       `json:"email" validate:"omitempty,email"`
	CustomFields CustomFields `json:"custom_fields" gorm:"type:jsonb;default:'{}'"`
	Contacts     []Contact    `json:"contacts,omitempty" gorm:"foreignKey:CompanyID"`
	Cards        []Card       `json:"cards,omitempty" gorm:"foreignKey:CompanyID"`
}

// SetCustomField sets a custom field value
func (c *Company) SetCustomField(key string, value interface{}) {
	if c.CustomFields == nil {
		c.CustomFields = make(CustomFields)
	}
	c.CustomFields[key] = value
}

// GetCustomField gets a custom field value
func (c *Company) GetCustomField(key string) (interface{}, bool) {
	if c.CustomFields == nil {
		return nil, false
	}
	val, exists := c.CustomFields[key]
	return val, exists
}

// GetFullAddress returns the complete address
func (c *Company) GetFullAddress() string {
	address := c.Address
	if c.City != "" {
		address += ", " + c.City
	}
	if c.State != "" {
		address += ", " + c.State
	}
	if c.Country != "" {
		address += ", " + c.Country
	}
	if c.PostalCode != "" {
		address += " " + c.PostalCode
	}
	return address
}