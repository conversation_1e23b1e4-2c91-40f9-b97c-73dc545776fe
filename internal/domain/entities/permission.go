package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Permission struct {
	BaseEntity
	Resource    string `json:"resource" gorm:"not null;index:idx_permission_resource_action" validate:"required"`
	Action      string `json:"action" gorm:"not null;index:idx_permission_resource_action" validate:"required"`
	Description string `json:"description"`
	Roles       []Role `json:"roles,omitempty" gorm:"many2many:role_permissions;"`
}

func (p *Permission) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

func (p *Permission) TableName() string {
	return "permissions"
}

const (
	ActionRead   = "read"
	ActionCreate = "create"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionManage = "manage"
	ActionExport = "export"
	ActionImport = "import"
	ActionAll    = "*"
)

const (
	ResourcePipelines  = "pipelines"
	ResourceCards      = "cards"
	ResourceContacts   = "contacts"
	ResourceCompanies  = "companies"
	ResourceUsers      = "users"
	ResourceRoles      = "roles"
	ResourceSettings   = "settings"
	ResourceReports    = "reports"
	ResourceDashboard  = "dashboard"
	ResourceAll        = "*"
)

var DefaultPermissions = []Permission{
	{Resource: ResourcePipelines, Action: ActionRead, Description: "View pipelines"},
	{Resource: ResourcePipelines, Action: ActionCreate, Description: "Create pipelines"},
	{Resource: ResourcePipelines, Action: ActionUpdate, Description: "Update pipelines"},
	{Resource: ResourcePipelines, Action: ActionDelete, Description: "Delete pipelines"},
	{Resource: ResourcePipelines, Action: ActionManage, Description: "Full pipeline management"},

	{Resource: ResourceCards, Action: ActionRead, Description: "View cards"},
	{Resource: ResourceCards, Action: ActionCreate, Description: "Create cards"},
	{Resource: ResourceCards, Action: ActionUpdate, Description: "Update cards"},
	{Resource: ResourceCards, Action: ActionDelete, Description: "Delete cards"},
	{Resource: ResourceCards, Action: ActionManage, Description: "Full card management"},
	{Resource: ResourceCards, Action: ActionExport, Description: "Export cards data"},
	{Resource: ResourceCards, Action: ActionImport, Description: "Import cards data"},

	{Resource: ResourceContacts, Action: ActionRead, Description: "View contacts"},
	{Resource: ResourceContacts, Action: ActionCreate, Description: "Create contacts"},
	{Resource: ResourceContacts, Action: ActionUpdate, Description: "Update contacts"},
	{Resource: ResourceContacts, Action: ActionDelete, Description: "Delete contacts"},
	{Resource: ResourceContacts, Action: ActionManage, Description: "Full contact management"},
	{Resource: ResourceContacts, Action: ActionExport, Description: "Export contacts"},
	{Resource: ResourceContacts, Action: ActionImport, Description: "Import contacts"},

	{Resource: ResourceCompanies, Action: ActionRead, Description: "View companies"},
	{Resource: ResourceCompanies, Action: ActionCreate, Description: "Create companies"},
	{Resource: ResourceCompanies, Action: ActionUpdate, Description: "Update companies"},
	{Resource: ResourceCompanies, Action: ActionDelete, Description: "Delete companies"},
	{Resource: ResourceCompanies, Action: ActionManage, Description: "Full company management"},

	{Resource: ResourceUsers, Action: ActionRead, Description: "View users"},
	{Resource: ResourceUsers, Action: ActionCreate, Description: "Create users"},
	{Resource: ResourceUsers, Action: ActionUpdate, Description: "Update users"},
	{Resource: ResourceUsers, Action: ActionDelete, Description: "Delete users"},
	{Resource: ResourceUsers, Action: ActionManage, Description: "Full user management"},

	{Resource: ResourceRoles, Action: ActionRead, Description: "View roles"},
	{Resource: ResourceRoles, Action: ActionCreate, Description: "Create roles"},
	{Resource: ResourceRoles, Action: ActionUpdate, Description: "Update roles"},
	{Resource: ResourceRoles, Action: ActionDelete, Description: "Delete roles"},
	{Resource: ResourceRoles, Action: ActionManage, Description: "Full role management"},

	{Resource: ResourceSettings, Action: ActionRead, Description: "View settings"},
	{Resource: ResourceSettings, Action: ActionUpdate, Description: "Update settings"},
	{Resource: ResourceSettings, Action: ActionManage, Description: "Full settings management"},

	{Resource: ResourceReports, Action: ActionRead, Description: "View reports"},
	{Resource: ResourceReports, Action: ActionCreate, Description: "Create reports"},
	{Resource: ResourceReports, Action: ActionExport, Description: "Export reports"},
	{Resource: ResourceReports, Action: ActionManage, Description: "Full report management"},

	{Resource: ResourceDashboard, Action: ActionRead, Description: "View dashboard"},
	{Resource: ResourceDashboard, Action: ActionManage, Description: "Manage dashboard widgets"},

	{Resource: ResourceAll, Action: ActionAll, Description: "Full system access"},
}