package entities

import (
	"time"

	"github.com/google/uuid"
)

// DataTransferType represents the type of data transfer
type DataTransferType string

const (
	DataTransferTypeImport DataTransferType = "import"
	DataTransferTypeExport DataTransferType = "export"
)

// DataTransferStatus represents the status of a data transfer
type DataTransferStatus string

const (
	DataTransferStatusPending    DataTransferStatus = "pending"
	DataTransferStatusProcessing DataTransferStatus = "processing"
	DataTransferStatusCompleted  DataTransferStatus = "completed"
	DataTransferStatusFailed     DataTransferStatus = "failed"
)

// DataTransferFormat represents the file format for transfer
type DataTransferFormat string

const (
	DataTransferFormatCSV  DataTransferFormat = "csv"
	DataTransferFormatXLSX DataTransferFormat = "xlsx"
	DataTransferFormatJSON DataTransferFormat = "json"
)

// DataTransfer represents an import/export operation
type DataTransfer struct {
	BaseEntity
	Type             DataTransferType   `json:"type" gorm:"not null;index" validate:"required,oneof=import export"`
	EntityType       string            `json:"entity_type" gorm:"not null;index" validate:"required,oneof=cards contacts companies"`
	FileName         string            `json:"file_name" gorm:"not null" validate:"required"`
	FilePath         string            `json:"file_path"`
	FileFormat       DataTransferFormat `json:"file_format" gorm:"not null" validate:"required,oneof=csv xlsx json"`
	TotalRecords     int              `json:"total_records" gorm:"default:0"`
	ProcessedRecords int              `json:"processed_records" gorm:"default:0"`
	SuccessRecords   int              `json:"success_records" gorm:"default:0"`
	FailedRecords    int              `json:"failed_records" gorm:"default:0"`
	SkippedRecords   int              `json:"skipped_records" gorm:"default:0"`
	Status           DataTransferStatus `json:"status" gorm:"default:'pending';index"`
	ErrorLog         CustomFields      `json:"error_log" gorm:"type:jsonb;default:'[]'"`
	Mapping          CustomFields      `json:"mapping" gorm:"type:jsonb;default:'{}'"`
	Filters          CustomFields      `json:"filters" gorm:"type:jsonb;default:'{}'"`
	Options          CustomFields      `json:"options" gorm:"type:jsonb;default:'{}'"`
	StartedAt        *time.Time       `json:"started_at,omitempty"`
	CompletedAt      *time.Time       `json:"completed_at,omitempty"`
	CreatedByID      uuid.UUID        `json:"created_by_id" gorm:"type:uuid;not null"`
	CreatedBy        *User            `json:"created_by,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	
	// Progress tracking
	Progress         float64          `json:"progress" gorm:"default:0"`
	EstimatedTimeLeft *time.Duration   `json:"estimated_time_left,omitempty" gorm:"-"`
}

// TableName specifies the table name for DataTransfer
func (DataTransfer) TableName() string {
	return "data_transfers"
}

// Start marks the transfer as started
func (dt *DataTransfer) Start() {
	now := time.Now()
	dt.StartedAt = &now
	dt.Status = DataTransferStatusProcessing
}

// Complete marks the transfer as completed
func (dt *DataTransfer) Complete() {
	now := time.Now()
	dt.CompletedAt = &now
	dt.Status = DataTransferStatusCompleted
	dt.Progress = 100
}

// Fail marks the transfer as failed
func (dt *DataTransfer) Fail(errorMessage string) {
	now := time.Now()
	dt.CompletedAt = &now
	dt.Status = DataTransferStatusFailed
	if dt.ErrorLog == nil {
		dt.ErrorLog = make(CustomFields)
	}
	dt.ErrorLog["fatal_error"] = errorMessage
}

// UpdateProgress updates the progress percentage
func (dt *DataTransfer) UpdateProgress() {
	if dt.TotalRecords > 0 {
		dt.Progress = float64(dt.ProcessedRecords) / float64(dt.TotalRecords) * 100
	}
}

// AddError adds an error to the error log
func (dt *DataTransfer) AddError(row int, field string, errorMessage string) {
	if dt.ErrorLog == nil {
		dt.ErrorLog = make(CustomFields)
	}
	
	errors, ok := dt.ErrorLog["errors"].([]interface{})
	if !ok {
		errors = []interface{}{}
	}
	
	error := map[string]interface{}{
		"row":     row,
		"field":   field,
		"message": errorMessage,
		"time":    time.Now(),
	}
	
	errors = append(errors, error)
	dt.ErrorLog["errors"] = errors
	dt.FailedRecords++
}

// GetDuration returns the duration of the transfer
func (dt *DataTransfer) GetDuration() *time.Duration {
	if dt.StartedAt == nil {
		return nil
	}
	
	endTime := time.Now()
	if dt.CompletedAt != nil {
		endTime = *dt.CompletedAt
	}
	
	duration := endTime.Sub(*dt.StartedAt)
	return &duration
}