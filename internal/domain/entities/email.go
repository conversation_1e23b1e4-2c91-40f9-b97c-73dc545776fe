package entities

import (
	"time"

	"github.com/google/uuid"
)

// EmailStatus represents the status of an email
type EmailStatus string

const (
	EmailStatusDraft   EmailStatus = "draft"
	EmailStatusSent    EmailStatus = "sent"
	EmailStatusReceived EmailStatus = "received"
	EmailStatusFailed  EmailStatus = "failed"
	EmailStatusBounced EmailStatus = "bounced"
	EmailStatusOpened  EmailStatus = "opened"
	EmailStatusClicked EmailStatus = "clicked"
)

// EmailType represents the type of email
type EmailType string

const (
	EmailTypeManual      EmailType = "manual"
	EmailTypeAutomated   EmailType = "automated"
	EmailTypeFollowUp    EmailType = "follow_up"
	EmailTypeNewsletter  EmailType = "newsletter"
	EmailTypeCampaign    EmailType = "campaign"
)

// Email represents an email communication
type Email struct {
	BaseEntity
	
	// Email metadata
	Subject     string      `json:"subject" gorm:"not null" validate:"required"`
	Body        string      `json:"body" gorm:"type:text" validate:"required"`
	BodyHTML    string      `json:"body_html" gorm:"type:text"`
	From        string      `json:"from" gorm:"not null" validate:"required,email"`
	To          []string    `json:"to" gorm:"type:jsonb" validate:"required,dive,email"`
	CC          []string    `json:"cc" gorm:"type:jsonb"`
	BCC         []string    `json:"bcc" gorm:"type:jsonb"`
	ReplyTo     string      `json:"reply_to" validate:"omitempty,email"`
	MessageID   string      `json:"message_id" gorm:"unique"`
	ThreadID    string      `json:"thread_id" gorm:"index"`
	
	// Email status and tracking
	Status      EmailStatus `json:"status" gorm:"not null;default:'draft'" validate:"required"`
	Type        EmailType   `json:"type" gorm:"not null;default:'manual'" validate:"required"`
	SentAt      *time.Time  `json:"sent_at,omitempty"`
	ReceivedAt  *time.Time  `json:"received_at,omitempty"`
	OpenedAt    *time.Time  `json:"opened_at,omitempty"`
	ClickedAt   *time.Time  `json:"clicked_at,omitempty"`
	BouncedAt   *time.Time  `json:"bounced_at,omitempty"`
	OpenCount   int         `json:"open_count" gorm:"default:0"`
	ClickCount  int         `json:"click_count" gorm:"default:0"`
	
	// Associations
	CardID      *uuid.UUID  `json:"card_id,omitempty" gorm:"type:uuid;index"`
	Card        *Card       `json:"card,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	ContactID   *uuid.UUID  `json:"contact_id,omitempty" gorm:"type:uuid;index"`
	Contact     *Contact    `json:"contact,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	CompanyID   *uuid.UUID  `json:"company_id,omitempty" gorm:"type:uuid;index"`
	Company     *Company    `json:"company,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	UserID      *uuid.UUID  `json:"user_id,omitempty" gorm:"type:uuid;index"`
	User        *User       `json:"user,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	
	// Email template
	TemplateID  *uuid.UUID  `json:"template_id,omitempty" gorm:"type:uuid"`
	Template    *EmailTemplate `json:"template,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	
	// Email attachments
	Attachments []Attachment `json:"attachments,omitempty" gorm:"many2many:email_attachments"`
	
	// Activity link
	ActivityID  *uuid.UUID  `json:"activity_id,omitempty" gorm:"type:uuid;unique"`
	Activity    *Activity   `json:"activity,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	
	// Additional metadata
	Metadata    CustomFields `json:"metadata" gorm:"type:jsonb;default:'{}'"`
	
	// Email sequence
	SequenceID  *uuid.UUID  `json:"sequence_id,omitempty" gorm:"type:uuid;index"`
	SequenceStep int        `json:"sequence_step" gorm:"default:0"`
}

// EmailTemplate represents an email template
type EmailTemplate struct {
	BaseEntity
	Name        string      `json:"name" gorm:"not null;unique" validate:"required"`
	Subject     string      `json:"subject" gorm:"not null" validate:"required"`
	Body        string      `json:"body" gorm:"type:text" validate:"required"`
	BodyHTML    string      `json:"body_html" gorm:"type:text"`
	Type        EmailType   `json:"type" gorm:"not null" validate:"required"`
	IsActive    bool        `json:"is_active" gorm:"default:true"`
	Variables   []string    `json:"variables" gorm:"type:jsonb"`
	Tags        []string    `json:"tags" gorm:"type:jsonb"`
	UsageCount  int         `json:"usage_count" gorm:"default:0"`
	CreatedByID *uuid.UUID  `json:"created_by_id,omitempty" gorm:"type:uuid"`
	CreatedBy   *User       `json:"created_by,omitempty" gorm:"constraint:OnDelete:SET NULL"`
}

// EmailSequence represents an automated email sequence
type EmailSequence struct {
	BaseEntity
	Name        string      `json:"name" gorm:"not null;unique" validate:"required"`
	Description string      `json:"description"`
	IsActive    bool        `json:"is_active" gorm:"default:false"`
	Trigger     string      `json:"trigger" validate:"required"` // stage_change, field_update, time_based
	Conditions  CustomFields `json:"conditions" gorm:"type:jsonb;default:'{}'"`
	Steps       []EmailSequenceStep `json:"steps,omitempty" gorm:"foreignKey:SequenceID"`
	CreatedByID *uuid.UUID  `json:"created_by_id,omitempty" gorm:"type:uuid"`
	CreatedBy   *User       `json:"created_by,omitempty" gorm:"constraint:OnDelete:SET NULL"`
}

// EmailSequenceStep represents a step in an email sequence
type EmailSequenceStep struct {
	BaseEntity
	SequenceID  uuid.UUID   `json:"sequence_id" gorm:"type:uuid;not null"`
	Sequence    *EmailSequence `json:"sequence,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	StepNumber  int         `json:"step_number" gorm:"not null"`
	DelayDays   int         `json:"delay_days" gorm:"default:0"`
	DelayHours  int         `json:"delay_hours" gorm:"default:0"`
	TemplateID  uuid.UUID   `json:"template_id" gorm:"type:uuid;not null"`
	Template    *EmailTemplate `json:"template,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	Conditions  CustomFields `json:"conditions" gorm:"type:jsonb;default:'{}'"`
}

// MarkAsOpened marks email as opened
func (e *Email) MarkAsOpened() {
	if e.OpenedAt == nil {
		now := time.Now()
		e.OpenedAt = &now
	}
	e.OpenCount++
}

// MarkAsClicked marks email as clicked
func (e *Email) MarkAsClicked() {
	if e.ClickedAt == nil {
		now := time.Now()
		e.ClickedAt = &now
	}
	e.ClickCount++
	// Also mark as opened if not already
	if e.OpenedAt == nil {
		e.MarkAsOpened()
	}
}

// MarkAsSent marks email as sent
func (e *Email) MarkAsSent() {
	now := time.Now()
	e.SentAt = &now
	e.Status = EmailStatusSent
}

// MarkAsFailed marks email as failed
func (e *Email) MarkAsFailed() {
	e.Status = EmailStatusFailed
}

// MarkAsBounced marks email as bounced
func (e *Email) MarkAsBounced() {
	now := time.Now()
	e.BouncedAt = &now
	e.Status = EmailStatusBounced
}