package entities

import (
	"time"

	"github.com/google/uuid"
)

// ActivityType represents the type of activity
type ActivityType string

const (
	ActivityTypeCall     ActivityType = "call"
	ActivityTypeEmail    ActivityType = "email"
	ActivityTypeMeeting  ActivityType = "meeting"
	ActivityTypeNote     ActivityType = "note"
	ActivityTypeTask     ActivityType = "task"
	ActivityTypeStageChange ActivityType = "stage_change"
	ActivityTypeValueChange ActivityType = "value_change"
	ActivityTypeCreated  ActivityType = "created"
	ActivityTypeUpdated  ActivityType = "updated"
)

// Activity represents an activity or history entry
type Activity struct {
	BaseEntity
	Type        ActivityType `json:"type" gorm:"not null" validate:"required"`
	Title       string       `json:"title" gorm:"not null" validate:"required"`
	Description string       `json:"description"`
	DueDate     *time.Time   `json:"due_date,omitempty"`
	CompletedAt *time.Time   `json:"completed_at,omitempty"`
	CardID      *uuid.UUID   `json:"card_id,omitempty" gorm:"type:uuid;index"`
	Card        *Card        `json:"card,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	ContactID   *uuid.UUID   `json:"contact_id,omitempty" gorm:"type:uuid;index"`
	Contact     *Contact     `json:"contact,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	CompanyID   *uuid.UUID   `json:"company_id,omitempty" gorm:"type:uuid;index"`
	Company     *Company     `json:"company,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	UserID      *uuid.UUID   `json:"user_id,omitempty" gorm:"type:uuid;index"`
	User        *User        `json:"user,omitempty" gorm:"constraint:OnDelete:SET NULL"`
	Metadata    CustomFields `json:"metadata" gorm:"type:jsonb;default:'{}'"`
}

// IsCompleted returns true if the activity is completed
func (a *Activity) IsCompleted() bool {
	return a.CompletedAt != nil
}

// IsOverdue returns true if the activity is overdue
func (a *Activity) IsOverdue() bool {
	if a.DueDate == nil || a.IsCompleted() {
		return false
	}
	return time.Now().After(*a.DueDate)
}

// Complete marks the activity as completed
func (a *Activity) Complete() {
	now := time.Now()
	a.CompletedAt = &now
}

// SetMetadata sets metadata for the activity
func (a *Activity) SetMetadata(key string, value interface{}) {
	if a.Metadata == nil {
		a.Metadata = make(CustomFields)
	}
	a.Metadata[key] = value
}

// GetMetadata gets metadata from the activity
func (a *Activity) GetMetadata(key string) (interface{}, bool) {
	if a.Metadata == nil {
		return nil, false
	}
	val, exists := a.Metadata[key]
	return val, exists
}