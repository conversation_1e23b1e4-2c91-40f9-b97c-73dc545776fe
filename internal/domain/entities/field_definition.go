package entities

// FieldType represents the type of a custom field
type FieldType string

const (
	FieldTypeText     FieldType = "text"
	FieldTypeNumber   FieldType = "number"
	FieldTypeEmail    FieldType = "email"
	FieldTypePhone    FieldType = "phone"
	FieldTypeDate     FieldType = "date"
	FieldTypeSelect   FieldType = "select"
	FieldTypeMultiSelect FieldType = "multi_select"
	FieldTypeBoolean  FieldType = "boolean"
	FieldTypeURL      FieldType = "url"
	FieldTypeTextArea FieldType = "textarea"
)

// FieldOptions represents options for select fields
type FieldOptions struct {
	Options []string `json:"options,omitempty"`
}

// FieldDefinition defines custom fields for entities
type FieldDefinition struct {
	BaseEntity
	Name        string       `json:"name" gorm:"not null" validate:"required"`
	Label       string       `json:"label" gorm:"not null" validate:"required"`
	Type        FieldType    `json:"type" gorm:"not null" validate:"required"`
	Required    bool         `json:"required" gorm:"default:false"`
	Options     FieldOptions `json:"options,omitempty" gorm:"type:jsonb"`
	EntityType  string       `json:"entity_type" gorm:"not null" validate:"required"` // "card", "contact", "company"
	Order       int          `json:"order" gorm:"default:0"`
	Description string       `json:"description"`
}

// IsValidForEntityType checks if the field definition is valid for the given entity type
func (fd *FieldDefinition) IsValidForEntityType(entityType string) bool {
	return fd.EntityType == entityType
}