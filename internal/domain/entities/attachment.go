package entities

import "github.com/google/uuid"

// Attachment represents a file attachment
type Attachment struct {
	BaseEntity
	FileName    string     `json:"file_name" gorm:"not null" validate:"required"`
	FilePath    string     `json:"file_path" gorm:"not null"`
	FileSize    int64      `json:"file_size" gorm:"not null"`
	MimeType    string     `json:"mime_type" gorm:"not null"`
	CardID      *uuid.UUID `json:"card_id,omitempty" gorm:"type:uuid;index"`
	Card        *Card      `json:"card,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	ContactID   *uuid.UUID `json:"contact_id,omitempty" gorm:"type:uuid;index"`
	Contact     *Contact   `json:"contact,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	CompanyID   *uuid.UUID `json:"company_id,omitempty" gorm:"type:uuid;index"`
	Company     *Company   `json:"company,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	UploadedByID uuid.UUID `json:"uploaded_by_id" gorm:"type:uuid;not null;index"`
	UploadedBy  *User      `json:"uploaded_by,omitempty" gorm:"constraint:OnDelete:CASCADE"`
}

// GetFileURL returns the URL to access the file
func (a *Attachment) GetFileURL() string {
	return "/api/v1/files/" + a.ID.String()
}

// IsImage returns true if the attachment is an image
func (a *Attachment) IsImage() bool {
	return a.MimeType == "image/jpeg" || a.MimeType == "image/jpg" || 
		   a.MimeType == "image/png" || a.MimeType == "image/gif" || 
		   a.MimeType == "image/webp"
}

// IsDocument returns true if the attachment is a document
func (a *Attachment) IsDocument() bool {
	return a.MimeType == "application/pdf" || a.MimeType == "application/msword" || 
		   a.MimeType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
		   a.MimeType == "application/vnd.ms-excel" ||
		   a.MimeType == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
}