package entities

import (
	"time"
)

// WebhookEvent represents the type of webhook event
type WebhookEvent string

const (
	WebhookEventCardCreated      WebhookEvent = "card.created"
	WebhookEventCardUpdated      WebhookEvent = "card.updated"
	WebhookEventCardDeleted      WebhookEvent = "card.deleted"
	WebhookEventCardStageChanged WebhookEvent = "card.stage_changed"
	WebhookEventContactCreated   WebhookEvent = "contact.created"
	WebhookEventContactUpdated   WebhookEvent = "contact.updated"
	WebhookEventContactDeleted   WebhookEvent = "contact.deleted"
	WebhookEventCompanyCreated   WebhookEvent = "company.created"
	WebhookEventCompanyUpdated   WebhookEvent = "company.updated"
	WebhookEventCompanyDeleted   WebhookEvent = "company.deleted"
)

// Webhook represents a webhook configuration
type Webhook struct {
	BaseEntity
	URL         string         `json:"url" gorm:"not null" validate:"required,url"`
	Events      []WebhookEvent `json:"events" gorm:"type:jsonb;not null"`
	Secret      string         `json:"-" gorm:"not null"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	Description string         `json:"description"`
	Headers     CustomFields   `json:"headers,omitempty" gorm:"type:jsonb;default:'{}'"`
}

// HasEvent checks if the webhook is configured for the given event
func (w *Webhook) HasEvent(event WebhookEvent) bool {
	for _, e := range w.Events {
		if e == event {
			return true
		}
	}
	return false
}

// WebhookDelivery represents a webhook delivery attempt
type WebhookDelivery struct {
	BaseEntity
	WebhookID    string       `json:"webhook_id" gorm:"type:uuid;not null;index"`
	Webhook      *Webhook     `json:"webhook,omitempty" gorm:"constraint:OnDelete:CASCADE"`
	Event        WebhookEvent `json:"event" gorm:"not null"`
	Payload      CustomFields `json:"payload" gorm:"type:jsonb;not null"`
	StatusCode   int          `json:"status_code"`
	Response     string       `json:"response"`
	Success      bool         `json:"success" gorm:"default:false"`
	AttemptCount int          `json:"attempt_count" gorm:"default:1"`
	NextRetryAt  *time.Time   `json:"next_retry_at,omitempty"`
}

// CanRetry returns true if the delivery can be retried
func (wd *WebhookDelivery) CanRetry() bool {
	return !wd.Success && wd.AttemptCount < 5
}

// ScheduleRetry schedules the next retry attempt
func (wd *WebhookDelivery) ScheduleRetry() {
	if !wd.CanRetry() {
		return
	}
	
	// Exponential backoff: 1min, 5min, 25min, 125min
	retryDelays := []time.Duration{
		time.Minute,
		5 * time.Minute,
		25 * time.Minute,
		125 * time.Minute,
	}
	
	if wd.AttemptCount-1 < len(retryDelays) {
		nextRetry := time.Now().Add(retryDelays[wd.AttemptCount-1])
		wd.NextRetryAt = &nextRetry
	}
}