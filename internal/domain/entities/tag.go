package entities

// Tag represents a tag that can be applied to cards
type Tag struct {
	BaseEntity
	Name  string `json:"name" gorm:"uniqueIndex;not null" validate:"required"`
	Color string `json:"color" gorm:"default:'#6B7280'"`
	Cards []Card `json:"cards,omitempty" gorm:"many2many:card_tags;"`
}

// CardTag represents the many-to-many relationship between cards and tags
type CardTag struct {
	CardID string `gorm:"primaryKey"`
	TagID  string `gorm:"primaryKey"`
}