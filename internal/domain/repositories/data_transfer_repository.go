package repositories

import (
	"context"
	"github.com/google/uuid"
	"crm-backend/internal/domain/entities"
)

type DataTransferRepository interface {
	Create(ctx context.Context, transfer *entities.DataTransfer) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.DataTransfer, error)
	Update(ctx context.Context, transfer *entities.DataTransfer) error
	List(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*entities.DataTransfer, int64, error)
	GetPending(ctx context.Context) ([]*entities.DataTransfer, error)
	GetByStatus(ctx context.Context, status entities.DataTransferStatus) ([]*entities.DataTransfer, error)
}