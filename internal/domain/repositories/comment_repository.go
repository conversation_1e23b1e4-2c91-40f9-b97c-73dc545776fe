package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// CommentRepository defines the interface for comment data access
type CommentRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, comment *entities.Comment) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Comment, error)
	Update(ctx context.Context, comment *entities.Comment) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Query operations
	List(ctx context.Context, filter CommentFilter) ([]*entities.Comment, error)
	ListWithPagination(ctx context.Context, filter CommentFilter, limit, offset int) ([]*entities.Comment, int64, error)
	
	// Entity-specific operations
	GetByEntity(ctx context.Context, entityType string, entityID uuid.UUID) ([]*entities.Comment, error)
	GetByEntityWithReplies(ctx context.Context, entityType string, entityID uuid.UUID) ([]*entities.Comment, error)
	CountByEntity(ctx context.Context, entityType string, entityID uuid.UUID) (int64, error)
	
	// Card comments
	GetByCardID(ctx context.Context, cardID uuid.UUID) ([]*entities.Comment, error)
	GetCardCommentCount(ctx context.Context, cardID uuid.UUID) (int64, error)
	GetPinnedCardComments(ctx context.Context, cardID uuid.UUID) ([]*entities.Comment, error)
	
	// Contact comments
	GetByContactID(ctx context.Context, contactID uuid.UUID) ([]*entities.Comment, error)
	GetContactCommentCount(ctx context.Context, contactID uuid.UUID) (int64, error)
	
	// Company comments
	GetByCompanyID(ctx context.Context, companyID uuid.UUID) ([]*entities.Comment, error)
	GetCompanyCommentCount(ctx context.Context, companyID uuid.UUID) (int64, error)
	
	// User related
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.Comment, error)
	GetUserMentions(ctx context.Context, userID uuid.UUID) ([]*entities.Comment, error)
	
	// Threading
	GetReplies(ctx context.Context, parentID uuid.UUID) ([]*entities.Comment, error)
	GetThreadDepth(ctx context.Context, commentID uuid.UUID) (int, error)
	
	// Special operations
	PinComment(ctx context.Context, id uuid.UUID) error
	UnpinComment(ctx context.Context, id uuid.UUID) error
	AddReaction(ctx context.Context, id uuid.UUID, userID string, reaction string) error
	RemoveReaction(ctx context.Context, id uuid.UUID, userID string, reaction string) error
	
	// Edit history
	CreateEditHistory(ctx context.Context, edit *entities.CommentEdit) error
	GetEditHistory(ctx context.Context, commentID uuid.UUID) ([]*entities.CommentEdit, error)
	
	// Bulk operations
	CreateBatch(ctx context.Context, comments []*entities.Comment) error
	DeleteByEntity(ctx context.Context, entityType string, entityID uuid.UUID) error
}

// CommentFilter represents filter criteria for comments
type CommentFilter struct {
	EntityType   *string
	EntityID     *uuid.UUID
	CardID       *uuid.UUID
	ContactID    *uuid.UUID
	CompanyID    *uuid.UUID
	UserID       *uuid.UUID
	IsInternal   *bool
	IsPinned     *bool
	HasReplies   *bool
	ParentID     *uuid.UUID
	SearchTerm   string
}