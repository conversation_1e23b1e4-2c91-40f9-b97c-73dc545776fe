package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// PipelineRepository defines the interface for pipeline data access
type PipelineRepository interface {
	Create(ctx context.Context, pipeline *entities.Pipeline) error
	CreateWithStages(ctx context.Context, pipeline *entities.Pipeline) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Pipeline, error)
	GetByIDWithStages(ctx context.Context, id uuid.UUID) (*entities.Pipeline, error)
	Update(ctx context.Context, pipeline *entities.Pipeline) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]entities.Pipeline, int64, error)
	ListWithStages(ctx context.Context, limit, offset int) ([]entities.Pipeline, int64, error)
	GetDefault(ctx context.Context) (*entities.Pipeline, error)
	SetDefault(ctx context.Context, id uuid.UUID) error
}

// StageRepository defines the interface for stage data access
type StageRepository interface {
	Create(ctx context.Context, stage *entities.Stage) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Stage, error)
	Update(ctx context.Context, stage *entities.Stage) error
	Delete(ctx context.Context, id uuid.UUID) error
	ListByPipelineID(ctx context.Context, pipelineID uuid.UUID) ([]entities.Stage, error)
	ReorderStages(ctx context.Context, pipelineID uuid.UUID, stageOrders []struct {
		ID    uuid.UUID
		Order int
	}) error
	GetWithCards(ctx context.Context, id uuid.UUID) (*entities.Stage, error)
}