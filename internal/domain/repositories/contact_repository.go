package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// ContactFilters represents filtering options for contacts
type ContactFilters struct {
	CompanyID    *uuid.UUID
	Search       string
	Email        string
	CustomFields map[string]interface{}
}

// ContactRepository defines the interface for contact data access
type ContactRepository interface {
	Create(ctx context.Context, contact *entities.Contact) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Contact, error)
	GetByIDWithRelations(ctx context.Context, id uuid.UUID) (*entities.Contact, error)
	GetByEmail(ctx context.Context, email string) (*entities.Contact, error)
	Update(ctx context.Context, contact *entities.Contact) error
	UpdateCustomFields(ctx context.Context, id uuid.UUID, fields entities.CustomFields) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filters ContactFilters, limit, offset int) ([]entities.Contact, int64, error)
	ListByCompanyID(ctx context.Context, companyID uuid.UUID, limit, offset int) ([]entities.Contact, int64, error)
	Search(ctx context.Context, query string, limit, offset int) ([]entities.Contact, int64, error)
	SearchByCustomFields(ctx context.Context, searchQuery map[string]interface{}, limit, offset int) ([]entities.Contact, int64, error)
}