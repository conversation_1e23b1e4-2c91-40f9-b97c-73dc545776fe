package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

type PermissionRepository interface {
	Create(ctx context.Context, permission *entities.Permission) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Permission, error)
	GetByResourceAction(ctx context.Context, resource, action string) (*entities.Permission, error)
	List(ctx context.Context) ([]entities.Permission, error)
	ListByResource(ctx context.Context, resource string) ([]entities.Permission, error)
	Update(ctx context.Context, permission *entities.Permission) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Bulk operations
	CreateBulk(ctx context.Context, permissions []entities.Permission) error
	GetByIDs(ctx context.Context, ids []uuid.UUID) ([]entities.Permission, error)
	
	// Role management
	GetPermissionRoles(ctx context.Context, permissionID uuid.UUID) ([]entities.Role, error)
}