package repositories

import (
	"context"
	"time"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// EmailRepository defines the interface for email data access
type EmailRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, email *entities.Email) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Email, error)
	Update(ctx context.Context, email *entities.Email) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Query operations
	List(ctx context.Context, filter EmailFilter) ([]*entities.Email, error)
	ListWithPagination(ctx context.Context, filter EmailFilter, limit, offset int) ([]*entities.Email, int64, error)
	GetByMessageID(ctx context.Context, messageID string) (*entities.Email, error)
	GetByThreadID(ctx context.Context, threadID string) ([]*entities.Email, error)
	
	// Card related
	GetByCardID(ctx context.Context, cardID uuid.UUID) ([]*entities.Email, error)
	GetCardEmailCount(ctx context.Context, cardID uuid.UUID) (int64, error)
	
	// Contact related
	GetByContactID(ctx context.Context, contactID uuid.UUID) ([]*entities.Email, error)
	GetContactEmailCount(ctx context.Context, contactID uuid.UUID) (int64, error)
	
	// Company related
	GetByCompanyID(ctx context.Context, companyID uuid.UUID) ([]*entities.Email, error)
	
	// User related
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.Email, error)
	GetUserSentEmails(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) ([]*entities.Email, error)
	
	// Status updates
	UpdateStatus(ctx context.Context, id uuid.UUID, status entities.EmailStatus) error
	MarkAsOpened(ctx context.Context, id uuid.UUID) error
	MarkAsClicked(ctx context.Context, id uuid.UUID) error
	MarkAsBounced(ctx context.Context, id uuid.UUID) error
	
	// Bulk operations
	CreateBatch(ctx context.Context, emails []*entities.Email) error
	UpdateStatusBatch(ctx context.Context, ids []uuid.UUID, status entities.EmailStatus) error
	
	// Analytics
	GetEmailStats(ctx context.Context, filter EmailStatsFilter) (*EmailStats, error)
	GetUserEmailStats(ctx context.Context, userID uuid.UUID, period string) (*EmailStats, error)
}

// EmailTemplateRepository defines the interface for email template data access
type EmailTemplateRepository interface {
	Create(ctx context.Context, template *entities.EmailTemplate) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.EmailTemplate, error)
	GetByName(ctx context.Context, name string) (*entities.EmailTemplate, error)
	Update(ctx context.Context, template *entities.EmailTemplate) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filter EmailTemplateFilter) ([]*entities.EmailTemplate, error)
	ListActive(ctx context.Context) ([]*entities.EmailTemplate, error)
	IncrementUsageCount(ctx context.Context, id uuid.UUID) error
}

// EmailSequenceRepository defines the interface for email sequence data access
type EmailSequenceRepository interface {
	Create(ctx context.Context, sequence *entities.EmailSequence) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.EmailSequence, error)
	GetByName(ctx context.Context, name string) (*entities.EmailSequence, error)
	Update(ctx context.Context, sequence *entities.EmailSequence) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filter EmailSequenceFilter) ([]*entities.EmailSequence, error)
	ListActive(ctx context.Context) ([]*entities.EmailSequence, error)
	GetSequenceSteps(ctx context.Context, sequenceID uuid.UUID) ([]*entities.EmailSequenceStep, error)
	CreateStep(ctx context.Context, step *entities.EmailSequenceStep) error
	UpdateStep(ctx context.Context, step *entities.EmailSequenceStep) error
	DeleteStep(ctx context.Context, stepID uuid.UUID) error
}

// EmailFilter represents filter criteria for emails
type EmailFilter struct {
	Status      *entities.EmailStatus
	Type        *entities.EmailType
	CardID      *uuid.UUID
	ContactID   *uuid.UUID
	CompanyID   *uuid.UUID
	UserID      *uuid.UUID
	ThreadID    *string
	StartDate   *time.Time
	EndDate     *time.Time
	SearchTerm  string
	HasOpened   *bool
	HasClicked  *bool
	HasBounced  *bool
}

// EmailTemplateFilter represents filter criteria for email templates
type EmailTemplateFilter struct {
	Type       *entities.EmailType
	IsActive   *bool
	CreatedByID *uuid.UUID
	SearchTerm string
	Tags       []string
}

// EmailSequenceFilter represents filter criteria for email sequences
type EmailSequenceFilter struct {
	IsActive    *bool
	Trigger     *string
	CreatedByID *uuid.UUID
	SearchTerm  string
}

// EmailStatsFilter represents filter criteria for email statistics
type EmailStatsFilter struct {
	UserID     *uuid.UUID
	CardID     *uuid.UUID
	ContactID  *uuid.UUID
	CompanyID  *uuid.UUID
	StartDate  time.Time
	EndDate    time.Time
	GroupBy    string // day, week, month
}

// EmailStats represents email statistics
type EmailStats struct {
	TotalSent     int64                  `json:"total_sent"`
	TotalReceived int64                  `json:"total_received"`
	TotalOpened   int64                  `json:"total_opened"`
	TotalClicked  int64                  `json:"total_clicked"`
	TotalBounced  int64                  `json:"total_bounced"`
	TotalFailed   int64                  `json:"total_failed"`
	OpenRate      float64                `json:"open_rate"`
	ClickRate     float64                `json:"click_rate"`
	BounceRate    float64                `json:"bounce_rate"`
	ByDate        map[string]*EmailStats `json:"by_date,omitempty"`
	ByType        map[string]*EmailStats `json:"by_type,omitempty"`
}