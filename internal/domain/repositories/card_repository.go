package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// CardFilters represents filtering options for cards
type CardFilters struct {
	StageID      *uuid.UUID
	ContactID    *uuid.UUID
	CompanyID    *uuid.UUID
	AssignedToID *uuid.UUID
	Priority     *entities.CardPriority
	Search       string
	Tags         []uuid.UUID
	CustomFields map[string]interface{}
}

// CardRepository defines the interface for card data access
type CardRepository interface {
	Create(ctx context.Context, card *entities.Card) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Card, error)
	GetByIDWithRelations(ctx context.Context, id uuid.UUID) (*entities.Card, error)
	Update(ctx context.Context, card *entities.Card) error
	UpdateCustomFields(ctx context.Context, id uuid.UUID, fields entities.CustomFields) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filters CardFilt<PERSON>, limit, offset int) ([]entities.Card, int64, error)
	ListByStageID(ctx context.Context, stageID uuid.UUID, limit, offset int) ([]entities.Card, int64, error)
	ListByContactID(ctx context.Context, contactID uuid.UUID, limit, offset int) ([]entities.Card, int64, error)
	ListByCompanyID(ctx context.Context, companyID uuid.UUID, limit, offset int) ([]entities.Card, int64, error)
	MoveToStage(ctx context.Context, cardID, stageID uuid.UUID) error
	GetOverdueCards(ctx context.Context, limit, offset int) ([]entities.Card, int64, error)
	GetCardsByValue(ctx context.Context, minValue, maxValue float64, limit, offset int) ([]entities.Card, int64, error)
	SearchByCustomFields(ctx context.Context, searchQuery map[string]interface{}, limit, offset int) ([]entities.Card, int64, error)
}