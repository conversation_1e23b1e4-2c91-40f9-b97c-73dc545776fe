package repositories

import (
	"context"
	"time"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// WebhookRepository defines the interface for webhook data access
type WebhookRepository interface {
	Create(ctx context.Context, webhook *entities.Webhook) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Webhook, error)
	Update(ctx context.Context, webhook *entities.Webhook) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]entities.Webhook, int64, error)
	GetActiveWebhooks(ctx context.Context) ([]entities.Webhook, error)
	GetWebhooksForEvent(ctx context.Context, event entities.WebhookEvent) ([]entities.Webhook, error)
	SetActive(ctx context.Context, id uuid.UUID, active bool) error
}

// WebhookDeliveryRepository defines the interface for webhook delivery data access
type WebhookDeliveryRepository interface {
	Create(ctx context.Context, delivery *entities.WebhookDelivery) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.WebhookDelivery, error)
	Update(ctx context.Context, delivery *entities.WebhookDelivery) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, webhookID *uuid.UUID, limit, offset int) ([]entities.WebhookDelivery, int64, error)
	GetFailedDeliveries(ctx context.Context, limit, offset int) ([]entities.WebhookDelivery, int64, error)
	GetPendingRetries(ctx context.Context, beforeTime time.Time, limit int) ([]entities.WebhookDelivery, error)
	MarkAsDelivered(ctx context.Context, id uuid.UUID, statusCode int, response string) error
	MarkAsFailed(ctx context.Context, id uuid.UUID, statusCode int, response string) error
	CleanupOldDeliveries(ctx context.Context, olderThan time.Time) error
}