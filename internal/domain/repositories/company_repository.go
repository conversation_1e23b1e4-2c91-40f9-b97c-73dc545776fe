package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// CompanyFilters represents filtering options for companies
type CompanyFilters struct {
	Industry     string
	Size         string
	Country      string
	Search       string
	CustomFields map[string]interface{}
}

// CompanyRepository defines the interface for company data access
type CompanyRepository interface {
	Create(ctx context.Context, company *entities.Company) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Company, error)
	GetByIDWithRelations(ctx context.Context, id uuid.UUID) (*entities.Company, error)
	GetByName(ctx context.Context, name string) (*entities.Company, error)
	Update(ctx context.Context, company *entities.Company) error
	UpdateCustomFields(ctx context.Context, id uuid.UUID, fields entities.CustomFields) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filters CompanyFilters, limit, offset int) ([]entities.Company, int64, error)
	Search(ctx context.Context, query string, limit, offset int) ([]entities.Company, int64, error)
	SearchByCustomFields(ctx context.Context, searchQuery map[string]interface{}, limit, offset int) ([]entities.Company, int64, error)
	GetByIndustry(ctx context.Context, industry string, limit, offset int) ([]entities.Company, int64, error)
}