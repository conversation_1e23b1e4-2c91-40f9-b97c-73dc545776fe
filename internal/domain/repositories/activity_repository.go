package repositories

import (
	"context"
	"time"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// ActivityFilters represents filtering options for activities
type ActivityFilters struct {
	Type      *entities.ActivityType
	CardID    *uuid.UUID
	ContactID *uuid.UUID
	CompanyID *uuid.UUID
	UserID    *uuid.UUID
	Completed *bool
	DateFrom  *time.Time
	DateTo    *time.Time
}

// ActivityRepository defines the interface for activity data access
type ActivityRepository interface {
	Create(ctx context.Context, activity *entities.Activity) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Activity, error)
	Update(ctx context.Context, activity *entities.Activity) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filters ActivityFilters, limit, offset int) ([]entities.Activity, int64, error)
	ListByCardID(ctx context.Context, cardID uuid.UUID, limit, offset int) ([]entities.Activity, int64, error)
	ListByContactID(ctx context.Context, contactID uuid.UUID, limit, offset int) ([]entities.Activity, int64, error)
	ListByCompanyID(ctx context.Context, companyID uuid.UUID, limit, offset int) ([]entities.Activity, int64, error)
	ListByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]entities.Activity, int64, error)
	GetOverdueActivities(ctx context.Context, userID *uuid.UUID, limit, offset int) ([]entities.Activity, int64, error)
	GetUpcomingActivities(ctx context.Context, userID *uuid.UUID, days int, limit, offset int) ([]entities.Activity, int64, error)
	MarkAsCompleted(ctx context.Context, id uuid.UUID) error
	GetActivitiesByType(ctx context.Context, activityType entities.ActivityType, limit, offset int) ([]entities.Activity, int64, error)
}