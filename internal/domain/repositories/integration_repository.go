package repositories

import (
	"context"
	"time"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// IntegrationRepository defines the interface for integration data access
type IntegrationRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, integration *entities.Integration) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Integration, error)
	GetByName(ctx context.Context, name string) (*entities.Integration, error)
	Update(ctx context.Context, integration *entities.Integration) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Query operations
	List(ctx context.Context, filter IntegrationFilter) ([]*entities.Integration, error)
	ListEnabled(ctx context.Context) ([]*entities.Integration, error)
	ListByType(ctx context.Context, integrationType entities.IntegrationType) ([]*entities.Integration, error)
	GetDefault(ctx context.Context, integrationType entities.IntegrationType) (*entities.Integration, error)
	
	// Configuration operations
	UpdateConfig(ctx context.Context, id uuid.UUID, config map[string]interface{}) error
	TestConnection(ctx context.Context, id uuid.UUID) error
	Enable(ctx context.Context, id uuid.UUID) error
	Disable(ctx context.Context, id uuid.UUID) error
	SetAsDefault(ctx context.Context, id uuid.UUID) error
	
	// Usage tracking
	IncrementUsageCount(ctx context.Context, id uuid.UUID) error
	UpdateLastUsed(ctx context.Context, id uuid.UUID) error
	
	// Permission management
	GetUserIntegrations(ctx context.Context, userID uuid.UUID) ([]*entities.Integration, error)
	CheckUserAccess(ctx context.Context, integrationID, userID uuid.UUID) (bool, error)
	UpdateAllowedRoles(ctx context.Context, id uuid.UUID, roles []string) error
	UpdateAllowedUsers(ctx context.Context, id uuid.UUID, users []uuid.UUID) error
	
	// Connection status
	UpdateConnectionStatus(ctx context.Context, id uuid.UUID, connected bool, error string) error
	UpdateTestResult(ctx context.Context, id uuid.UUID, success bool, error string) error
}

// IntegrationLogRepository defines the interface for integration log data access
type IntegrationLogRepository interface {
	Create(ctx context.Context, log *entities.IntegrationLog) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.IntegrationLog, error)
	List(ctx context.Context, filter IntegrationLogFilter) ([]*entities.IntegrationLog, error)
	ListWithPagination(ctx context.Context, filter IntegrationLogFilter, limit, offset int) ([]*entities.IntegrationLog, int64, error)
	DeleteOld(ctx context.Context, beforeDate time.Time) (int64, error)
	GetStats(ctx context.Context, integrationID uuid.UUID, period string) (*IntegrationStats, error)
}

// IntegrationFilter represents filter criteria for integrations
type IntegrationFilter struct {
	Type         *entities.IntegrationType
	Provider     *entities.IntegrationProvider
	IsEnabled    *bool
	IsConnected  *bool
	IsDefault    *bool
	CreatedByID  *uuid.UUID
	SearchTerm   string
}

// IntegrationLogFilter represents filter criteria for integration logs
type IntegrationLogFilter struct {
	IntegrationID *uuid.UUID
	Action        *string
	Status        *string
	EntityType    *string
	EntityID      *uuid.UUID
	UserID        *uuid.UUID
	StartDate     *time.Time
	EndDate       *time.Time
	HasError      *bool
}

// IntegrationStats represents statistics for an integration
type IntegrationStats struct {
	TotalRequests   int64                    `json:"total_requests"`
	SuccessCount    int64                    `json:"success_count"`
	FailureCount    int64                    `json:"failure_count"`
	PendingCount    int64                    `json:"pending_count"`
	SuccessRate     float64                  `json:"success_rate"`
	AverageDuration int                      `json:"average_duration"`
	ByAction        map[string]*ActionStats  `json:"by_action"`
	ByDay           map[string]*DayStats     `json:"by_day"`
	TopErrors       []ErrorStats             `json:"top_errors"`
}

// ActionStats represents statistics for a specific action
type ActionStats struct {
	Count           int64   `json:"count"`
	SuccessCount    int64   `json:"success_count"`
	FailureCount    int64   `json:"failure_count"`
	AverageDuration int     `json:"average_duration"`
}

// DayStats represents daily statistics
type DayStats struct {
	Date         string  `json:"date"`
	Count        int64   `json:"count"`
	SuccessCount int64   `json:"success_count"`
	FailureCount int64   `json:"failure_count"`
}

// ErrorStats represents error statistics
type ErrorStats struct {
	Error string `json:"error"`
	Count int64  `json:"count"`
}