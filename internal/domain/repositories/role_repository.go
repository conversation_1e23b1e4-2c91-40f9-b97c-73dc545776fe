package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

type RoleRepository interface {
	Create(ctx context.Context, role *entities.Role) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Role, error)
	GetByName(ctx context.Context, name string) (*entities.Role, error)
	List(ctx context.Context, limit, offset int) ([]entities.Role, int64, error)
	Update(ctx context.Context, role *entities.Role) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Permission management
	GetRolePermissions(ctx context.Context, roleID uuid.UUID) ([]entities.Permission, error)
	AssignPermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error
	RemovePermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error
	ReplacePermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error
	
	// User management
	GetRoleUsers(ctx context.Context, roleID uuid.UUID) ([]entities.User, error)
	AssignUserToRole(ctx context.Context, userID, roleID uuid.UUID) error
	RemoveUserFromRole(ctx context.Context, userID uuid.UUID) error
	CountUsersInRole(ctx context.Context, roleID uuid.UUID) (int64, error)
}