package repositories

import (
	"context"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// FieldDefinitionRepository defines the interface for field definition data access
type FieldDefinitionRepository interface {
	Create(ctx context.Context, fieldDef *entities.FieldDefinition) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.FieldDefinition, error)
	Update(ctx context.Context, fieldDef *entities.FieldDefinition) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]entities.FieldDefinition, int64, error)
	ListByEntityType(ctx context.Context, entityType string, limit, offset int) ([]entities.FieldDefinition, int64, error)
	GetByName(ctx context.Context, name string, entityType string) (*entities.FieldDefinition, error)
	ReorderFields(ctx context.Context, entityType string, fieldOrders []struct {
		ID    uuid.UUID
		Order int
	}) error
}