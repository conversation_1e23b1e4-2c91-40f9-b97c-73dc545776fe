package repositories

import (
	"context"
	"time"

	"crm-backend/internal/domain/entities"
	"github.com/google/uuid"
)

// NotificationRepository defines the interface for notification data access
type NotificationRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, notification *entities.Notification) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Notification, error)
	Update(ctx context.Context, notification *entities.Notification) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Query operations
	List(ctx context.Context, filter NotificationFilter) ([]*entities.Notification, error)
	ListWithPagination(ctx context.Context, filter NotificationFilter, limit, offset int) ([]*entities.Notification, int64, error)
	
	// User notifications
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.Notification, error)
	GetUnreadByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.Notification, error)
	GetUnreadCount(ctx context.Context, userID uuid.UUID) (int64, error)
	GetRecentByUserID(ctx context.Context, userID uuid.UUID, limit int) ([]*entities.Notification, error)
	
	// Status operations
	MarkAsRead(ctx context.Context, id uuid.UUID) error
	MarkAsUnread(ctx context.Context, id uuid.UUID) error
	MarkAllAsRead(ctx context.Context, userID uuid.UUID) error
	MarkAsSent(ctx context.Context, id uuid.UUID) error
	
	// Bulk operations
	CreateBatch(ctx context.Context, notifications []*entities.Notification) error
	MarkMultipleAsRead(ctx context.Context, ids []uuid.UUID) error
	DeleteOld(ctx context.Context, beforeDate time.Time) (int64, error)
	DeleteExpired(ctx context.Context) (int64, error)
	
	// Scheduled notifications
	GetScheduledNotifications(ctx context.Context, beforeTime time.Time) ([]*entities.Notification, error)
	GetPendingNotifications(ctx context.Context) ([]*entities.Notification, error)
	
	// Group operations
	GetByGroupKey(ctx context.Context, groupKey string) ([]*entities.Notification, error)
	IncrementGroupCount(ctx context.Context, groupKey string) error
	
	// Delivery status
	UpdateDeliveryStatus(ctx context.Context, id uuid.UUID, channel entities.NotificationChannel, status entities.NotificationDeliveryStatus) error
	GetFailedNotifications(ctx context.Context, userID uuid.UUID) ([]*entities.Notification, error)
	
	// Analytics
	GetNotificationStats(ctx context.Context, userID uuid.UUID, period string) (*NotificationStats, error)
}

// NotificationPreferenceRepository defines the interface for notification preferences
type NotificationPreferenceRepository interface {
	Create(ctx context.Context, preference *entities.NotificationPreference) error
	GetByUserID(ctx context.Context, userID uuid.UUID) (*entities.NotificationPreference, error)
	Update(ctx context.Context, preference *entities.NotificationPreference) error
	Delete(ctx context.Context, userID uuid.UUID) error
	GetOrCreateDefault(ctx context.Context, userID uuid.UUID) (*entities.NotificationPreference, error)
}

// NotificationFilter represents filter criteria for notifications
type NotificationFilter struct {
	UserID       *uuid.UUID
	Type         *entities.NotificationType
	Priority     *entities.NotificationPriority
	IsRead       *bool
	IsSent       *bool
	Channels     []entities.NotificationChannel
	EntityType   *string
	EntityID     *uuid.UUID
	SenderID     *uuid.UUID
	StartDate    *time.Time
	EndDate      *time.Time
	SearchTerm   string
	GroupKey     *string
}

// NotificationStats represents notification statistics
type NotificationStats struct {
	TotalCount       int64                                     `json:"total_count"`
	UnreadCount      int64                                     `json:"unread_count"`
	ReadCount        int64                                     `json:"read_count"`
	SentCount        int64                                     `json:"sent_count"`
	FailedCount      int64                                     `json:"failed_count"`
	ByType           map[entities.NotificationType]int64      `json:"by_type"`
	ByPriority       map[entities.NotificationPriority]int64  `json:"by_priority"`
	ByChannel        map[entities.NotificationChannel]int64   `json:"by_channel"`
	ReadRate         float64                                   `json:"read_rate"`
	DeliveryRate     float64                                   `json:"delivery_rate"`
	AverageReadTime  time.Duration                            `json:"average_read_time"`
}