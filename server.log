2025/08/27 17:15:26 Successfully connected to database
2025/08/27 17:15:26 Starting database migration...

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:57
[0m[33m[0.306ms] [34;1m[rows:0][0m CREATE EXTENSION IF NOT EXISTS "uuid-ossp"

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[7.799ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.284ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[12.847ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'users'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.725ms] [34;1m[rows:-][0m SELECT * FROM "users" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[9.552ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'users' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[10.013ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'users'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.941ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'users'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.848ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.463ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.163ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.008ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m
[0m[33m[0.176ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m
[0m[33m[5.013ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'users'

2025/08/27 17:15:26 [32m
[0m[33m[0.268ms] [34;1m[rows:-][0m SELECT * FROM "users" LIMIT 1

2025/08/27 17:15:26 [32m
[0m[33m[7.684ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'users' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m
[0m[33m[7.668ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'users'

2025/08/27 17:15:26 [32m
[0m[33m[0.632ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'users'

2025/08/27 17:15:26 [32m
[0m[33m[2.802ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'users' AND indexname = 'idx_users_email' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.354ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'email') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.188ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'password') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.447ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'first_name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.365ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'last_name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.339ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'role') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.331ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'users' AND column_name = 'is_active') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'users' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.848ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'users' AND indexname = 'idx_users_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.795ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'users' AND indexname = 'idx_users_email' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.788ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.121ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[4.935ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.583ms] [34;1m[rows:-][0m SELECT * FROM "field_definitions" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[7.533ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'field_definitions' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[8.607ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'field_definitions'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.574ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'field_definitions'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.825ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.492ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.442ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.449ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.812ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.443ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'label') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.341ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'type') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.416ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'required') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.327ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'options') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.310ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'entity_type') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.309ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'order') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.319ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'field_definitions' AND column_name = 'description') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'field_definitions' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.906ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'field_definitions' AND indexname = 'idx_field_definitions_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.653ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.112ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[4.670ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.492ms] [34;1m[rows:-][0m SELECT * FROM "pipelines" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[7.097ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'pipelines' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[7.335ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'pipelines'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.689ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'pipelines'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.552ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.493ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.449ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.458ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.545ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.572ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'description') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.340ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'color') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.311ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'icon') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.317ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'is_default') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.330ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'is_active') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.405ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'pipelines' AND column_name = 'sort_order') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'pipelines' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.895ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'pipelines' AND indexname = 'idx_pipelines_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.670ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.129ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[5.586ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'stages'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.570ms] [34;1m[rows:-][0m SELECT * FROM "stages" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[7.000ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'stages' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[7.022ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'stages'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.564ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'stages'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.485ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.359ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.341ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.342ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.323ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'pipeline_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.327ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.329ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'description') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.333ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'color') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.327ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'is_closed_won') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.324ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'is_closed_lost') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m
[0m[33m[0.106ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m
[0m[33m[5.179ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'stages'

2025/08/27 17:15:26 [32m
[0m[33m[0.263ms] [34;1m[rows:-][0m SELECT * FROM "stages" LIMIT 1

2025/08/27 17:15:26 [32m
[0m[33m[8.292ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'stages' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m
[0m[33m[7.591ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'stages'

2025/08/27 17:15:26 [32m
[0m[33m[0.631ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'stages'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[4.531ms] [34;1m[rows:0][0m ALTER TABLE "stages" ALTER COLUMN "probability" SET DEFAULT 0

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.474ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'probability') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.917ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'sort_order') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.549ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'auto_move_after_days') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.531ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND column_name = 'is_active') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'stages' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.255ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'stages' AND constraint_name = 'fk_pipelines_stages'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.853ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'stages' AND indexname = 'idx_stages_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.262ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'stages' AND indexname = 'idx_stages_pipeline_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.190ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.123ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.522ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'companies'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.694ms] [34;1m[rows:-][0m SELECT * FROM "companies" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.548ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'companies' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.477ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'companies'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.174ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'companies'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.373ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.348ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.333ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.376ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.455ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.427ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'website') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.435ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'industry') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.387ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'size') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.307ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'address') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.292ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'city') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.309ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'state') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.311ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'country') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.308ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'postal_code') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.301ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'phone') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.315ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'email') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m
[0m[33m[0.096ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m
[0m[33m[1.616ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'companies'

2025/08/27 17:15:26 [32m
[0m[33m[0.178ms] [34;1m[rows:-][0m SELECT * FROM "companies" LIMIT 1

2025/08/27 17:15:26 [32m
[0m[33m[1.363ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'companies' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m
[0m[33m[1.419ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'companies'

2025/08/27 17:15:26 [32m
[0m[33m[0.182ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'companies'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.602ms] [34;1m[rows:0][0m ALTER TABLE "companies" ALTER COLUMN "custom_fields" SET DEFAULT '{}'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.755ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'companies' AND column_name = 'custom_fields') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'companies' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.500ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'companies' AND indexname = 'idx_companies_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.369ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'companies' AND indexname = 'idx_companies_name' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.058ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.080ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.288ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'contacts'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.751ms] [34;1m[rows:-][0m SELECT * FROM "contacts" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.474ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'contacts' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.176ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'contacts'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.187ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'contacts'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.448ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.740ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.437ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.408ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.385ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'first_name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.376ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'last_name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.376ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'email') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.361ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'phone') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.329ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'job_title') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.322ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'company_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m
[0m[33m[0.106ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m
[0m[33m[2.172ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'contacts'

2025/08/27 17:15:26 [32m
[0m[33m[0.203ms] [34;1m[rows:-][0m SELECT * FROM "contacts" LIMIT 1

2025/08/27 17:15:26 [32m
[0m[33m[1.174ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'contacts' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m
[0m[33m[1.084ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'contacts'

2025/08/27 17:15:26 [32m
[0m[33m[0.186ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'contacts'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[3.273ms] [34;1m[rows:0][0m ALTER TABLE "contacts" ALTER COLUMN "custom_fields" SET DEFAULT '{}'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.680ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND column_name = 'custom_fields') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'contacts' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.920ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'contacts' AND constraint_name = 'fk_companies_contacts'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.211ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'contacts' AND indexname = 'idx_contacts_company_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.216ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'contacts' AND indexname = 'idx_contacts_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.189ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'contacts' AND indexname = 'idx_contacts_email' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.299ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.131ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.431ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'cards'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.280ms] [34;1m[rows:-][0m SELECT * FROM "cards" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.474ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'cards' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.855ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'cards'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.302ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'cards'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.688ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.455ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.439ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.367ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.348ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'title') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.353ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'description') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.352ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'value') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.364ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'priority') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.382ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'stage_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.341ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'contact_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.404ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'company_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.326ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'assigned_to_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.322ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'expected_close_date') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.338ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'actual_close_date') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m
[0m[33m[0.098ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m
[0m[33m[1.293ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'cards'

2025/08/27 17:15:26 [32m
[0m[33m[0.171ms] [34;1m[rows:-][0m SELECT * FROM "cards" LIMIT 1

2025/08/27 17:15:26 [32m
[0m[33m[2.215ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'cards' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m
[0m[33m[1.814ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'cards'

2025/08/27 17:15:26 [32m
[0m[33m[0.292ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'cards'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[3.355ms] [34;1m[rows:0][0m ALTER TABLE "cards" ALTER COLUMN "custom_fields" SET DEFAULT '{}'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.848ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND column_name = 'custom_fields') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'cards' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.771ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND constraint_name = 'fk_contacts_cards'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.437ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND constraint_name = 'fk_cards_assigned_to'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.347ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND constraint_name = 'fk_stages_cards'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.320ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'cards' AND constraint_name = 'fk_companies_cards'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.191ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'cards' AND indexname = 'idx_cards_stage_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.188ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'cards' AND indexname = 'idx_cards_contact_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.171ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'cards' AND indexname = 'idx_cards_company_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.177ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'cards' AND indexname = 'idx_cards_assigned_to_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.169ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'cards' AND indexname = 'idx_cards_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.181ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'tags' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.095ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.104ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'tags'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.463ms] [34;1m[rows:-][0m SELECT * FROM "tags" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.795ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'tags' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.816ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'tags'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.153ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'tags'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.331ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'tags' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'tags' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.345ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'tags' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'tags' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.332ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'tags' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'tags' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.343ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'tags' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'tags' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m
[0m[33m[0.096ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m
[0m[33m[1.005ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'tags'

2025/08/27 17:15:26 [32m
[0m[33m[0.160ms] [34;1m[rows:-][0m SELECT * FROM "tags" LIMIT 1

2025/08/27 17:15:26 [32m
[0m[33m[1.495ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'tags' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m
[0m[33m[1.616ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'tags'

2025/08/27 17:15:26 [32m
[0m[33m[0.254ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'tags'

2025/08/27 17:15:26 [32m
[0m[33m[0.335ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'tags' AND indexname = 'idx_tags_name' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.469ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'tags' AND column_name = 'name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'tags' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.330ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'tags' AND column_name = 'color') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'tags' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.180ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'tags' AND indexname = 'idx_tags_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.178ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'tags' AND indexname = 'idx_tags_name' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.212ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'card_tags' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.097ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.000ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'card_tags'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.376ms] [34;1m[rows:-][0m SELECT * FROM "card_tags" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.767ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'card_tags' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.658ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'card_tags'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.137ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'card_tags'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.412ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'card_tags' AND column_name = 'tag_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'card_tags' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.412ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'card_tags' AND column_name = 'card_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'card_tags' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.297ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'card_tags' AND constraint_name = 'fk_card_tags_tag'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.229ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'card_tags' AND constraint_name = 'fk_card_tags_card'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.174ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.082ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.443ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'activities'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.948ms] [34;1m[rows:-][0m SELECT * FROM "activities" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.098ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'activities' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.012ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'activities'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.161ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'activities'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.335ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.343ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.336ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.328ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.383ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'type') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.360ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'title') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.351ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'description') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.308ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'due_date') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.324ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'completed_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.317ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'card_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.340ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'contact_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.379ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'company_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.334ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'user_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m
[0m[33m[0.108ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m
[0m[33m[1.248ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'activities'

2025/08/27 17:15:26 [32m
[0m[33m[0.196ms] [34;1m[rows:-][0m SELECT * FROM "activities" LIMIT 1

2025/08/27 17:15:26 [32m
[0m[33m[1.539ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'activities' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m
[0m[33m[1.465ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'activities'

2025/08/27 17:15:26 [32m
[0m[33m[0.208ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'activities'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.662ms] [34;1m[rows:0][0m ALTER TABLE "activities" ALTER COLUMN "metadata" SET DEFAULT '{}'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.420ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND column_name = 'metadata') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'activities' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.421ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND constraint_name = 'fk_activities_user'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.426ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND constraint_name = 'fk_contacts_activities'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.250ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND constraint_name = 'fk_cards_activities'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.209ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'activities' AND constraint_name = 'fk_activities_company'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.169ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'activities' AND indexname = 'idx_activities_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.155ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'activities' AND indexname = 'idx_activities_card_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.157ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'activities' AND indexname = 'idx_activities_contact_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.149ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'activities' AND indexname = 'idx_activities_company_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.148ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'activities' AND indexname = 'idx_activities_user_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.144ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.073ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.387ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'attachments'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.640ms] [34;1m[rows:-][0m SELECT * FROM "attachments" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.874ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'attachments' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.906ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'attachments'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.148ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'attachments'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.335ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.308ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.304ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.346ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.363ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'file_name') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.309ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'file_path') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.302ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'file_size') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.301ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'mime_type') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.294ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'card_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.300ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'contact_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.326ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'company_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.324ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND column_name = 'uploaded_by_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'attachments' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.294ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND constraint_name = 'fk_attachments_contact'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.256ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND constraint_name = 'fk_attachments_company'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.246ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND constraint_name = 'fk_attachments_uploaded_by'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.233ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'attachments' AND constraint_name = 'fk_cards_attachments'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.196ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'attachments' AND indexname = 'idx_attachments_uploaded_by_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.183ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'attachments' AND indexname = 'idx_attachments_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.185ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'attachments' AND indexname = 'idx_attachments_card_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.191ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'attachments' AND indexname = 'idx_attachments_contact_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.470ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'attachments' AND indexname = 'idx_attachments_company_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.162ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.072ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.312ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.475ms] [34;1m[rows:-][0m SELECT * FROM "webhooks" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.979ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'webhooks' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.138ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'webhooks'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.173ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'webhooks'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.384ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.486ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.361ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.351ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.326ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'url') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.338ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'events') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.311ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'secret') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.304ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'is_active') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.297ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'description') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m
[0m[33m[0.079ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m
[0m[33m[1.107ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks'

2025/08/27 17:15:26 [32m
[0m[33m[0.168ms] [34;1m[rows:-][0m SELECT * FROM "webhooks" LIMIT 1

2025/08/27 17:15:26 [32m
[0m[33m[1.106ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'webhooks' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m
[0m[33m[0.967ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'webhooks'

2025/08/27 17:15:26 [32m
[0m[33m[0.184ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'webhooks'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.567ms] [34;1m[rows:0][0m ALTER TABLE "webhooks" ALTER COLUMN "headers" SET DEFAULT '{}'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.477ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhooks' AND column_name = 'headers') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhooks' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.377ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'webhooks' AND indexname = 'idx_webhooks_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.466ms] [34;1m[rows:1][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND table_type = 'BASE TABLE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.225ms] [34;1m[rows:1][0m SELECT CURRENT_DATABASE()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[2.007ms] [34;1m[rows:-][0m SELECT c.column_name, c.is_nullable = 'YES', c.udt_name, c.character_maximum_length, c.numeric_precision, c.numeric_precision_radix, c.numeric_scale, c.datetime_precision, 8 * typlen, c.column_default, pd.description, c.identity_increment FROM information_schema.columns AS c JOIN pg_type AS pgt ON c.udt_name = pgt.typname LEFT JOIN pg_catalog.pg_description as pd ON pd.objsubid = c.ordinal_position AND pd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = c.table_schema)) where table_catalog = 'crm' AND table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.674ms] [34;1m[rows:-][0m SELECT * FROM "webhook_deliveries" LIMIT 1

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.034ms] [34;1m[rows:-][0m SELECT constraint_name FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'webhook_deliveries' AND constraint_type = 'UNIQUE'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[1.441ms] [34;1m[rows:-][0m SELECT c.column_name, constraint_name, constraint_type FROM information_schema.table_constraints tc JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_catalog, table_name, constraint_name) JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema AND tc.table_name = c.table_name AND ccu.column_name = c.column_name WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE') AND c.table_catalog = 'crm' AND c.table_schema = CURRENT_SCHEMA() AND c.table_name = 'webhook_deliveries'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.208ms] [34;1m[rows:-][0m SELECT a.attname as column_name, format_type(a.atttypid, a.atttypmod) AS data_type
		FROM pg_attribute a JOIN pg_class b ON a.attrelid = b.oid AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA())
		WHERE a.attnum > 0 -- hide internal columns
		AND NOT a.attisdropped -- hide deleted columns
		AND b.relname = 'webhook_deliveries'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.484ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.468ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'created_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.447ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'updated_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.403ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'deleted_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.475ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'webhook_id') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.349ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'event') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.336ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'payload') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.334ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'status_code') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.338ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'response') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.291ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'success') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.285ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'attempt_count') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.318ms] [34;1m[rows:0][0m SELECT description FROM pg_catalog.pg_description WHERE objsubid = (SELECT ordinal_position FROM information_schema.columns WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND column_name = 'next_retry_at') AND objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = 'webhook_deliveries' AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = CURRENT_SCHEMA()))

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.328ms] [34;1m[rows:1][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE table_schema = CURRENT_SCHEMA() AND table_name = 'webhook_deliveries' AND constraint_name = 'fk_webhook_deliveries_webhook'

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.230ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'webhook_deliveries' AND indexname = 'idx_webhook_deliveries_deleted_at' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:62
[0m[33m[0.215ms] [34;1m[rows:1][0m SELECT count(*) FROM pg_indexes WHERE tablename = 'webhook_deliveries' AND indexname = 'idx_webhook_deliveries_webhook_id' AND schemaname = CURRENT_SCHEMA()

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:102
[0m[33m[0.293ms] [34;1m[rows:0][0m CREATE INDEX IF NOT EXISTS idx_cards_custom_fields_gin ON cards USING GIN (custom_fields)

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:102
[0m[33m[0.214ms] [34;1m[rows:0][0m CREATE INDEX IF NOT EXISTS idx_contacts_custom_fields_gin ON contacts USING GIN (custom_fields)

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:102
[0m[33m[0.218ms] [34;1m[rows:0][0m CREATE INDEX IF NOT EXISTS idx_companies_custom_fields_gin ON companies USING GIN (custom_fields)

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:102
[0m[33m[0.365ms] [34;1m[rows:0][0m CREATE INDEX IF NOT EXISTS idx_activities_metadata_gin ON activities USING GIN (metadata)

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:102
[0m[33m[0.156ms] [34;1m[rows:0][0m CREATE INDEX IF NOT EXISTS idx_field_definitions_options_gin ON field_definitions USING GIN (options)

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:102
[0m[33m[0.129ms] [34;1m[rows:0][0m CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_payload_gin ON webhook_deliveries USING GIN (payload)
2025/08/27 17:15:26 Database migration completed successfully
2025/08/27 17:15:26 Starting data seeding...

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:116
[0m[33m[0.239ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE "pipelines"."deleted_at" IS NULL

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:147
[0m[33m[0.265ms] [34;1m[rows:1][0m SELECT count(*) FROM "users" WHERE role = 'admin' AND "users"."deleted_at" IS NULL

2025/08/27 17:15:26 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/connection.go:166
[0m[33m[0.226ms] [34;1m[rows:1][0m SELECT count(*) FROM "field_definitions" WHERE "field_definitions"."deleted_at" IS NULL
2025/08/27 17:15:26 Data seeding completed successfully
2025/08/27 17:15:26 ✅ Background workers started
2025/08/27 17:15:26 🚀 Server starting on 0.0.0.0:8080

 ┌───────────────────────────────────────────────────┐ 
 │                   Fiber v2.52.0                   │ 
 │               http://127.0.0.1:8080               │ 
 │       (bound on host 0.0.0.0 and port 8080)       │ 
 │                                                   │ 
 │ Handlers ............ 70  Processes ........... 1 │ 
 │ Prefork ....... Disabled  PID ........... 1238367 │ 
 └───────────────────────────────────────────────────┘ 


2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[2.053ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:15:41] 200 -   56.852095ms POST /api/v1/auth/login -

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[1.035ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:163
[0m[33m[0.366ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:168
[0m[33m[0.280ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE cards.deleted_at IS NULL AND "cards"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:176
[0m[33m[0.654ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:184
[0m[33m[0.387ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_lost = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:193
[0m[33m[0.510ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.expected_close_date < '2025-08-27 17:15:41.782' AND cards.deleted_at IS NULL) AND (stages.is_closed_won = false AND stages.is_closed_lost = false) AND "cards"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:203
[0m[33m[0.584ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value), 0) as total FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:216
[0m[33m[0.503ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value * probability / 100.0), 0) as expected FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = false AND stages.is_closed_lost = false AND cards.deleted_at IS NULL) AND "cards"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:238
[0m[33m[0.242ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:243
[0m[33m[0.235ms] [34;1m[rows:1][0m SELECT count(*) FROM "stages" WHERE is_active = true AND "stages"."deleted_at" IS NULL

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:264
[0m[33m[0.960ms] [34;1m[rows:0][0m SELECT stages.id as stage_id, stages.name as stage_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.id, stages.name ORDER BY stages.sort_order ASC

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:296
[0m[33m[0.714ms] [34;1m[rows:0][0m SELECT pipelines.id as pipeline_id, pipelines.name as pipeline_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id JOIN pipelines ON stages.pipeline_id = pipelines.id WHERE (cards.deleted_at IS NULL AND pipelines.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY pipelines.id, pipelines.name ORDER BY pipelines.sort_order ASC

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:326
[0m[33m[0.610ms] [34;1m[rows:0][0m SELECT type, COUNT(*) as count, MAX(created_at) as last_updated FROM "activities" WHERE created_at >= '2025-07-28 17:15:41.786' AND "activities"."deleted_at" IS NULL GROUP BY "type" ORDER BY count DESC LIMIT 10

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:367
[0m[33m[1.073ms] [34;1m[rows:0][0m SELECT 
			users.id as user_id,
			users.first_name,
			users.last_name,
			COUNT(cards.id) as cards_count,
			COALESCE(SUM(CASE WHEN stages.is_closed_won THEN cards.value ELSE 0 END), 0) as total_revenue,
			COUNT(CASE WHEN stages.is_closed_won THEN 1 END) as closed_won
		 FROM "cards" LEFT JOIN users ON cards.assigned_to_id = users.id JOIN stages ON cards.stage_id = stages.id WHERE (cards.created_at >= '2025-05-27 17:15:41.787' AND users.id IS NOT NULL) AND "cards"."deleted_at" IS NULL GROUP BY users.id, users.first_name, users.last_name ORDER BY total_revenue DESC LIMIT 5

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:408
[0m[33m[0.571ms] [34;1m[rows:0][0m SELECT 
			TO_CHAR(cards.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(cards.value), 0) as revenue,
			COUNT(*) as count
		 FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = true AND cards.actual_close_date >= '2024-08-27 17:15:41.788') AND "cards"."deleted_at" IS NULL GROUP BY TO_CHAR(cards.actual_close_date, 'YYYY-MM') ORDER BY month DESC LIMIT 12

2025/08/27 17:15:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:437
[0m[33m[0.458ms] [34;1m[rows:0][0m SELECT stages.name as stage_name, COUNT(cards.id) as card_count, stages.sort_order FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.name, stages.sort_order ORDER BY stages.sort_order ASC
[2025-08-27 10:15:41] 200 -    9.831925ms GET /api/v1/dashboard/stats -

2025/08/27 17:16:09 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.819ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:16:08] 200 -    53.67019ms POST /api/v1/auth/login -

2025/08/27 17:16:09 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.518ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
2025/08/27 17:16:09 SSE client registered: 98dcc557-32dd-4dab-8cd0-43159a39c1e5 (user: 9f61e195-b989-42cd-a052-658e9b622820)
2025/08/27 17:16:09 SSE client unregistered: 98dcc557-32dd-4dab-8cd0-43159a39c1e5
[2025-08-27 10:16:08] 200 -     715.429µs GET /api/v1/realtime/events -

2025/08/27 17:30:17 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.748ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:30:17] 200 -   58.472611ms POST /api/v1/auth/login -

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.650ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:31:11] 200 -    60.36958ms POST /api/v1/auth/login -

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.480ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.297ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.735ms] [34;1m[rows:0][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.599ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[2.256ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:31:11] 200 -    3.717882ms GET /api/v1/pipelines -

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.432ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:25
[0m[33m[5.304ms] [34;1m[rows:1][0m INSERT INTO "cards" ("created_at","updated_at","deleted_at","title","description","value","priority","stage_id","contact_id","company_id","assigned_to_id","expected_close_date","actual_close_date","id","custom_fields") VALUES ('2025-08-27 10:31:11.584','2025-08-27 10:31:11.584',NULL,'Test Card with Custom Fields','Testing JSONB custom fields functionality',150000,'high','04492c32-4348-4db5-b6a9-aedefdfb73b9',NULL,NULL,NULL,NULL,NULL,'74a58135-517e-4d27-80b0-7d49a07a5b34','{"additional_notes":"This is a test of custom fields","budget":"$50K - $100K","contact_date":"2025-08-27","interested_in":["Product A","Product B"],"source":"Website"}') RETURNING "id","custom_fields"

2025/08/27 17:31:11 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/activity_repository.go:25
[0m[33m[4.443ms] [34;1m[rows:1][0m INSERT INTO "activities" ("created_at","updated_at","deleted_at","type","title","description","due_date","completed_at","card_id","contact_id","company_id","user_id","id") VALUES ('2025-08-27 10:31:11.589','2025-08-27 10:31:11.589',NULL,'created','Card created','Card \'Test Card with Custom Fields\' was created',NULL,NULL,'74a58135-517e-4d27-80b0-7d49a07a5b34',NULL,NULL,'9f61e195-b989-42cd-a052-658e9b622820','dae8ff7e-d41d-4bcd-956d-afdaa1766a45') RETURNING "id","metadata"
[2025-08-27 10:31:11] 201 -   10.682588ms POST /api/v1/cards -

2025/08/27 17:31:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.688ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:31:24] 200 -   59.909746ms POST /api/v1/auth/login -

2025/08/27 17:31:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.791ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:31:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.203ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:31:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.539ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:31:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.315ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:31:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[2.287ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:31:24] 200 -    3.576098ms GET /api/v1/cards -

2025/08/27 17:31:51 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.700ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:31:51] 200 -   54.842496ms POST /api/v1/auth/login -

2025/08/27 17:31:51 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.487ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
2025/08/27 17:31:51 SSE client registered: e02232cf-f583-403f-b7b7-be886693507c (user: 9f61e195-b989-42cd-a052-658e9b622820)
2025/08/27 17:31:51 SSE client unregistered: e02232cf-f583-403f-b7b7-be886693507c
[2025-08-27 10:31:51] 200 -     698.516µs GET /api/v1/realtime/events -

2025/08/27 17:33:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.665ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:33:41] 200 -   53.803002ms POST /api/v1/auth/login -

2025/08/27 17:33:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.488ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:33:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:31
[0m[33m[0.750ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE id = '74a58135-517e-4d27-80b0-7d49a07a5b34' AND "cards"."deleted_at" IS NULL ORDER BY "cards"."id" LIMIT 1

2025/08/27 17:33:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:62
[0m[33m[54.305ms] [34;1m[rows:1][0m UPDATE "cards" SET "created_at"='2025-08-27 17:31:11.584',"updated_at"='2025-08-27 10:33:41.797',"deleted_at"=NULL,"title"='Updated Test Card',"description"='Testing JSONB custom fields functionality',"value"=200000,"priority"='high',"stage_id"='04492c32-4348-4db5-b6a9-aedefdfb73b9',"contact_id"=NULL,"company_id"=NULL,"assigned_to_id"=NULL,"expected_close_date"=NULL,"actual_close_date"=NULL,"custom_fields"='{"additional_notes":"This is a test of custom fields","budget":"$100K - $500K","contact_date":"2025-08-27","interested_in":["Product A","Product B"],"source":"Referral","status":"Hot Lead"}' WHERE "cards"."deleted_at" IS NULL AND "id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:33:41 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/activity_repository.go:25
[0m[33m[2.856ms] [34;1m[rows:1][0m INSERT INTO "activities" ("created_at","updated_at","deleted_at","type","title","description","due_date","completed_at","card_id","contact_id","company_id","user_id","id") VALUES ('2025-08-27 10:33:41.852','2025-08-27 10:33:41.852',NULL,'updated','Card updated','Card \'Updated Test Card\' was updated',NULL,NULL,'74a58135-517e-4d27-80b0-7d49a07a5b34',NULL,NULL,'9f61e195-b989-42cd-a052-658e9b622820','ea65a08e-0943-4f76-b9be-14b18e3679c7') RETURNING "id","metadata"
[2025-08-27 10:33:41] 200 -    58.96668ms PUT /api/v1/cards/74a58135-517e-4d27-80b0-7d49a07a5b34 -
[2025-08-27 10:39:32] 401 -      64.224µs GET /api/v1/realtime/events -
[2025-08-27 10:39:32] 204 -      20.409µs OPTIONS /api/dashboard/stats -

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.557ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '7535ac7b-eb8e-4322-a77f-a9728e74a2cd' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:163
[0m[33m[0.208ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:168
[0m[33m[0.167ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE cards.deleted_at IS NULL AND "cards"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:176
[0m[33m[0.366ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:184
[0m[33m[0.391ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_lost = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:193
[0m[33m[0.393ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.expected_close_date < '2025-08-27 17:39:33.212' AND cards.deleted_at IS NULL) AND (stages.is_closed_won = false AND stages.is_closed_lost = false) AND "cards"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:203
[0m[33m[0.445ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value), 0) as total FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:216
[0m[33m[0.455ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value * probability / 100.0), 0) as expected FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = false AND stages.is_closed_lost = false AND cards.deleted_at IS NULL) AND "cards"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:238
[0m[33m[0.207ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:243
[0m[33m[0.215ms] [34;1m[rows:1][0m SELECT count(*) FROM "stages" WHERE is_active = true AND "stages"."deleted_at" IS NULL

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:264
[0m[33m[0.479ms] [34;1m[rows:1][0m SELECT stages.id as stage_id, stages.name as stage_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.id, stages.name ORDER BY stages.sort_order ASC

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:296
[0m[33m[0.564ms] [34;1m[rows:1][0m SELECT pipelines.id as pipeline_id, pipelines.name as pipeline_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id JOIN pipelines ON stages.pipeline_id = pipelines.id WHERE (cards.deleted_at IS NULL AND pipelines.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY pipelines.id, pipelines.name ORDER BY pipelines.sort_order ASC

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:326
[0m[33m[0.333ms] [34;1m[rows:2][0m SELECT type, COUNT(*) as count, MAX(created_at) as last_updated FROM "activities" WHERE created_at >= '2025-07-28 17:39:33.215' AND "activities"."deleted_at" IS NULL GROUP BY "type" ORDER BY count DESC LIMIT 10

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:367
[0m[33m[0.605ms] [34;1m[rows:0][0m SELECT 
			users.id as user_id,
			users.first_name,
			users.last_name,
			COUNT(cards.id) as cards_count,
			COALESCE(SUM(CASE WHEN stages.is_closed_won THEN cards.value ELSE 0 END), 0) as total_revenue,
			COUNT(CASE WHEN stages.is_closed_won THEN 1 END) as closed_won
		 FROM "cards" LEFT JOIN users ON cards.assigned_to_id = users.id JOIN stages ON cards.stage_id = stages.id WHERE (cards.created_at >= '2025-05-27 17:39:33.215' AND users.id IS NOT NULL) AND "cards"."deleted_at" IS NULL GROUP BY users.id, users.first_name, users.last_name ORDER BY total_revenue DESC LIMIT 5

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:408
[0m[33m[0.572ms] [34;1m[rows:0][0m SELECT 
			TO_CHAR(cards.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(cards.value), 0) as revenue,
			COUNT(*) as count
		 FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = true AND cards.actual_close_date >= '2024-08-27 17:39:33.216') AND "cards"."deleted_at" IS NULL GROUP BY TO_CHAR(cards.actual_close_date, 'YYYY-MM') ORDER BY month DESC LIMIT 12

2025/08/27 17:39:33 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:437
[0m[33m[0.659ms] [34;1m[rows:1][0m SELECT stages.name as stage_name, COUNT(cards.id) as card_count, stages.sort_order FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.name, stages.sort_order ORDER BY stages.sort_order ASC
[2025-08-27 10:39:32] 200 -    7.490404ms GET /api/dashboard/stats -
[2025-08-27 10:39:33] 401 -      35.338µs GET /api/v1/realtime/events -
[2025-08-27 10:39:36] 401 -      33.314µs GET /api/v1/realtime/events -
[2025-08-27 10:39:42] 401 -      33.755µs GET /api/v1/realtime/events -
[2025-08-27 10:39:52] 204 -       10.56µs OPTIONS /api/pipelines -

2025/08/27 17:39:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.612ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '7535ac7b-eb8e-4322-a77f-a9728e74a2cd' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:39:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.378ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:39:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.542ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:39:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.145ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:39:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.746ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:39:52] 200 -    3.077781ms GET /api/pipelines -
[2025-08-27 10:39:56] 401 -      29.447µs GET /api/v1/realtime/events -
[2025-08-27 10:40:25] 401 -      43.424µs GET /api/v1/realtime/events -
[2025-08-27 10:40:55] 401 -      47.942µs GET /api/v1/realtime/events -
[2025-08-27 10:41:25] 401 -      54.034µs GET /api/v1/realtime/events -
[2025-08-27 10:41:55] 401 -      29.848µs GET /api/v1/realtime/events -
[2025-08-27 10:42:25] 401 -      47.161µs GET /api/v1/realtime/events -
[2025-08-27 10:42:55] 401 -       31.58µs GET /api/v1/realtime/events -
[2025-08-27 10:45:04] 204 -      16.652µs OPTIONS /api/v1/auth/logout -

2025/08/27 17:45:04 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.453ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '7535ac7b-eb8e-4322-a77f-a9728e74a2cd' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:45:04] 200 -     581.661µs POST /api/v1/auth/logout -
[2025-08-27 10:45:24] 204 -      19.107µs OPTIONS /api/v1/auth/login -

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.515ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:45:24] 200 -   65.744843ms POST /api/v1/auth/login -

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.565ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:163
[0m[33m[0.267ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:168
[0m[33m[0.230ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE cards.deleted_at IS NULL AND "cards"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:176
[0m[33m[0.440ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:184
[0m[33m[0.307ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_lost = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:193
[0m[33m[0.317ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.expected_close_date < '2025-08-27 17:45:24.709' AND cards.deleted_at IS NULL) AND (stages.is_closed_won = false AND stages.is_closed_lost = false) AND "cards"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:203
[0m[33m[0.435ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value), 0) as total FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:216
[0m[33m[0.524ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value * probability / 100.0), 0) as expected FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = false AND stages.is_closed_lost = false AND cards.deleted_at IS NULL) AND "cards"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:238
[0m[33m[0.264ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:243
[0m[33m[0.233ms] [34;1m[rows:1][0m SELECT count(*) FROM "stages" WHERE is_active = true AND "stages"."deleted_at" IS NULL

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:264
[0m[33m[0.557ms] [34;1m[rows:1][0m SELECT stages.id as stage_id, stages.name as stage_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.id, stages.name ORDER BY stages.sort_order ASC

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:296
[0m[33m[0.582ms] [34;1m[rows:1][0m SELECT pipelines.id as pipeline_id, pipelines.name as pipeline_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id JOIN pipelines ON stages.pipeline_id = pipelines.id WHERE (cards.deleted_at IS NULL AND pipelines.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY pipelines.id, pipelines.name ORDER BY pipelines.sort_order ASC

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:326
[0m[33m[0.363ms] [34;1m[rows:2][0m SELECT type, COUNT(*) as count, MAX(created_at) as last_updated FROM "activities" WHERE created_at >= '2025-07-28 17:45:24.712' AND "activities"."deleted_at" IS NULL GROUP BY "type" ORDER BY count DESC LIMIT 10

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:367
[0m[33m[0.586ms] [34;1m[rows:0][0m SELECT 
			users.id as user_id,
			users.first_name,
			users.last_name,
			COUNT(cards.id) as cards_count,
			COALESCE(SUM(CASE WHEN stages.is_closed_won THEN cards.value ELSE 0 END), 0) as total_revenue,
			COUNT(CASE WHEN stages.is_closed_won THEN 1 END) as closed_won
		 FROM "cards" LEFT JOIN users ON cards.assigned_to_id = users.id JOIN stages ON cards.stage_id = stages.id WHERE (cards.created_at >= '2025-05-27 17:45:24.712' AND users.id IS NOT NULL) AND "cards"."deleted_at" IS NULL GROUP BY users.id, users.first_name, users.last_name ORDER BY total_revenue DESC LIMIT 5

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:408
[0m[33m[0.345ms] [34;1m[rows:0][0m SELECT 
			TO_CHAR(cards.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(cards.value), 0) as revenue,
			COUNT(*) as count
		 FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = true AND cards.actual_close_date >= '2024-08-27 17:45:24.713') AND "cards"."deleted_at" IS NULL GROUP BY TO_CHAR(cards.actual_close_date, 'YYYY-MM') ORDER BY month DESC LIMIT 12

2025/08/27 17:45:24 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:437
[0m[33m[0.348ms] [34;1m[rows:1][0m SELECT stages.name as stage_name, COUNT(cards.id) as card_count, stages.sort_order FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.name, stages.sort_order ORDER BY stages.sort_order ASC
[2025-08-27 10:45:24] 200 -    7.256455ms GET /api/dashboard/stats -

2025/08/27 17:45:35 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.545ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:45:35 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.291ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:45:35 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.382ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:45:35 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.843ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:45:35 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.161ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:45:35] 200 -    2.286035ms GET /api/pipelines -
[2025-08-27 10:45:48] 204 -       8.156µs OPTIONS /api/v1/pipelines -

2025/08/27 17:45:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.473ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:45:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.196ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:45:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.304ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:45:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.719ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:45:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.086ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:45:48] 200 -    1.989123ms GET /api/v1/pipelines -

2025/08/27 17:46:39 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.795ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:46:39] 401 -      68.241µs GET /api/v1/realtime/events -

2025/08/27 17:46:39 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.312ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:46:39 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.580ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:46:39 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.183ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:46:39 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.687ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:46:39] 200 -    3.174899ms GET /api/pipelines -
[2025-08-27 10:46:40] 204 -      18.276µs OPTIONS /api/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -
[2025-08-27 10:46:40] 204 -       15.77µs OPTIONS /api/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.672ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.769ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.939ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[2.854ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:46:40] 200 -    3.841393ms GET /api/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.550ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.418ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.079ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.811ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:46:40] 200 -    2.775189ms GET /api/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -
[2025-08-27 10:46:40] 204 -       10.08µs OPTIONS /api/v1/cards -

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.430ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.258ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.480ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.246ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.228ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:46:40] 200 -    2.220148ms GET /api/v1/cards -

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.482ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.245ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.389ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.355ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:46:40 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.307ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:46:40] 200 -    2.328658ms GET /api/v1/cards -
[2025-08-27 10:46:40] 401 -      22.754µs GET /api/v1/realtime/events -
[2025-08-27 10:46:43] 401 -      35.478µs GET /api/v1/realtime/events -
[2025-08-27 10:46:47] 401 -      27.483µs GET /api/v1/realtime/events -
[2025-08-27 10:46:57] 401 -      25.639µs GET /api/v1/realtime/events -

2025/08/27 17:46:59 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.654ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:46:59] 200 -   66.117373ms POST /api/v1/auth/login -

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.618ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:163
[0m[33m[0.251ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:168
[0m[33m[0.167ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE cards.deleted_at IS NULL AND "cards"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:176
[0m[33m[0.440ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:184
[0m[33m[0.476ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_lost = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:193
[0m[33m[0.484ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.expected_close_date < '2025-08-27 17:47:00.26' AND cards.deleted_at IS NULL) AND (stages.is_closed_won = false AND stages.is_closed_lost = false) AND "cards"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:203
[0m[33m[0.482ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value), 0) as total FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:216
[0m[33m[0.457ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value * probability / 100.0), 0) as expected FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = false AND stages.is_closed_lost = false AND cards.deleted_at IS NULL) AND "cards"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:238
[0m[33m[0.185ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:243
[0m[33m[0.192ms] [34;1m[rows:1][0m SELECT count(*) FROM "stages" WHERE is_active = true AND "stages"."deleted_at" IS NULL

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:264
[0m[33m[0.485ms] [34;1m[rows:1][0m SELECT stages.id as stage_id, stages.name as stage_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.id, stages.name ORDER BY stages.sort_order ASC

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:296
[0m[33m[0.609ms] [34;1m[rows:1][0m SELECT pipelines.id as pipeline_id, pipelines.name as pipeline_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id JOIN pipelines ON stages.pipeline_id = pipelines.id WHERE (cards.deleted_at IS NULL AND pipelines.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY pipelines.id, pipelines.name ORDER BY pipelines.sort_order ASC

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:326
[0m[33m[0.332ms] [34;1m[rows:2][0m SELECT type, COUNT(*) as count, MAX(created_at) as last_updated FROM "activities" WHERE created_at >= '2025-07-28 17:47:00.263' AND "activities"."deleted_at" IS NULL GROUP BY "type" ORDER BY count DESC LIMIT 10

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:367
[0m[33m[0.577ms] [34;1m[rows:0][0m SELECT 
			users.id as user_id,
			users.first_name,
			users.last_name,
			COUNT(cards.id) as cards_count,
			COALESCE(SUM(CASE WHEN stages.is_closed_won THEN cards.value ELSE 0 END), 0) as total_revenue,
			COUNT(CASE WHEN stages.is_closed_won THEN 1 END) as closed_won
		 FROM "cards" LEFT JOIN users ON cards.assigned_to_id = users.id JOIN stages ON cards.stage_id = stages.id WHERE (cards.created_at >= '2025-05-27 17:47:00.263' AND users.id IS NOT NULL) AND "cards"."deleted_at" IS NULL GROUP BY users.id, users.first_name, users.last_name ORDER BY total_revenue DESC LIMIT 5

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:408
[0m[33m[0.369ms] [34;1m[rows:0][0m SELECT 
			TO_CHAR(cards.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(cards.value), 0) as revenue,
			COUNT(*) as count
		 FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = true AND cards.actual_close_date >= '2024-08-27 17:47:00.264') AND "cards"."deleted_at" IS NULL GROUP BY TO_CHAR(cards.actual_close_date, 'YYYY-MM') ORDER BY month DESC LIMIT 12

2025/08/27 17:47:00 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:437
[0m[33m[0.388ms] [34;1m[rows:1][0m SELECT stages.name as stage_name, COUNT(cards.id) as card_count, stages.sort_order FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.name, stages.sort_order ORDER BY stages.sort_order ASC
[2025-08-27 10:47:00] 200 -    7.264589ms GET /api/dashboard/stats -

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.618ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.469ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.175ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.629ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:47:10] 200 -    2.478342ms GET /api/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.564ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.523ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.255ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.704ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:47:10] 200 -    2.493892ms GET /api/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[8.021ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[1.800ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.368ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.743ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[3.561ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:47:10] 200 -    13.66194ms GET /api/v1/cards -

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.853ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.244ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.280ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.180ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:47:10 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.842ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:47:10] 200 -    2.328625ms GET /api/v1/cards -
[2025-08-27 10:47:16] 401 -      30.198µs GET /api/v1/realtime/events -
[2025-08-27 10:47:46] 401 -      30.308µs GET /api/v1/realtime/events -
[2025-08-27 10:48:15] 401 -      35.208µs GET /api/v1/realtime/events -

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[1.061ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:48:17] 401 -       36.36µs GET /api/v1/realtime/events -

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[1.481ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.880ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.828ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[4.236ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:48:17] 200 -     7.20196ms GET /api/v1/pipelines -
[2025-08-27 10:48:17] 204 -      14.117µs OPTIONS /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -
[2025-08-27 10:48:17] 204 -       7.364µs OPTIONS /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.533ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.495ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.286ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.839ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:48:17] 200 -     2.58592ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.483ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.367ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.993ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.434ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:48:17] 200 -    2.117058ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[1.010ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.368ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.384ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.310ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.286ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:48:17] 200 -     3.04294ms GET /api/v1/cards -

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.333ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.217ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.361ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.218ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:48:18 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.013ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:48:18] 200 -    1.824013ms GET /api/v1/cards -
[2025-08-27 10:48:18] 401 -      29.938µs GET /api/v1/realtime/events -
[2025-08-27 10:48:21] 401 -      25.549µs GET /api/v1/realtime/events -
[2025-08-27 10:48:26] 401 -      31.901µs GET /api/v1/realtime/events -
[2025-08-27 10:48:28] 401 -      31.721µs GET /api/v1/realtime/events -
[2025-08-27 10:48:29] 401 -      36.531µs GET /api/v1/realtime/events -
[2025-08-27 10:48:32] 401 -      37.913µs GET /api/v1/realtime/events -
[2025-08-27 10:48:38] 401 -      57.601µs GET /api/v1/realtime/events -
[2025-08-27 10:48:49] 401 -      95.413µs GET /api/v1/realtime/events -
[2025-08-27 10:49:12] 401 -     107.086µs GET /api/v1/realtime/events -
[2025-08-27 10:49:39] 401 -      31.982µs GET /api/v1/realtime/events -
[2025-08-27 10:49:40] 401 -      31.351µs GET /api/v1/realtime/events -
[2025-08-27 10:49:43] 401 -      25.429µs GET /api/v1/realtime/events -
[2025-08-27 10:49:50] 401 -       26.21µs GET /api/v1/realtime/events -

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.746ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.429ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL
[2025-08-27 10:49:52] 401 -      67.159µs GET /api/v1/realtime/events -

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.672ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[2.063ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[2.629ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:49:52] 200 -      4.1847ms GET /api/v1/pipelines -

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.379ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.308ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.852ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.309ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:49:52] 200 -    1.911321ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.513ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.398ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.119ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.713ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:49:52] 200 -    2.451221ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.481ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.307ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.393ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.267ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.132ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:49:52] 200 -    2.194026ms GET /api/v1/cards -

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.406ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.381ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.538ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.354ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:49:52 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.554ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:49:52] 200 -    2.683078ms GET /api/v1/cards -
[2025-08-27 10:49:53] 401 -      44.476µs GET /api/v1/realtime/events -
[2025-08-27 10:49:55] 401 -      41.159µs GET /api/v1/realtime/events -
[2025-08-27 10:49:59] 401 -      30.709µs GET /api/v1/realtime/events -
[2025-08-27 10:50:10] 401 -      26.711µs GET /api/v1/realtime/events -
[2025-08-27 10:50:30] 401 -      41.209µs GET /api/v1/realtime/events -

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.940ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:50:42] 200 -   60.870985ms POST /api/v1/auth/login -

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.358ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:163
[0m[33m[0.264ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:168
[0m[33m[0.580ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE cards.deleted_at IS NULL AND "cards"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:176
[0m[33m[0.845ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:184
[0m[33m[0.505ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_lost = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:193
[0m[33m[0.562ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.expected_close_date < '2025-08-27 17:50:42.899' AND cards.deleted_at IS NULL) AND (stages.is_closed_won = false AND stages.is_closed_lost = false) AND "cards"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:203
[0m[33m[0.728ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value), 0) as total FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:216
[0m[33m[0.671ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value * probability / 100.0), 0) as expected FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = false AND stages.is_closed_lost = false AND cards.deleted_at IS NULL) AND "cards"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:238
[0m[33m[0.212ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:243
[0m[33m[0.333ms] [34;1m[rows:1][0m SELECT count(*) FROM "stages" WHERE is_active = true AND "stages"."deleted_at" IS NULL

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:264
[0m[33m[0.828ms] [34;1m[rows:1][0m SELECT stages.id as stage_id, stages.name as stage_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.id, stages.name ORDER BY stages.sort_order ASC

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:296
[0m[33m[0.883ms] [34;1m[rows:1][0m SELECT pipelines.id as pipeline_id, pipelines.name as pipeline_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id JOIN pipelines ON stages.pipeline_id = pipelines.id WHERE (cards.deleted_at IS NULL AND pipelines.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY pipelines.id, pipelines.name ORDER BY pipelines.sort_order ASC

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:326
[0m[33m[1.440ms] [34;1m[rows:2][0m SELECT type, COUNT(*) as count, MAX(created_at) as last_updated FROM "activities" WHERE created_at >= '2025-07-28 17:50:42.904' AND "activities"."deleted_at" IS NULL GROUP BY "type" ORDER BY count DESC LIMIT 10

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:367
[0m[33m[1.621ms] [34;1m[rows:0][0m SELECT 
			users.id as user_id,
			users.first_name,
			users.last_name,
			COUNT(cards.id) as cards_count,
			COALESCE(SUM(CASE WHEN stages.is_closed_won THEN cards.value ELSE 0 END), 0) as total_revenue,
			COUNT(CASE WHEN stages.is_closed_won THEN 1 END) as closed_won
		 FROM "cards" LEFT JOIN users ON cards.assigned_to_id = users.id JOIN stages ON cards.stage_id = stages.id WHERE (cards.created_at >= '2025-05-27 17:50:42.905' AND users.id IS NOT NULL) AND "cards"."deleted_at" IS NULL GROUP BY users.id, users.first_name, users.last_name ORDER BY total_revenue DESC LIMIT 5

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:408
[0m[33m[0.677ms] [34;1m[rows:0][0m SELECT 
			TO_CHAR(cards.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(cards.value), 0) as revenue,
			COUNT(*) as count
		 FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = true AND cards.actual_close_date >= '2024-08-27 17:50:42.907') AND "cards"."deleted_at" IS NULL GROUP BY TO_CHAR(cards.actual_close_date, 'YYYY-MM') ORDER BY month DESC LIMIT 12

2025/08/27 17:50:42 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:437
[0m[33m[0.442ms] [34;1m[rows:1][0m SELECT stages.name as stage_name, COUNT(cards.id) as card_count, stages.sort_order FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.name, stages.sort_order ORDER BY stages.sort_order ASC
[2025-08-27 10:50:42] 200 -   11.673936ms GET /api/dashboard/stats -
[2025-08-27 10:51:00] 401 -      42.722µs GET /api/v1/realtime/events -

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.624ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:51:04] 401 -      22.243µs GET /api/v1/realtime/events -

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.368ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.358ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.799ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.201ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:51:04] 200 -    2.560712ms GET /api/v1/pipelines -

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.538ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.292ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.704ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.115ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:51:04] 200 -    1.882877ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.386ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.355ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.807ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.270ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:51:04] 200 -    1.856385ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.636ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.266ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.268ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.174ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.940ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:51:04] 200 -    2.234895ms GET /api/v1/cards -

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.420ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.265ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.222ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.178ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:51:05 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.807ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:51:04] 200 -    1.743178ms GET /api/v1/cards -
[2025-08-27 10:51:05] 401 -      37.472µs GET /api/v1/realtime/events -
[2025-08-27 10:51:08] 401 -      25.459µs GET /api/v1/realtime/events -
[2025-08-27 10:51:12] 401 -      27.122µs GET /api/v1/realtime/events -
[2025-08-27 10:51:23] 401 -      64.614µs GET /api/v1/realtime/events -
[2025-08-27 10:51:43] 401 -      27.353µs GET /api/v1/realtime/events -

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[1.145ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:51:48] 200 -   82.083631ms POST /api/v1/auth/login -

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.408ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:163
[0m[33m[0.178ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:168
[0m[33m[0.145ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE cards.deleted_at IS NULL AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:176
[0m[33m[0.559ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:184
[0m[33m[0.626ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_lost = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:193
[0m[33m[0.481ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.expected_close_date < '2025-08-27 17:51:48.406' AND cards.deleted_at IS NULL) AND (stages.is_closed_won = false AND stages.is_closed_lost = false) AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:203
[0m[33m[0.551ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value), 0) as total FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:216
[0m[33m[0.530ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value * probability / 100.0), 0) as expected FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = false AND stages.is_closed_lost = false AND cards.deleted_at IS NULL) AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:238
[0m[33m[0.284ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:243
[0m[33m[0.217ms] [34;1m[rows:1][0m SELECT count(*) FROM "stages" WHERE is_active = true AND "stages"."deleted_at" IS NULL

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:264
[0m[33m[0.458ms] [34;1m[rows:1][0m SELECT stages.id as stage_id, stages.name as stage_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.id, stages.name ORDER BY stages.sort_order ASC

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:296
[0m[33m[0.543ms] [34;1m[rows:1][0m SELECT pipelines.id as pipeline_id, pipelines.name as pipeline_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id JOIN pipelines ON stages.pipeline_id = pipelines.id WHERE (cards.deleted_at IS NULL AND pipelines.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY pipelines.id, pipelines.name ORDER BY pipelines.sort_order ASC

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:326
[0m[33m[0.290ms] [34;1m[rows:2][0m SELECT type, COUNT(*) as count, MAX(created_at) as last_updated FROM "activities" WHERE created_at >= '2025-07-28 17:51:48.41' AND "activities"."deleted_at" IS NULL GROUP BY "type" ORDER BY count DESC LIMIT 10

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:367
[0m[33m[0.552ms] [34;1m[rows:0][0m SELECT 
			users.id as user_id,
			users.first_name,
			users.last_name,
			COUNT(cards.id) as cards_count,
			COALESCE(SUM(CASE WHEN stages.is_closed_won THEN cards.value ELSE 0 END), 0) as total_revenue,
			COUNT(CASE WHEN stages.is_closed_won THEN 1 END) as closed_won
		 FROM "cards" LEFT JOIN users ON cards.assigned_to_id = users.id JOIN stages ON cards.stage_id = stages.id WHERE (cards.created_at >= '2025-05-27 17:51:48.41' AND users.id IS NOT NULL) AND "cards"."deleted_at" IS NULL GROUP BY users.id, users.first_name, users.last_name ORDER BY total_revenue DESC LIMIT 5

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:408
[0m[33m[0.341ms] [34;1m[rows:0][0m SELECT 
			TO_CHAR(cards.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(cards.value), 0) as revenue,
			COUNT(*) as count
		 FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = true AND cards.actual_close_date >= '2024-08-27 17:51:48.411') AND "cards"."deleted_at" IS NULL GROUP BY TO_CHAR(cards.actual_close_date, 'YYYY-MM') ORDER BY month DESC LIMIT 12

2025/08/27 17:51:48 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:437
[0m[33m[0.352ms] [34;1m[rows:1][0m SELECT stages.name as stage_name, COUNT(cards.id) as card_count, stages.sort_order FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.name, stages.sort_order ORDER BY stages.sort_order ASC
[2025-08-27 10:51:48] 200 -    7.345929ms GET /api/dashboard/stats -

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.587ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.388ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.949ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.411ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:51:49] 200 -    2.254874ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.384ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.320ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.877ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.246ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:51:49] 200 -    1.896363ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.424ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.228ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.309ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.213ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.949ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:51:49] 200 -    1.860724ms GET /api/v1/cards -

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.328ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.240ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.269ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.182ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:51:49 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.805ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:51:49] 200 -    1.574052ms GET /api/v1/cards -
[2025-08-27 10:52:14] 401 -      35.168µs GET /api/v1/realtime/events -
[2025-08-27 10:52:44] 401 -       46.38µs GET /api/v1/realtime/events -
[2025-08-27 10:55:00] 401 -     115.342µs HEAD /api/v1/pipelines -
[2025-08-27 10:55:55] 401 -      59.845µs GET /api/v1/realtime/events -
[2025-08-27 10:55:56] 401 -      33.625µs GET /api/v1/realtime/events -
[2025-08-27 10:55:58] 401 -      40.448µs GET /api/v1/realtime/events -
[2025-08-27 10:56:03] 401 -      66.348µs GET /api/v1/realtime/events -
[2025-08-27 10:56:07] 204 -       9.539µs OPTIONS /api/v1/auth/login -

2025/08/27 17:56:07 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[0.584ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:56:07] 200 -   89.842628ms POST /api/v1/auth/login -
[2025-08-27 10:56:07] 204 -      10.821µs OPTIONS /api/dashboard/stats -

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.414ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:163
[0m[33m[0.622ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:168
[0m[33m[0.261ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE cards.deleted_at IS NULL AND "cards"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:176
[0m[33m[0.694ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:184
[0m[33m[0.497ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_lost = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:193
[0m[33m[0.461ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.expected_close_date < '2025-08-27 17:56:08.175' AND cards.deleted_at IS NULL) AND (stages.is_closed_won = false AND stages.is_closed_lost = false) AND "cards"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:203
[0m[33m[0.470ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value), 0) as total FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE stages.is_closed_won = true AND "cards"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:216
[0m[33m[0.665ms] [34;1m[rows:0][0m SELECT COALESCE(SUM(value * probability / 100.0), 0) as expected FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = false AND stages.is_closed_lost = false AND cards.deleted_at IS NULL) AND "cards"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:238
[0m[33m[0.245ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:243
[0m[33m[0.317ms] [34;1m[rows:1][0m SELECT count(*) FROM "stages" WHERE is_active = true AND "stages"."deleted_at" IS NULL

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:264
[0m[33m[0.788ms] [34;1m[rows:1][0m SELECT stages.id as stage_id, stages.name as stage_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.id, stages.name ORDER BY stages.sort_order ASC

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:296
[0m[33m[0.758ms] [34;1m[rows:1][0m SELECT pipelines.id as pipeline_id, pipelines.name as pipeline_name, COUNT(cards.id) as card_count, COALESCE(SUM(cards.value), 0) as total_value FROM "cards" JOIN stages ON cards.stage_id = stages.id JOIN pipelines ON stages.pipeline_id = pipelines.id WHERE (cards.deleted_at IS NULL AND pipelines.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY pipelines.id, pipelines.name ORDER BY pipelines.sort_order ASC

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:326
[0m[33m[0.642ms] [34;1m[rows:2][0m SELECT type, COUNT(*) as count, MAX(created_at) as last_updated FROM "activities" WHERE created_at >= '2025-07-28 17:56:08.179' AND "activities"."deleted_at" IS NULL GROUP BY "type" ORDER BY count DESC LIMIT 10

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:367
[0m[33m[1.007ms] [34;1m[rows:0][0m SELECT 
			users.id as user_id,
			users.first_name,
			users.last_name,
			COUNT(cards.id) as cards_count,
			COALESCE(SUM(CASE WHEN stages.is_closed_won THEN cards.value ELSE 0 END), 0) as total_revenue,
			COUNT(CASE WHEN stages.is_closed_won THEN 1 END) as closed_won
		 FROM "cards" LEFT JOIN users ON cards.assigned_to_id = users.id JOIN stages ON cards.stage_id = stages.id WHERE (cards.created_at >= '2025-05-27 17:56:08.18' AND users.id IS NOT NULL) AND "cards"."deleted_at" IS NULL GROUP BY users.id, users.first_name, users.last_name ORDER BY total_revenue DESC LIMIT 5

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:408
[0m[33m[0.597ms] [34;1m[rows:0][0m SELECT 
			TO_CHAR(cards.actual_close_date, 'YYYY-MM') as month,
			COALESCE(SUM(cards.value), 0) as revenue,
			COUNT(*) as count
		 FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (stages.is_closed_won = true AND cards.actual_close_date >= '2024-08-27 17:56:08.181') AND "cards"."deleted_at" IS NULL GROUP BY TO_CHAR(cards.actual_close_date, 'YYYY-MM') ORDER BY month DESC LIMIT 12

2025/08/27 17:56:08 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/http/handlers/dashboard_handler.go:437
[0m[33m[0.558ms] [34;1m[rows:1][0m SELECT stages.name as stage_name, COUNT(cards.id) as card_count, stages.sort_order FROM "cards" JOIN stages ON cards.stage_id = stages.id WHERE (cards.deleted_at IS NULL AND stages.is_active = true) AND "cards"."deleted_at" IS NULL GROUP BY stages.name, stages.sort_order ORDER BY stages.sort_order ASC
[2025-08-27 10:56:07] 200 -    9.915974ms GET /api/dashboard/stats -
[2025-08-27 10:56:13] 401 -      39.216µs GET /api/v1/realtime/events -
[2025-08-27 10:56:24] 204 -      14.127µs OPTIONS /api/v1/pipelines -

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.619ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.219ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.414ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.864ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.257ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:56:24] 200 -    2.381288ms GET /api/v1/pipelines -
[2025-08-27 10:56:24] 204 -      17.464µs OPTIONS /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -
[2025-08-27 10:56:24] 204 -       7.304µs OPTIONS /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.548ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.546ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.291ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.816ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:56:24] 200 -    2.628935ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.402ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.302ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.906ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.380ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:56:24] 200 -     2.06061ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -
[2025-08-27 10:56:24] 204 -      21.702µs OPTIONS /api/v1/cards -

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.377ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.201ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.270ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.195ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.001ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:56:24] 200 -    1.791301ms GET /api/v1/cards -

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.234ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.178ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.175ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.170ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:56:25 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.848ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:56:24] 200 -    1.535378ms GET /api/v1/cards -
[2025-08-27 10:56:34] 401 -      36.831µs GET /api/v1/realtime/events -
[2025-08-27 10:57:04] 401 -      26.191µs GET /api/v1/realtime/events -
[2025-08-27 10:57:34] 401 -      31.761µs GET /api/v1/realtime/events -
[2025-08-27 10:58:04] 401 -      86.637µs GET /api/v1/realtime/events -
[2025-08-27 10:58:34] 401 -      34.627µs GET /api/v1/realtime/events -

2025/08/27 17:58:46 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:40
[0m[33m[1.178ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE email = '<EMAIL>' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1
[2025-08-27 10:58:46] 200 -   61.363752ms POST /api/v1/auth/login -

2025/08/27 17:58:46 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.623ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:58:46 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.264ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:58:46 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.812ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:58:46 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.444ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:58:46] 200 -    2.326562ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -
[2025-08-27 10:59:04] 401 -      28.484µs GET /api/v1/realtime/events -
[2025-08-27 10:59:13] 401 -      47.231µs GET /api/v1/realtime/events -

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.904ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:103
[0m[33m[0.248ms] [34;1m[rows:1][0m SELECT count(*) FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.371ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL LIMIT 50

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[0.789ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE is_active = true AND "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:128
[0m[33m[1.149ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE is_active = true AND "pipelines"."deleted_at" IS NULL ORDER BY sort_order ASC, name ASC LIMIT 50
[2025-08-27 10:59:13] 200 -    2.679583ms GET /api/v1/pipelines -

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.880ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.197ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.591ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.895ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:59:13] 200 -    2.062284ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.408ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.318ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."stage_id" IN ('04492c32-4348-4db5-b6a9-aedefdfb73b9','ab8ce8f4-0dab-41db-9f43-80d83180bb70','be04474a-62f5-4d5f-a067-887c5a816227','6dd27345-77f2-43fb-8653-04257e85e3dd','4f22f98a-9773-4cd5-a316-3d8c73b6ab03','dffcb6ca-e52b-4cf4-84bb-a3cfb520a4c6') AND "cards"."deleted_at" IS NULL

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[0.726ms] [34;1m[rows:6][0m SELECT * FROM "stages" WHERE "stages"."pipeline_id" = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "stages"."deleted_at" IS NULL ORDER BY sort_order ASC

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/pipeline_repository.go:48
[0m[33m[1.102ms] [34;1m[rows:1][0m SELECT * FROM "pipelines" WHERE id = 'a59b996f-85a3-4d95-8100-13ff097efe5a' AND "pipelines"."deleted_at" IS NULL ORDER BY "pipelines"."id" LIMIT 1
[2025-08-27 10:59:13] 200 -    1.725094ms GET /api/v1/pipelines/a59b996f-85a3-4d95-8100-13ff097efe5a -

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.499ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.318ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.243ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.213ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.886ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:59:13] 200 -    1.992749ms GET /api/v1/cards -

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/user_repository.go:30
[0m[33m[0.491ms] [34;1m[rows:1][0m SELECT * FROM "users" WHERE id = '9f61e195-b989-42cd-a052-658e9b622820' AND "users"."deleted_at" IS NULL ORDER BY "users"."id" LIMIT 1

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:89
[0m[33m[0.307ms] [34;1m[rows:1][0m SELECT count(*) FROM "cards" WHERE "cards"."deleted_at" IS NULL

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.271ms] [34;1m[rows:1][0m SELECT * FROM "stages" WHERE "stages"."id" = '04492c32-4348-4db5-b6a9-aedefdfb73b9' AND "stages"."deleted_at" IS NULL

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[0.238ms] [34;1m[rows:0][0m SELECT * FROM "card_tags" WHERE "card_tags"."card_id" = '74a58135-517e-4d27-80b0-7d49a07a5b34'

2025/08/27 17:59:13 [32m/home/<USER>/start_up/CRM_NEW/internal/adapters/database/repositories/card_repository.go:103
[0m[33m[1.212ms] [34;1m[rows:1][0m SELECT * FROM "cards" WHERE "cards"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 10
[2025-08-27 10:59:13] 200 -     2.54865ms GET /api/v1/cards -
[2025-08-27 10:59:14] 401 -      45.006µs GET /api/v1/realtime/events -
[2025-08-27 10:59:16] 401 -      25.179µs GET /api/v1/realtime/events -
[2025-08-27 10:59:21] 401 -      28.054µs GET /api/v1/realtime/events -
[2025-08-27 10:59:32] 401 -       46.89µs GET /api/v1/realtime/events -
[2025-08-27 10:59:55] 401 -      35.508µs GET /api/v1/realtime/events -
2025/08/27 18:02:19 🔄 Shutting down server...
2025/08/27 18:02:19 ✅ Server stopped
