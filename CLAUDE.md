# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

```bash
# Initialize and start the system (first time)
make init

# Start development environment
make dev

# Run backend in development (outside Docker)
go run main.go

# Run frontend in development (outside Docker)
cd frontend && npm run dev

# View logs
make logs
make backend-logs
make frontend-logs

# Database operations
make db-shell         # Connect to PostgreSQL
docker-compose exec postgres psql -U postgres -d crm

# Run Playwright E2E tests
npx playwright test test-crm-ui.spec.js

# Restart services
make restart

# Clean everything
make clean
```

## Architecture Overview

### Backend - Go with Clean Architecture

The backend follows Clean Architecture principles with clear separation of concerns:

- **Domain Layer** (`internal/domain/`)
  - `entities/`: Core business entities (Card, Pipeline, Contact, Company, User)
  - `repositories/`: Repository interfaces defining data access contracts
  
- **Use Cases Layer** (`internal/usecases/`)
  - Business logic implementation
  - Orchestrates between domain entities and external systems
  
- **Adapters Layer** (`internal/adapters/`)
  - `http/handlers/`: Fiber HTTP handlers
  - `database/repositories/`: GORM repository implementations
  - `events/sse_manager.go`: Server-Sent Events for real-time updates
  
- **Infrastructure** (`internal/infrastructure/`)
  - Configuration management (Viper)
  - JWT authentication

### Frontend - Next.js 14 with App Router

- **App Router Structure** (`frontend/src/app/`)
  - Authentication pages: `/auth/login`, `/auth/register`
  - Dashboard with nested routes: `/dashboard`, `/dashboard/pipeline`, `/dashboard/contacts`
  
- **State Management**
  - Zustand stores in `frontend/src/stores/`
  - React Query for server state in `frontend/src/lib/react-query/`
  
- **Components**
  - shadcn/ui components in `frontend/src/components/ui/`
  - Feature components organized by domain (pipeline, contacts, companies)
  - Drag & drop using @dnd-kit

### Database Schema

PostgreSQL with JSONB for dynamic fields:
- Cards store custom fields in `custom_fields` JSONB column
- Pipelines have multiple stages with customizable names
- Full audit trail via activities table

### API Endpoints

Base URL: `http://localhost:8080/api/v1`

Key endpoints:
- Auth: `/auth/login`, `/auth/register`, `/auth/refresh`
- Pipelines: CRUD at `/pipelines`
- Cards: CRUD at `/cards`, move between stages at `/cards/:id/move`
- Real-time: SSE at `/realtime/events`

## Development Workflow

### Making Backend Changes

1. Domain entities are in `internal/domain/entities/`
2. Use cases in `internal/usecases/` handle business logic
3. HTTP handlers in `internal/adapters/http/handlers/`
4. Database repositories in `internal/adapters/database/repositories/`
5. Migrations go in `migrations/` as numbered SQL files

### Making Frontend Changes

1. Pages use Next.js 14 App Router in `frontend/src/app/`
2. Shared components in `frontend/src/components/`
3. API client functions in `frontend/src/lib/api/`
4. Type definitions in `frontend/src/types/`
5. Use shadcn/ui components from `frontend/src/components/ui/`

### Testing

- E2E tests: `npx playwright test test-crm-ui.spec.js`
- Frontend type checking: `cd frontend && npm run type-check`
- Frontend linting: `cd frontend && npm run lint`

## Key Features

- **Dynamic Fields**: Cards support JSONB custom fields without schema changes
- **Real-time Updates**: SSE provides instant updates across clients
- **Drag & Drop**: Move cards between pipeline stages
- **JWT Auth**: Access and refresh token system
- **File Storage**: MinIO for S3-compatible file storage (port 9000)

## Environment Variables

Critical environment variables set in docker-compose.yml:
- `CRM_AUTH_JWT_SECRET`: JWT signing key
- `CRM_DATABASE_*`: PostgreSQL connection
- `CRM_REDIS_*`: Redis cache connection
- Frontend expects API at `http://localhost:8080`

## Common Tasks

### Add a new API endpoint
1. Define handler in `internal/adapters/http/handlers/`
2. Add route in `cmd/server/main.go` or relevant router setup
3. Implement use case in `internal/usecases/`
4. Add repository method if needed

### Add a new frontend page
1. Create route folder in `frontend/src/app/`
2. Add `page.tsx` file
3. Update navigation in `frontend/src/components/dashboard/sidebar.tsx`

### Modify pipeline stages
1. Update stage definitions in database
2. Stages are customizable per pipeline via API
3. Frontend dynamically renders based on pipeline configuration