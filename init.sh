#!/bin/bash

set -e

echo "🚀 CRM System Initialization Script"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed${NC}"
    echo "Please install Docker first: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed${NC}"
    echo "Please install Docker Compose first: https://docs.docker.com/compose/install/"
    exit 1
fi

# Function to generate random string
generate_secret() {
    openssl rand -hex 32 2>/dev/null || cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 32 | head -n 1
}

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}Creating .env file...${NC}"
    cat > .env << EOF
# Database
DB_PASSWORD=$(generate_secret)

# JWT
JWT_SECRET=$(generate_secret)

# MinIO
MINIO_USER=minioadmin
MINIO_PASSWORD=$(generate_secret)

# PgAdmin
PGADMIN_PASSWORD=$(generate_secret)

# Telegram Bot (Optional - add your token here)
TELEGRAM_BOT_TOKEN=

# API URLs
API_URL=http://localhost:8080
WS_URL=ws://localhost:8080

# Environment
APP_ENV=development
EOF
    echo -e "${GREEN}✓ .env file created${NC}"
else
    echo -e "${YELLOW}.env file already exists, skipping...${NC}"
fi

# Create necessary directories
echo -e "${YELLOW}Creating necessary directories...${NC}"
mkdir -p uploads
mkdir -p nginx/ssl
mkdir -p frontend/public

# Create nginx config if it doesn't exist
if [ ! -f nginx/nginx.conf ]; then
    echo -e "${YELLOW}Creating nginx configuration...${NC}"
    cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8080;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name localhost;

        client_max_body_size 100M;

        # API routes
        location /api {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # SSE endpoint
        location /api/v1/events {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection '';
            proxy_set_header Cache-Control 'no-cache';
            proxy_set_header X-Accel-Buffering 'no';
            proxy_buffering off;
            chunked_transfer_encoding off;
        }

        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }
    }
}
EOF
    echo -e "${GREEN}✓ Nginx configuration created${NC}"
fi

# Create frontend .env.local if it doesn't exist
if [ ! -f frontend/.env.local ]; then
    echo -e "${YELLOW}Creating frontend .env.local...${NC}"
    cat > frontend/.env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080
EOF
    echo -e "${GREEN}✓ Frontend .env.local created${NC}"
fi

# Install backend dependencies
echo -e "${YELLOW}Installing backend dependencies...${NC}"
if [ -f go.mod ]; then
    go mod download
    echo -e "${GREEN}✓ Backend dependencies installed${NC}"
else
    echo -e "${RED}Warning: go.mod not found, skipping backend dependencies${NC}"
fi

# Install frontend dependencies
echo -e "${YELLOW}Installing frontend dependencies...${NC}"
if [ -f frontend/package.json ]; then
    cd frontend
    npm install --legacy-peer-deps || npm install
    cd ..
    echo -e "${GREEN}✓ Frontend dependencies installed${NC}"
else
    echo -e "${RED}Warning: frontend/package.json not found, skipping frontend dependencies${NC}"
fi

# Start services
echo -e "${YELLOW}Starting Docker services...${NC}"
docker-compose -f docker-compose.yml up -d

# Wait for services to be healthy
echo -e "${YELLOW}Waiting for services to be healthy...${NC}"
sleep 10

# Check service health
echo -e "${YELLOW}Checking service health...${NC}"
services=("postgres" "redis" "minio" "backend" "frontend")
all_healthy=true

for service in "${services[@]}"; do
    if docker-compose ps | grep -q "$service.*Up"; then
        echo -e "${GREEN}✓ $service is running${NC}"
    else
        echo -e "${RED}✗ $service is not running${NC}"
        all_healthy=false
    fi
done

if [ "$all_healthy" = true ]; then
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}✓ CRM System initialized successfully!${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    echo "🌐 Access points:"
    echo "   Frontend:    http://localhost:3000"
    echo "   Backend API: http://localhost:8080"
    echo "   MinIO:       http://localhost:9001"
    echo "   PgAdmin:     http://localhost:5050 (run with --profile tools)"
    echo ""
    echo "📝 Default credentials:"
    echo "   Email:    <EMAIL>"
    echo "   Password: admin123"
    echo ""
    echo "🚀 Quick commands:"
    echo "   View logs:       docker-compose logs -f"
    echo "   Stop services:   docker-compose down"
    echo "   Reset database:  docker-compose down -v && ./init.sh"
    echo "   With PgAdmin:    docker-compose --profile tools up -d"
else
    echo -e "${RED}================================${NC}"
    echo -e "${RED}Some services failed to start${NC}"
    echo -e "${RED}Check logs: docker-compose logs${NC}"
    echo -e "${RED}================================${NC}"
    exit 1
fi