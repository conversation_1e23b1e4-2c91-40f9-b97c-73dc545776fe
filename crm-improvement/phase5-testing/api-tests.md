# CRM API Testing Plan

## Executive Summary

This document outlines a comprehensive testing strategy for the CRM API system, focusing on performance, reliability, and scalability testing to ensure the API can handle production workloads and viral growth scenarios.

## API Endpoints Overview

### Base URL
- Development: `http://localhost:8080`
- Production: `https://api.crm.example.com`

### Authentication Endpoints
- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/register` - User registration  
- `POST /api/v1/auth/refresh` - Token refresh (protected)
- `GET /api/v1/auth/profile` - Get user profile (protected)
- `POST /api/v1/auth/logout` - User logout (protected)

### Core Business Endpoints
- **Cards**: Full CRUD + move operations at `/api/v1/cards`
- **Pipelines**: Full CRUD + stage management at `/api/v1/pipelines`
- **Contacts**: Full CRUD + search at `/api/v1/contacts`
- **Companies**: Full CRUD + search at `/api/v1/companies`
- **Users**: Management operations at `/api/v1/users`
- **Roles**: RBAC operations at `/api/v1/roles`
- **Files**: Upload/download at `/api/v1/files`
- **Dashboard**: Analytics at `/api/v1/dashboard`
- **Comments**: Commenting system
- **Real-time**: SSE at `/api/v1/realtime/events`

## 1. Load Testing Scenarios

### 1.1 Baseline Performance Tests

#### Test Case: API-LOAD-001 - Authentication Load
**Objective**: Measure auth endpoint performance under varying loads
**Tool**: k6
**Configuration**:
```javascript
// auth-load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export let options = {
  stages: [
    { duration: '2m', target: 10 },   // Ramp up
    { duration: '5m', target: 50 },   // Stay at 50 users
    { duration: '2m', target: 100 },  // Ramp to 100 users
    { duration: '5m', target: 100 },  // Stay at 100 users
    { duration: '2m', target: 0 },    // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'],
    errors: ['rate<0.1'],
  },
};

export default function () {
  const loginPayload = JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  });
  
  const params = {
    headers: { 'Content-Type': 'application/json' },
  };
  
  const response = http.post('http://localhost:8080/api/v1/auth/login', loginPayload, params);
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'has access token': (r) => JSON.parse(r.body).access_token !== undefined,
  });
  
  errorRate.add(response.status !== 200);
  sleep(1);
}
```

**Success Criteria**:
- P95 response time < 500ms
- Error rate < 0.1%
- Throughput > 200 RPS

#### Test Case: API-LOAD-002 - CRUD Operations Load
**Objective**: Test core business operations under load
**Scenarios**:
- Card creation/updates (70% read, 30% write)
- Pipeline operations (80% read, 20% write)
- Contact/Company searches (90% read, 10% write)

```javascript
// crud-load-test.js
export let options = {
  scenarios: {
    card_operations: {
      executor: 'constant-vus',
      vus: 50,
      duration: '10m',
      tags: { test_type: 'card_crud' },
    },
    pipeline_reads: {
      executor: 'constant-vus', 
      vus: 30,
      duration: '10m',
      tags: { test_type: 'pipeline_read' },
    },
    contact_search: {
      executor: 'constant-vus',
      vus: 20,
      duration: '10m', 
      tags: { test_type: 'contact_search' },
    },
  },
  thresholds: {
    'http_req_duration{test_type:card_crud}': ['p(95)<1000'],
    'http_req_duration{test_type:pipeline_read}': ['p(95)<200'],
    'http_req_duration{test_type:contact_search}': ['p(95)<300'],
  },
};
```

### 1.2 Spike Testing

#### Test Case: API-LOAD-003 - Traffic Spike Simulation
**Objective**: Test system behavior during sudden traffic increases (viral scenarios)

```javascript
// spike-test.js
export let options = {
  stages: [
    { duration: '2m', target: 50 },   // Normal load
    { duration: '1m', target: 500 },  // 10x spike
    { duration: '5m', target: 500 },  // Sustained spike
    { duration: '2m', target: 50 },   // Recovery
    { duration: '5m', target: 50 },   // Normal operation
  ],
};
```

**Success Criteria**:
- System remains responsive during spike
- Recovery time < 30 seconds after spike ends
- No data corruption or loss
- Error rate < 1% during spike

### 1.3 Stress Testing

#### Test Case: API-LOAD-004 - Breaking Point Discovery
**Objective**: Find system limits and failure points

```bash
# Gradual load increase using k6
k6 run --stage 1m:100,1m:200,1m:300,1m:400,1m:500,1m:600 stress-test.js
```

**Metrics to Track**:
- Response times (P50, P95, P99)
- Error rates by endpoint
- System resources (CPU, Memory, DB connections)
- Database query performance

### 1.4 Soak Testing  

#### Test Case: API-LOAD-005 - Long Duration Stability
**Objective**: Verify system stability over extended periods

```javascript
// soak-test.js
export let options = {
  stages: [
    { duration: '5m', target: 100 },   // Ramp up
    { duration: '6h', target: 100 },   // Steady state
    { duration: '5m', target: 0 },     // Ramp down
  ],
};
```

**Success Criteria**:
- Memory usage remains stable (no leaks)
- Response times don't degrade over time
- Error rate remains < 0.1%
- Database connections properly managed

## 2. Performance Benchmarks

### 2.1 Response Time Targets

| Endpoint Category | P95 Target | P99 Target | Notes |
|------------------|------------|------------|-------|
| Authentication | <500ms | <1000ms | Include JWT generation |
| Simple GET operations | <100ms | <200ms | Single record lookup |
| List endpoints | <300ms | <500ms | With pagination |
| Search operations | <500ms | <800ms | Full-text search |
| CREATE operations | <800ms | <1200ms | Include validation |
| UPDATE operations | <600ms | <1000ms | Include validation |
| File uploads | <5000ms | <8000ms | Up to 10MB files |
| Dashboard/Analytics | <1000ms | <2000ms | Complex aggregations |
| Real-time events | <50ms | <100ms | SSE connection setup |

### 2.2 Throughput Targets

| Operation Type | Target RPS | Peak RPS | Notes |
|---------------|-----------|----------|-------|
| Authentication | 200 | 1000 | Login/register |
| Read operations | 1000 | 5000 | GET endpoints |
| Write operations | 100 | 500 | POST/PUT endpoints |
| Search operations | 200 | 1000 | Complex queries |
| File operations | 50 | 200 | Upload/download |

### 2.3 Resource Utilization Targets

- **CPU Usage**: <70% under normal load, <90% under peak
- **Memory Usage**: <80% of available, stable over time
- **Database Connections**: <80% of pool size
- **Response Size**: <1MB for list endpoints

## 3. Contract Testing

### 3.1 OpenAPI Specification Validation

#### Test Case: API-CONTRACT-001 - Schema Compliance
**Tool**: Dredd or Prism
**Configuration**:
```bash
# Generate OpenAPI spec from code annotations
swag init -g cmd/server/main.go -o ./docs

# Validate all endpoints against spec
dredd ./docs/swagger.yaml http://localhost:8080

# Contract testing with Pact
pact-broker publish ./pacts --consumer-app-version=1.0.0
```

#### Test Case: API-CONTRACT-002 - Response Format Validation
**Objective**: Ensure all responses match defined schemas

```javascript
// contract-test.js
import { check } from 'k6';
import { Ajv } from 'https://jslib.k6.io/ajv/6.12.5/index.js';

const ajv = new Ajv();

const loginResponseSchema = {
  type: 'object',
  properties: {
    access_token: { type: 'string' },
    refresh_token: { type: 'string' },
    expires_in: { type: 'number' },
    user: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        name: { type: 'string' }
      },
      required: ['id', 'email', 'name']
    }
  },
  required: ['access_token', 'refresh_token', 'expires_in', 'user']
};

const validate = ajv.compile(loginResponseSchema);

export default function() {
  const response = http.post('http://localhost:8080/api/v1/auth/login', payload);
  const isValid = validate(JSON.parse(response.body));
  
  check(response, {
    'schema validation passed': () => isValid,
  });
}
```

### 3.2 Backward Compatibility Testing

#### Test Case: API-CONTRACT-003 - Version Compatibility
**Objective**: Ensure API changes don't break existing clients

```bash
# Test against previous API version
newman run api-v1-tests.postman_collection.json --env production.json
newman run api-v2-tests.postman_collection.json --env production.json

# Compare responses
api-diff --old-spec v1-spec.yaml --new-spec v2-spec.yaml
```

## 4. Security Testing

### 4.1 Authentication & Authorization

#### Test Case: API-SEC-001 - JWT Security
**Objective**: Validate JWT implementation security

```javascript
// jwt-security-test.js
export default function() {
  // Test 1: Invalid JWT signature
  const invalidToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid.signature";
  
  const response1 = http.get('http://localhost:8080/api/v1/auth/profile', {
    headers: { 'Authorization': `Bearer ${invalidToken}` }
  });
  
  check(response1, {
    'rejects invalid JWT': (r) => r.status === 401,
  });
  
  // Test 2: Expired JWT
  const expiredToken = "expired.jwt.token";
  const response2 = http.get('http://localhost:8080/api/v1/auth/profile', {
    headers: { 'Authorization': `Bearer ${expiredToken}` }
  });
  
  check(response2, {
    'rejects expired JWT': (r) => r.status === 401,
  });
  
  // Test 3: Missing Authorization header
  const response3 = http.get('http://localhost:8080/api/v1/auth/profile');
  
  check(response3, {
    'requires authorization': (r) => r.status === 401,
  });
}
```

#### Test Case: API-SEC-002 - Role-Based Access Control
**Objective**: Verify RBAC implementation

```javascript
// rbac-test.js
export default function() {
  // Test with different user roles
  const adminToken = getTokenForRole('admin');
  const userToken = getTokenForRole('user');
  const guestToken = getTokenForRole('guest');
  
  // Admin-only endpoint
  const adminResponse = http.get('http://localhost:8080/api/v1/users', {
    headers: { 'Authorization': `Bearer ${adminToken}` }
  });
  
  check(adminResponse, {
    'admin can access users': (r) => r.status === 200,
  });
  
  // User trying admin endpoint
  const userResponse = http.get('http://localhost:8080/api/v1/users', {
    headers: { 'Authorization': `Bearer ${userToken}` }
  });
  
  check(userResponse, {
    'user cannot access admin endpoint': (r) => r.status === 403,
  });
}
```

### 4.2 Input Validation & Injection Prevention

#### Test Case: API-SEC-003 - SQL Injection Prevention
**Objective**: Test for SQL injection vulnerabilities

```bash
# Using SQLMap
sqlmap -u "http://localhost:8080/api/v1/contacts?search=test" \
       --headers="Authorization: Bearer TOKEN" \
       --batch --level=5 --risk=3

# Manual injection tests
curl -X GET "http://localhost:8080/api/v1/contacts?search=' OR 1=1--" \
     -H "Authorization: Bearer TOKEN"
```

#### Test Case: API-SEC-004 - XSS Prevention
**Objective**: Test for cross-site scripting vulnerabilities

```javascript
// xss-test.js
const xssPayloads = [
  '<script>alert("XSS")</script>',
  '"><script>alert("XSS")</script>',
  'javascript:alert("XSS")',
  '<img src=x onerror=alert("XSS")>',
];

export default function() {
  xssPayloads.forEach(payload => {
    const response = http.post('http://localhost:8080/api/v1/contacts', 
      JSON.stringify({ name: payload, email: '<EMAIL>' }),
      { headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer TOKEN' } }
    );
    
    check(response, {
      'XSS payload sanitized': (r) => !r.body.includes('<script>'),
    });
  });
}
```

## 5. Error Handling Verification

### 5.1 HTTP Status Code Validation

#### Test Case: API-ERROR-001 - Proper Status Codes
**Objective**: Verify correct HTTP status codes for different scenarios

```javascript
// error-handling-test.js
export default function() {
  // Test 400 - Bad Request
  const badRequest = http.post('http://localhost:8080/api/v1/auth/login',
    JSON.stringify({ email: 'invalid-email' }),
    { headers: { 'Content-Type': 'application/json' } }
  );
  
  check(badRequest, {
    'returns 400 for invalid input': (r) => r.status === 400,
    'has error message': (r) => JSON.parse(r.body).error !== undefined,
  });
  
  // Test 401 - Unauthorized
  const unauthorized = http.get('http://localhost:8080/api/v1/auth/profile');
  check(unauthorized, {
    'returns 401 for missing auth': (r) => r.status === 401,
  });
  
  // Test 404 - Not Found
  const notFound = http.get('http://localhost:8080/api/v1/cards/non-existent-id',
    { headers: { 'Authorization': 'Bearer TOKEN' } }
  );
  check(notFound, {
    'returns 404 for non-existent resource': (r) => r.status === 404,
  });
  
  // Test 422 - Validation Error
  const validationError = http.post('http://localhost:8080/api/v1/cards',
    JSON.stringify({ title: '' }), // Empty title
    { headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer TOKEN' } }
  );
  check(validationError, {
    'returns 422 for validation errors': (r) => r.status === 422,
    'includes field errors': (r) => JSON.parse(r.body).fields !== undefined,
  });
}
```

### 5.2 Error Response Format Consistency

#### Test Case: API-ERROR-002 - Error Response Schema
**Objective**: Ensure all error responses follow consistent format

```javascript
const errorResponseSchema = {
  type: 'object',
  properties: {
    error: { type: 'string' },
    code: { type: 'string' },
    timestamp: { type: 'string' },
    path: { type: 'string' },
    fields: {
      type: 'object',
      additionalProperties: { type: 'string' }
    }
  },
  required: ['error']
};
```

## 6. Rate Limiting Tests

### 6.1 Rate Limit Enforcement

#### Test Case: API-RATE-001 - General Rate Limits
**Objective**: Verify rate limiting works correctly
**Current Limits**: 100 requests/minute per IP, 1000 requests/hour per user

```javascript
// rate-limit-test.js
export let options = {
  scenarios: {
    rate_limit_test: {
      executor: 'constant-arrival-rate',
      rate: 200, // 200 requests per second
      timeUnit: '1s',
      duration: '2m',
      preAllocatedVUs: 50,
      maxVUs: 100,
    },
  },
};

export default function() {
  const response = http.get('http://localhost:8080/api/v1/pipelines',
    { headers: { 'Authorization': 'Bearer TOKEN' } }
  );
  
  check(response, {
    'rate limit enforced': (r) => r.status === 200 || r.status === 429,
    'rate limit header present': (r) => r.headers['X-Ratelimit-Remaining'] !== undefined,
  });
  
  if (response.status === 429) {
    console.log('Rate limit hit at request #' + __ITER);
  }
}
```

#### Test Case: API-RATE-002 - Rate Limit Recovery
**Objective**: Test system recovery after rate limit period

```bash
# Bash script to test rate limit recovery
#!/bin/bash
echo "Testing rate limit recovery..."

# Hit rate limit
for i in {1..150}; do
  curl -s -o /dev/null -w "%{http_code}\n" \
    -H "Authorization: Bearer TOKEN" \
    http://localhost:8080/api/v1/pipelines
done

echo "Waiting 60 seconds for rate limit reset..."
sleep 60

# Test recovery
response=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer TOKEN" \
  http://localhost:8080/api/v1/pipelines)

if [ "$response" -eq 200 ]; then
  echo "✅ Rate limit recovery successful"
else
  echo "❌ Rate limit recovery failed: $response"
fi
```

## 7. Concurrent User Scenarios

### 7.1 Multi-User Operations

#### Test Case: API-CONCURRENT-001 - Concurrent Card Operations
**Objective**: Test system behavior with multiple users modifying same resources

```javascript
// concurrent-operations-test.js
import { SharedArray } from 'k6/data';

const users = new SharedArray('users', function () {
  return JSON.parse(open('./test-users.json'));
});

export let options = {
  scenarios: {
    concurrent_card_updates: {
      executor: 'per-vu-iterations',
      vus: 10,
      iterations: 50,
      maxDuration: '10m',
    },
  },
};

export default function() {
  const user = users[__VU - 1];
  const token = authenticateUser(user);
  
  // Multiple users updating the same card
  const cardId = 'shared-card-id';
  const updatePayload = JSON.stringify({
    title: `Updated by VU ${__VU} at ${new Date().toISOString()}`,
    description: `Iteration ${__ITER}`
  });
  
  const response = http.put(`http://localhost:8080/api/v1/cards/${cardId}`,
    updatePayload,
    { headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` } }
  );
  
  check(response, {
    'update successful': (r) => r.status === 200,
    'no data corruption': (r) => {
      const data = JSON.parse(r.body);
      return data.title.includes(`VU ${__VU}`);
    },
  });
}
```

#### Test Case: API-CONCURRENT-002 - Real-time Event Consistency
**Objective**: Verify SSE events are delivered consistently to multiple clients

```javascript
// sse-concurrent-test.js
export let options = {
  scenarios: {
    sse_listeners: {
      executor: 'constant-vus',
      vus: 20,
      duration: '5m',
    },
  },
};

export default function() {
  // Establish SSE connection
  const sseResponse = http.get('http://localhost:8080/api/v1/realtime/events',
    { 
      headers: { 'Authorization': 'Bearer TOKEN' },
      responseType: 'stream'
    }
  );
  
  check(sseResponse, {
    'SSE connection established': (r) => r.status === 200,
    'content-type is text/event-stream': (r) => 
      r.headers['Content-Type'].includes('text/event-stream'),
  });
}
```

## 8. Data Integrity Tests

### 8.1 Transaction Consistency

#### Test Case: API-DATA-001 - ACID Compliance
**Objective**: Verify database transactions maintain ACID properties

```javascript
// data-integrity-test.js
export default function() {
  // Test transaction rollback on error
  const invalidCardData = JSON.stringify({
    title: 'Test Card',
    stage_id: 'non-existent-stage', // This should cause transaction rollback
    contacts: [
      { name: 'Contact 1', email: '<EMAIL>' },
      { name: 'Contact 2', email: '<EMAIL>' }
    ]
  });
  
  const response = http.post('http://localhost:8080/api/v1/cards',
    invalidCardData,
    { headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer TOKEN' } }
  );
  
  check(response, {
    'transaction rolled back': (r) => r.status === 400 || r.status === 422,
  });
  
  // Verify no partial data was created
  const verifyResponse = http.get('http://localhost:8080/api/v1/contacts?search=Contact 1',
    { headers: { 'Authorization': 'Bearer TOKEN' } }
  );
  
  check(verifyResponse, {
    'no orphaned contacts created': (r) => {
      const data = JSON.parse(r.body);
      return data.data.length === 0;
    },
  });
}
```

### 8.2 Custom Fields Validation

#### Test Case: API-DATA-002 - JSONB Field Integrity
**Objective**: Test custom fields (JSONB) data integrity

```javascript
// jsonb-integrity-test.js
export default function() {
  const customFieldsPayload = JSON.stringify({
    title: 'Card with Custom Fields',
    stage_id: 'valid-stage-id',
    custom_fields: {
      priority: 'high',
      estimated_value: 50000.50,
      tags: ['prospect', 'enterprise'],
      metadata: {
        source: 'website',
        campaign: 'Q4-2024'
      }
    }
  });
  
  const createResponse = http.post('http://localhost:8080/api/v1/cards',
    customFieldsPayload,
    { headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer TOKEN' } }
  );
  
  check(createResponse, {
    'card created with custom fields': (r) => r.status === 201,
  });
  
  if (createResponse.status === 201) {
    const cardData = JSON.parse(createResponse.body);
    const cardId = cardData.data.id;
    
    // Retrieve and verify custom fields
    const getResponse = http.get(`http://localhost:8080/api/v1/cards/${cardId}`,
      { headers: { 'Authorization': 'Bearer TOKEN' } }
    );
    
    check(getResponse, {
      'custom fields preserved': (r) => {
        const data = JSON.parse(r.body);
        const fields = data.data.custom_fields;
        return fields.priority === 'high' && 
               fields.estimated_value === 50000.50 &&
               Array.isArray(fields.tags) &&
               fields.metadata.source === 'website';
      },
    });
  }
}
```

## 9. API Versioning Tests

### 9.1 Version Routing

#### Test Case: API-VERSION-001 - Version Header Support
**Objective**: Test API versioning through headers and URL paths

```javascript
// version-test.js
export default function() {
  // Test v1 endpoint
  const v1Response = http.get('http://localhost:8080/api/v1/pipelines',
    { headers: { 'Authorization': 'Bearer TOKEN' } }
  );
  
  check(v1Response, {
    'v1 endpoint accessible': (r) => r.status === 200,
    'v1 response format': (r) => {
      const data = JSON.parse(r.body);
      return data.version === 'v1' || data.data !== undefined;
    },
  });
  
  // Test version via header
  const headerVersionResponse = http.get('http://localhost:8080/api/pipelines',
    { 
      headers: { 
        'Authorization': 'Bearer TOKEN',
        'API-Version': 'v1'
      } 
    }
  );
  
  check(headerVersionResponse, {
    'header version routing works': (r) => r.status === 200,
  });
  
  // Test unsupported version
  const unsupportedResponse = http.get('http://localhost:8080/api/v999/pipelines',
    { headers: { 'Authorization': 'Bearer TOKEN' } }
  );
  
  check(unsupportedResponse, {
    'unsupported version returns 404': (r) => r.status === 404,
  });
}
```

### 9.2 Deprecation Handling

#### Test Case: API-VERSION-002 - Deprecation Warnings
**Objective**: Test deprecated endpoint behavior

```javascript
// deprecation-test.js
export default function() {
  // Test deprecated endpoint (if any exist)
  const deprecatedResponse = http.get('http://localhost:8080/api/cards', // Non-versioned endpoint
    { headers: { 'Authorization': 'Bearer TOKEN' } }
  );
  
  check(deprecatedResponse, {
    'deprecated endpoint still works': (r) => r.status === 200,
    'deprecation warning present': (r) => 
      r.headers['Warning'] !== undefined || 
      r.headers['Sunset'] !== undefined,
  });
}
```

## 10. Documentation Validation

### 10.1 OpenAPI Specification Accuracy

#### Test Case: API-DOC-001 - Swagger/OpenAPI Validation
**Objective**: Ensure API documentation matches implementation

```bash
# Generate swagger docs from code
swag init -g cmd/server/main.go -o ./docs

# Validate swagger spec
swagger-codegen validate -i ./docs/swagger.yaml

# Test all documented endpoints
dredd ./docs/swagger.yaml http://localhost:8080 \
  --header "Authorization: Bearer TOKEN"
```

#### Test Case: API-DOC-002 - Response Schema Validation
**Objective**: Verify all documented response schemas are accurate

```javascript
// schema-validation-test.js
import { Ajv } from 'https://jslib.k6.io/ajv/6.12.5/index.js';

const ajv = new Ajv();

// Load schemas from OpenAPI spec
const schemas = JSON.parse(open('./docs/schemas.json'));

export default function() {
  // Test each endpoint against its schema
  Object.keys(schemas).forEach(endpoint => {
    const schema = schemas[endpoint];
    const validate = ajv.compile(schema);
    
    const response = http.get(`http://localhost:8080${endpoint}`,
      { headers: { 'Authorization': 'Bearer TOKEN' } }
    );
    
    if (response.status === 200) {
      const isValid = validate(JSON.parse(response.body));
      check(response, {
        [`${endpoint} matches schema`]: () => isValid,
      });
      
      if (!isValid) {
        console.log(`Schema validation failed for ${endpoint}:`, validate.errors);
      }
    }
  });
}
```

## Test Execution Plan

### Phase 1: Setup & Baseline (Week 1)
1. **Environment Setup**
   - Set up test environment matching production
   - Configure monitoring and metrics collection
   - Prepare test data and user accounts
   
2. **Baseline Testing**
   - Execute basic functionality tests
   - Establish performance baselines
   - Document current system behavior

### Phase 2: Load & Performance Testing (Week 2)
1. **Load Testing Execution**
   - Run all load test scenarios
   - Collect performance metrics
   - Identify bottlenecks

2. **Performance Optimization**
   - Analyze results and optimize
   - Re-run tests to verify improvements
   - Document optimizations

### Phase 3: Security & Reliability (Week 3)
1. **Security Testing**
   - Execute security test suite
   - Perform penetration testing
   - Fix vulnerabilities

2. **Error Handling & Edge Cases**
   - Test error scenarios
   - Validate error responses
   - Test system recovery

### Phase 4: Integration & Chaos Testing (Week 4)
1. **Integration Testing**
   - End-to-end workflow testing
   - Third-party integration testing
   - Cross-browser API consumption

2. **Chaos Testing**
   - Simulate failures
   - Test system resilience
   - Validate monitoring and alerting

### Phase 5: Documentation & Reporting (Week 5)
1. **Documentation Validation**
   - Verify API documentation accuracy
   - Test code examples
   - Validate schemas

2. **Final Reporting**
   - Compile test results
   - Create performance report
   - Document recommendations

## Test Tools & Infrastructure

### Load Testing Tools
- **k6**: Primary load testing tool
- **Apache JMeter**: Complex scenario testing
- **Artillery**: Quick performance checks
- **Gatling**: High-performance testing

### Security Testing Tools
- **OWASP ZAP**: Automated security scanning
- **SQLMap**: SQL injection testing
- **Burp Suite**: Manual security testing
- **Nmap**: Network scanning

### Contract Testing Tools
- **Dredd**: OpenAPI specification testing
- **Pact**: Consumer-driven contract testing
- **Postman/Newman**: API collection testing
- **Insomnia**: API testing and documentation

### Monitoring & Observability
- **Prometheus**: Metrics collection
- **Grafana**: Metrics visualization
- **Jaeger**: Distributed tracing
- **ELK Stack**: Log analysis

### CI/CD Integration
```yaml
# .github/workflows/api-tests.yml
name: API Testing Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  api-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: crm_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Go
        uses: actions/setup-go@v3
        with:
          go-version: '1.21'
          
      - name: Install k6
        run: |
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver keyserver.ubuntu.com --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
      - name: Start CRM API
        run: |
          go run cmd/server/main.go &
          sleep 10
          
      - name: Run Load Tests
        run: |
          k6 run tests/load/auth-load-test.js
          k6 run tests/load/crud-load-test.js
          
      - name: Run Contract Tests
        run: |
          k6 run tests/contract/schema-validation-test.js
          
      - name: Run Security Tests
        run: |
          k6 run tests/security/jwt-security-test.js
          k6 run tests/security/injection-test.js
          
      - name: Upload Results
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: test-results/
```

## Success Criteria Summary

### Performance Criteria
- **Response Times**: 95% of requests under target thresholds
- **Throughput**: Sustained RPS targets met
- **Error Rates**: <0.1% for normal operations, <1% during spikes
- **Resource Usage**: CPU <70%, Memory <80% under normal load

### Reliability Criteria
- **Uptime**: 99.9% availability during tests
- **Recovery**: <30 seconds recovery after failures
- **Data Integrity**: Zero data corruption or loss
- **Consistency**: All real-time events delivered correctly

### Security Criteria
- **Authentication**: 100% pass rate on auth/authz tests
- **Injection Prevention**: Zero successful injection attacks
- **Input Validation**: All invalid inputs properly rejected
- **Rate Limiting**: Correctly enforced per documented limits

### Quality Criteria
- **Contract Compliance**: 100% OpenAPI specification adherence
- **Documentation**: All endpoints documented and validated
- **Error Handling**: Consistent error responses across all endpoints
- **Versioning**: Proper version handling and deprecation warnings

## Risk Assessment & Mitigation

### High-Risk Areas
1. **Database Performance**: Complex queries with JSONB fields
2. **Real-time Events**: SSE connection management at scale
3. **File Uploads**: Memory usage during large file operations
4. **Authentication**: JWT token validation performance

### Mitigation Strategies
1. **Database Monitoring**: Query analysis and index optimization
2. **Connection Pooling**: Proper SSE connection lifecycle management
3. **Streaming**: Implement streaming for large file operations
4. **Token Caching**: Cache validated tokens to reduce overhead

## Recommendations

1. **Implement Circuit Breakers**: Add circuit breaker pattern for external dependencies
2. **Add Request Tracing**: Implement distributed tracing for complex operations
3. **Performance Monitoring**: Set up real-time performance monitoring
4. **Automated Testing**: Integrate performance tests into CI/CD pipeline
5. **Capacity Planning**: Use test results for infrastructure scaling decisions
6. **Documentation**: Maintain up-to-date API documentation with performance characteristics

This comprehensive API testing plan provides a structured approach to validating the CRM system's performance, reliability, and security characteristics. Regular execution of these tests will ensure the API can handle production workloads and scale effectively as the business grows.