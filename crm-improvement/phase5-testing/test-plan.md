# CRM System Comprehensive Testing Strategy

**Date:** January 29, 2025  
**Version:** 1.0  
**Target Implementation:** Phase 4 Improvements & AI Features  

## Executive Summary

This comprehensive testing strategy covers all aspects of the CRM system testing lifecycle, including unit tests, integration tests, end-to-end testing, performance testing, security testing, accessibility testing, and AI/ML feature validation. The strategy is designed to ensure high quality delivery of the Phase 4 improvements while maintaining system reliability and user experience.

**Key Objectives:**
- Achieve 90% test coverage for critical business logic
- Implement risk-based testing approach prioritizing high-impact features
- Establish automated testing pipeline for CI/CD integration
- Ensure comprehensive validation of AI/ML features
- Maintain testing performance with <15 minute full test suite execution

## System Architecture Overview

**Tech Stack:**
- Backend: Go 1.21+ with Fiber v2, GORM, PostgreSQL, Redis
- Frontend: Next.js 14, React Query, shadcn/ui, TypeScript
- E2E Testing: Playwright
- AI/ML: Python microservices with various ML frameworks

---

## 1. Unit Testing Strategy

### 1.1 Backend Unit Tests (Go)

**Framework:** Go's built-in testing package + Testify + Gomock

**Test Coverage Targets:**
- Business Logic (Use Cases): 95% coverage
- Domain Entities: 90% coverage
- Repository Implementations: 85% coverage
- HTTP Handlers: 80% coverage
- Middleware: 85% coverage

**Testing Structure:**
```
tests/
├── unit/
│   ├── usecases/
│   │   ├── auth_usecase_test.go
│   │   ├── card_usecase_test.go
│   │   ├── pipeline_usecase_test.go
│   │   ├── ai_usecase_test.go
│   │   └── ...
│   ├── handlers/
│   │   ├── auth_handler_test.go
│   │   ├── card_handler_test.go
│   │   └── ...
│   ├── repositories/
│   │   ├── card_repository_test.go
│   │   ├── user_repository_test.go
│   │   └── ...
│   └── entities/
│       ├── card_test.go
│       ├── user_test.go
│       └── ...
├── mocks/
│   ├── repositories/
│   ├── services/
│   └── external_apis/
└── testdata/
    ├── fixtures/
    └── golden_files/
```

**Key Test Categories:**

1. **Use Case Tests**
   - Business logic validation
   - Error handling scenarios
   - Edge cases and boundary conditions
   - Transaction handling
   - Cache invalidation logic

2. **Repository Tests**
   - Database operations (CRUD)
   - Complex queries and filtering
   - Pagination logic
   - Soft deletion behavior
   - Optimistic locking

3. **Handler Tests**
   - Request validation
   - Response formatting
   - Authentication/authorization
   - Error response handling
   - Rate limiting behavior

4. **AI Feature Tests**
   - Model prediction accuracy
   - Feature engineering correctness
   - API integration points
   - Fallback mechanisms
   - Cache behavior for AI results

**Example Test Implementation:**
```go
// tests/unit/usecases/card_usecase_test.go
package usecases_test

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/google/uuid"
    
    "crm/internal/domain/entities"
    "crm/internal/usecases"
    "crm/tests/mocks/repositories"
)

func TestCardUsecase_CreateCard(t *testing.T) {
    tests := []struct {
        name          string
        input         *entities.Card
        setupMocks    func(*repositories.MockCardRepository)
        expectedError error
        validate      func(*testing.T, *entities.Card, error)
    }{
        {
            name: "successful card creation",
            input: &entities.Card{
                Title:       "Test Card",
                Description: "Test Description",
                PipelineID:  uuid.New(),
                StageID:     uuid.New(),
            },
            setupMocks: func(repo *repositories.MockCardRepository) {
                repo.On("Create", mock.Anything, mock.AnythingOfType("*entities.Card")).
                    Return(nil).
                    Run(func(args mock.Arguments) {
                        card := args.Get(1).(*entities.Card)
                        card.ID = uuid.New()
                        card.CreatedAt = time.Now()
                    })
            },
            expectedError: nil,
            validate: func(t *testing.T, card *entities.Card, err error) {
                assert.NoError(t, err)
                assert.NotEqual(t, uuid.Nil, card.ID)
                assert.Equal(t, "Test Card", card.Title)
            },
        },
        {
            name: "validation error for empty title",
            input: &entities.Card{
                Title:      "", // Empty title should cause validation error
                PipelineID: uuid.New(),
                StageID:    uuid.New(),
            },
            setupMocks:    func(repo *repositories.MockCardRepository) {},
            expectedError: usecases.ErrInvalidCardTitle,
            validate: func(t *testing.T, card *entities.Card, err error) {
                assert.Error(t, err)
                assert.Equal(t, usecases.ErrInvalidCardTitle, err)
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mockRepo := &repositories.MockCardRepository{}
            tt.setupMocks(mockRepo)

            usecase := usecases.NewCardUsecase(mockRepo, nil, nil)
            result, err := usecase.Create(context.Background(), tt.input)

            tt.validate(t, result, err)
            mockRepo.AssertExpectations(t)
        })
    }
}
```

### 1.2 Frontend Unit Tests (TypeScript/React)

**Framework:** Jest + React Testing Library + MSW (Mock Service Worker)

**Test Coverage Targets:**
- Custom Hooks: 90% coverage
- Utility Functions: 95% coverage
- Store Logic (Zustand): 85% coverage
- API Client Functions: 80% coverage

**Testing Structure:**
```
frontend/
├── __tests__/
│   ├── components/
│   │   ├── pipeline/
│   │   │   ├── PipelineBoard.test.tsx
│   │   │   ├── CardItem.test.tsx
│   │   │   └── StageColumn.test.tsx
│   │   ├── forms/
│   │   │   ├── AddCardDialog.test.tsx
│   │   │   └── ContactForm.test.tsx
│   │   └── ui/
│   │       ├── Button.test.tsx
│   │       └── DataTable.test.tsx
│   ├── hooks/
│   │   ├── useDebounce.test.ts
│   │   ├── useInView.test.ts
│   │   └── useAuth.test.ts
│   ├── stores/
│   │   ├── authStore.test.ts
│   │   ├── pipelineStore.test.ts
│   │   └── notificationStore.test.ts
│   ├── lib/
│   │   ├── api/
│   │   │   ├── cards.test.ts
│   │   │   └── auth.test.ts
│   │   └── utils.test.ts
│   └── pages/
│       ├── Dashboard.test.tsx
│       └── Pipeline.test.tsx
├── __mocks__/
│   ├── next-router.js
│   ├── zustand.js
│   └── handlers.ts
├── jest.config.js
├── jest.setup.js
└── test-utils.tsx
```

**Key Test Categories:**

1. **Component Tests**
   - Rendering behavior
   - User interaction handling
   - Props validation
   - State management
   - Error boundaries

2. **Hook Tests**
   - State transitions
   - Side effects
   - Cleanup behavior
   - Error handling

3. **Store Tests**
   - State mutations
   - Action dispatching
   - Selector functions
   - Persistence behavior

4. **API Tests**
   - Request formatting
   - Response handling
   - Error scenarios
   - Caching behavior

**Example Test Implementation:**
```typescript
// frontend/__tests__/components/pipeline/PipelineBoard.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { DndContext } from '@dnd-kit/core'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { PipelineBoard } from '@/components/pipeline/pipeline-board'
import { mockPipeline, mockCards } from '@/__mocks__/data'

describe('PipelineBoard', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
  })

  const renderPipelineBoard = (props = {}) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <DndContext>
          <PipelineBoard
            pipeline={mockPipeline}
            cards={mockCards}
            {...props}
          />
        </DndContext>
      </QueryClientProvider>
    )
  }

  test('renders pipeline stages correctly', () => {
    renderPipelineBoard()
    
    expect(screen.getByText('Lead')).toBeInTheDocument()
    expect(screen.getByText('Qualified')).toBeInTheDocument()
    expect(screen.getByText('Proposal')).toBeInTheDocument()
    expect(screen.getByText('Closed')).toBeInTheDocument()
  })

  test('displays cards in correct stages', () => {
    renderPipelineBoard()
    
    const leadStage = screen.getByTestId('stage-lead')
    expect(leadStage).toHaveTextContent('Test Lead Card')
    
    const qualifiedStage = screen.getByTestId('stage-qualified')
    expect(qualifiedStage).toHaveTextContent('Test Qualified Card')
  })

  test('handles card drag and drop', async () => {
    const onCardMove = jest.fn()
    renderPipelineBoard({ onCardMove })

    const card = screen.getByTestId('card-1')
    const targetStage = screen.getByTestId('stage-qualified')

    fireEvent.dragStart(card)
    fireEvent.dragOver(targetStage)
    fireEvent.drop(targetStage)

    await waitFor(() => {
      expect(onCardMove).toHaveBeenCalledWith({
        cardId: '1',
        sourceStageId: 'lead',
        targetStageId: 'qualified'
      })
    })
  })
})
```

### 1.3 AI/ML Unit Tests (Python)

**Framework:** pytest + unittest.mock + pytest-mock

**Test Coverage Targets:**
- Feature Engineering: 90% coverage
- Model Prediction Logic: 85% coverage
- Data Processing Pipelines: 90% coverage
- Integration Adapters: 80% coverage

**Key Test Categories:**

1. **Model Tests**
   - Prediction accuracy validation
   - Input/output format validation
   - Edge case handling
   - Model versioning

2. **Feature Engineering Tests**
   - Data transformation correctness
   - Missing data handling
   - Feature scaling and normalization
   - Feature selection logic

3. **Pipeline Tests**
   - Data flow validation
   - Error handling
   - Retry mechanisms
   - Performance benchmarks

---

## 2. Integration Testing Strategy

### 2.1 API Integration Tests

**Framework:** Go test + testcontainers + PostgreSQL test containers

**Test Scope:**
- Complete API endpoint workflows
- Database integration validation
- External service integration
- Authentication/authorization flows
- Real-time SSE functionality

**Testing Structure:**
```
tests/
├── integration/
│   ├── api/
│   │   ├── auth_integration_test.go
│   │   ├── cards_integration_test.go
│   │   ├── pipeline_integration_test.go
│   │   ├── ai_features_integration_test.go
│   │   └── ...
│   ├── database/
│   │   ├── migrations_test.go
│   │   ├── queries_test.go
│   │   └── transactions_test.go
│   ├── external_services/
│   │   ├── redis_integration_test.go
│   │   ├── email_service_test.go
│   │   └── ai_service_test.go
│   └── realtime/
│       ├── sse_integration_test.go
│       └── websocket_test.go
└── fixtures/
    ├── test_data.sql
    └── mock_responses/
```

**Key Test Scenarios:**

1. **Authentication Flow Tests**
   - User registration workflow
   - Login/logout functionality
   - JWT token validation and refresh
   - Password reset flow
   - Role-based access control

2. **Business Logic Integration Tests**
   - Card lifecycle (create, update, move, delete)
   - Pipeline management workflows
   - Contact and company management
   - Activity tracking and audit trails
   - File upload and management

3. **AI Feature Integration Tests**
   - Lead scoring pipeline
   - Deal prediction workflows
   - Email suggestion generation
   - Sentiment analysis integration
   - Recommendation engine functionality

4. **Real-time Features Tests**
   - SSE connection establishment
   - Event broadcasting
   - Real-time updates across clients
   - Connection handling and reconnection

**Example Integration Test:**
```go
// tests/integration/api/cards_integration_test.go
package api_test

import (
    "bytes"
    "context"
    "encoding/json"
    "net/http"
    "testing"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "github.com/testcontainers/testcontainers-go"
    
    "crm/internal/domain/entities"
    "crm/tests/integration/testutils"
)

type CardsIntegrationTestSuite struct {
    suite.Suite
    testContainer *testutils.TestContainer
    authToken     string
}

func (suite *CardsIntegrationTestSuite) SetupSuite() {
    var err error
    suite.testContainer, err = testutils.SetupTestEnvironment(context.Background())
    suite.Require().NoError(err)
    
    // Create test user and get auth token
    suite.authToken, err = suite.testContainer.CreateTestUser("<EMAIL>", "password")
    suite.Require().NoError(err)
}

func (suite *CardsIntegrationTestSuite) TearDownSuite() {
    suite.testContainer.Cleanup()
}

func (suite *CardsIntegrationTestSuite) TestCreateCard() {
    // Prepare test data
    cardData := map[string]interface{}{
        "title":       "Test Card",
        "description": "Test Description",
        "value":       100000,
        "pipeline_id": suite.testContainer.TestPipelineID,
        "stage_id":    suite.testContainer.TestStageID,
    }
    
    jsonData, _ := json.Marshal(cardData)
    
    // Make request
    req, _ := http.NewRequest("POST", suite.testContainer.BaseURL+"/api/v1/cards", bytes.NewBuffer(jsonData))
    req.Header.Set("Authorization", "Bearer "+suite.authToken)
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := http.DefaultClient.Do(req)
    suite.Require().NoError(err)
    defer resp.Body.Close()
    
    // Validate response
    suite.Equal(http.StatusCreated, resp.StatusCode)
    
    var result map[string]interface{}
    err = json.NewDecoder(resp.Body).Decode(&result)
    suite.Require().NoError(err)
    
    data := result["data"].(map[string]interface{})
    suite.Equal("Test Card", data["title"])
    suite.Equal(float64(100000), data["value"])
    suite.NotEmpty(data["id"])
    
    // Verify database state
    cardID := data["id"].(string)
    card, err := suite.testContainer.DB.GetCardByID(cardID)
    suite.Require().NoError(err)
    suite.Equal("Test Card", card.Title)
}

func (suite *CardsIntegrationTestSuite) TestCardMoveBetweenStages() {
    // Create a test card
    card := suite.createTestCard("Test Card for Move")
    
    // Move card to different stage
    moveData := map[string]interface{}{
        "stage_id": suite.testContainer.TestStageID2,
    }
    jsonData, _ := json.Marshal(moveData)
    
    req, _ := http.NewRequest("PATCH", 
        suite.testContainer.BaseURL+"/api/v1/cards/"+card.ID+"/move", 
        bytes.NewBuffer(jsonData))
    req.Header.Set("Authorization", "Bearer "+suite.authToken)
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := http.DefaultClient.Do(req)
    suite.Require().NoError(err)
    defer resp.Body.Close()
    
    suite.Equal(http.StatusOK, resp.StatusCode)
    
    // Verify card moved in database
    updatedCard, err := suite.testContainer.DB.GetCardByID(card.ID)
    suite.Require().NoError(err)
    suite.Equal(suite.testContainer.TestStageID2, updatedCard.StageID)
    
    // Verify activity was logged
    activities, err := suite.testContainer.DB.GetActivitiesByEntityID(card.ID)
    suite.Require().NoError(err)
    suite.Contains(activities, "card_moved")
}

func TestCardsIntegrationSuite(t *testing.T) {
    suite.Run(t, new(CardsIntegrationTestSuite))
}
```

### 2.2 Database Integration Tests

**Test Categories:**
- Migration scripts validation
- Query performance testing
- Transaction integrity
- Concurrency handling
- Data consistency checks

### 2.3 External Service Integration Tests

**Services to Test:**
- Redis caching layer
- Email service providers (SMTP)
- File storage (MinIO)
- AI/ML service APIs
- Third-party APIs (data enrichment)

---

## 3. End-to-End (E2E) Testing Strategy

### 3.1 Current E2E Setup Enhancement

**Framework:** Playwright (existing) + enhanced configuration

**Enhanced Configuration:**
```javascript
// playwright.config.enhanced.js
const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests/e2e',
  timeout: 60 * 1000,
  expect: { timeout: 10000 },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 2 : 4,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    storageState: 'tests/auth.json'
  },

  projects: [
    // Desktop browsers
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    // Mobile browsers
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
    // Tablet
    {
      name: 'tablet',
      use: { ...devices['iPad Pro'] },
    }
  ],

  webServer: {
    command: 'make dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  }
});
```

### 3.2 Critical User Journeys

**Test Structure:**
```
tests/
├── e2e/
│   ├── auth/
│   │   ├── login.spec.js
│   │   ├── registration.spec.js
│   │   └── password-reset.spec.js
│   ├── pipeline/
│   │   ├── card-management.spec.js
│   │   ├── pipeline-navigation.spec.js
│   │   └── drag-and-drop.spec.js
│   ├── contacts/
│   │   ├── contact-crud.spec.js
│   │   └── contact-search.spec.js
│   ├── companies/
│   │   ├── company-management.spec.js
│   │   └── company-contacts.spec.js
│   ├── ai-features/
│   │   ├── lead-scoring.spec.js
│   │   ├── email-suggestions.spec.js
│   │   └── sentiment-analysis.spec.js
│   ├── mobile/
│   │   ├── mobile-navigation.spec.js
│   │   ├── touch-interactions.spec.js
│   │   └── responsive-layouts.spec.js
│   ├── performance/
│   │   ├── page-load-times.spec.js
│   │   └── large-dataset-handling.spec.js
│   └── accessibility/
│       ├── keyboard-navigation.spec.js
│       ├── screen-reader.spec.js
│       └── color-contrast.spec.js
├── fixtures/
│   ├── test-data.json
│   └── mock-responses/
├── page-objects/
│   ├── LoginPage.js
│   ├── DashboardPage.js
│   ├── PipelinePage.js
│   └── ...
└── utils/
    ├── auth-helper.js
    ├── data-generator.js
    └── test-helpers.js
```

**Priority Test Scenarios:**

1. **Authentication & Authorization (Critical)**
   - User login/logout flow
   - Session persistence
   - Role-based access control
   - Multi-factor authentication (if implemented)

2. **Core CRM Workflows (Critical)**
   - Pipeline management and card operations
   - Contact and company management
   - Search and filtering functionality
   - Data import/export operations

3. **AI Features (High Priority)**
   - Lead scoring display and updates
   - Email suggestion acceptance and editing
   - Sentiment analysis visualization
   - Deal prediction accuracy display

4. **Real-time Features (High Priority)**
   - Live updates across multiple browser tabs
   - Collaborative editing scenarios
   - Notification system functionality

5. **Mobile Experience (Medium Priority)**
   - Touch-based interactions
   - Responsive layout validation
   - Mobile-specific features
   - Offline capability (if implemented)

**Example E2E Test:**
```javascript
// tests/e2e/pipeline/card-management.spec.js
const { test, expect } = require('@playwright/test');
const { LoginPage } = require('../../page-objects/LoginPage');
const { PipelinePage } = require('../../page-objects/PipelinePage');
const { testData } = require('../../fixtures/test-data.json');

test.describe('Card Management', () => {
  let loginPage;
  let pipelinePage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    pipelinePage = new PipelinePage(page);
    
    await loginPage.goto();
    await loginPage.login(testData.users.admin.email, testData.users.admin.password);
    await pipelinePage.goto();
  });

  test('should create a new card with AI lead scoring', async ({ page }) => {
    // Create new card
    await pipelinePage.clickAddCardButton();
    await pipelinePage.fillCardForm({
      title: 'High Value Prospect',
      description: 'Enterprise client with 500+ employees',
      value: 250000,
      company: 'Acme Corporation',
      contact: 'John Smith'
    });
    
    await pipelinePage.submitCardForm();
    
    // Verify card created
    await expect(pipelinePage.getCardByTitle('High Value Prospect')).toBeVisible();
    
    // Wait for AI lead scoring
    await page.waitForTimeout(3000); // Allow time for AI processing
    
    // Verify lead score appears
    const leadScore = await pipelinePage.getLeadScore('High Value Prospect');
    expect(leadScore).toBeGreaterThan(70); // Expecting high score for enterprise client
    
    // Take screenshot for verification
    await page.screenshot({
      path: 'test-results/card-with-lead-score.png',
      fullPage: true
    });
  });

  test('should move card between pipeline stages with real-time updates', async ({ page, context }) => {
    // Open second browser context to test real-time updates
    const secondPage = await context.newPage();
    const secondPipelinePage = new PipelinePage(secondPage);
    await secondPipelinePage.goto();
    
    // Create card in first browser
    await pipelinePage.createQuickCard('Test Real-time Card');
    
    // Verify card appears in second browser
    await expect(secondPipelinePage.getCardByTitle('Test Real-time Card')).toBeVisible();
    
    // Move card to next stage in first browser
    await pipelinePage.moveCardToStage('Test Real-time Card', 'Qualified');
    
    // Verify real-time update in second browser
    await expect(secondPipelinePage.getCardInStage('Test Real-time Card', 'Qualified')).toBeVisible();
    
    await secondPage.close();
  });

  test('should handle bulk card operations efficiently', async ({ page }) => {
    // Create multiple test cards
    const cardTitles = ['Bulk Test 1', 'Bulk Test 2', 'Bulk Test 3'];
    
    for (const title of cardTitles) {
      await pipelinePage.createQuickCard(title);
    }
    
    // Select all created cards
    await pipelinePage.selectMultipleCards(cardTitles);
    
    // Perform bulk operation - move to next stage
    await pipelinePage.bulkMoveCards('Qualified');
    
    // Verify all cards moved
    for (const title of cardTitles) {
      await expect(pipelinePage.getCardInStage(title, 'Qualified')).toBeVisible();
    }
    
    // Measure performance
    const startTime = Date.now();
    await pipelinePage.bulkDeleteCards(cardTitles);
    const endTime = Date.now();
    
    expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
  });
});
```

---

## 4. Performance Testing Strategy

### 4.1 Performance Test Categories

**Framework:** Artillery.io + k6 + Lighthouse CI

**Test Categories:**

1. **Load Testing**
   - Normal load: 100 concurrent users
   - Peak load: 500 concurrent users
   - Stress testing: 1000+ concurrent users

2. **API Performance Testing**
   - Response time benchmarks
   - Throughput testing
   - Database query performance
   - Cache hit ratios

3. **Frontend Performance Testing**
   - Core Web Vitals measurement
   - Bundle size analysis
   - Page load times
   - JavaScript execution time

4. **AI Features Performance**
   - Model inference time
   - Batch processing capabilities
   - Cache effectiveness
   - Concurrent prediction handling

### 4.2 Performance Benchmarks

**API Response Time Targets:**
- Authentication: < 200ms (95th percentile)
- CRUD Operations: < 300ms (95th percentile)
- Search Operations: < 500ms (95th percentile)
- AI Predictions: < 1000ms (95th percentile)
- Bulk Operations: < 2000ms (95th percentile)

**Frontend Performance Targets:**
- Largest Contentful Paint (LCP): < 2.5s
- First Input Delay (FID): < 100ms
- Cumulative Layout Shift (CLS): < 0.1
- Time to Interactive (TTI): < 3.5s

**System Resource Limits:**
- CPU Usage: < 70% under normal load
- Memory Usage: < 80% of available RAM
- Database Connections: < 80% of connection pool
- Cache Memory: < 75% of allocated Redis memory

### 4.3 Performance Test Implementation

**Load Testing Configuration:**
```yaml
# artillery-config.yml
config:
  target: 'http://localhost:8080'
  phases:
    - duration: 300  # 5 minutes
      arrivalRate: 10  # 10 users per second
      name: "Warm up"
    - duration: 600  # 10 minutes
      arrivalRate: 50  # 50 users per second
      name: "Normal load"
    - duration: 300  # 5 minutes
      arrivalRate: 100  # 100 users per second
      name: "Peak load"
  processor: "./test-processors.js"

scenarios:
  - name: "API Workflow"
    weight: 70
    flow:
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "<EMAIL>"
            password: "password"
      - get:
          url: "/api/v1/cards"
          headers:
            Authorization: "Bearer {{ token }}"
      - post:
          url: "/api/v1/cards"
          headers:
            Authorization: "Bearer {{ token }}"
          json:
            title: "Load Test Card {{ $uuid }}"
            value: 50000
  
  - name: "AI Features"
    weight: 30
    flow:
      - get:
          url: "/api/v1/ai/leads/{{ cardId }}/score"
          headers:
            Authorization: "Bearer {{ token }}"
```

**Frontend Performance Testing:**
```javascript
// tests/performance/lighthouse-config.js
module.exports = {
  ci: {
    collect: {
      numberOfRuns: 3,
      url: [
        'http://localhost:3000/auth/login',
        'http://localhost:3000/dashboard',
        'http://localhost:3000/pipeline',
        'http://localhost:3000/contacts',
      ],
    },
    assert: {
      assertions: {
        'categories:performance': ['error', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.8 }],
      },
    },
    upload: {
      target: 'filesystem',
      outputDir: './lighthouse-results',
    },
  },
};
```

---

## 5. Security Testing Strategy

### 5.1 Security Test Categories

**Framework:** OWASP ZAP + Custom Go security tests + npm audit

**Test Areas:**

1. **Authentication & Authorization Testing**
   - JWT token security
   - Session management
   - Role-based access control
   - Password security policies
   - Multi-factor authentication

2. **Input Validation Testing**
   - SQL injection protection
   - XSS prevention
   - CSRF protection
   - File upload security
   - API input sanitization

3. **Data Protection Testing**
   - Encryption at rest and in transit
   - PII data handling
   - GDPR compliance validation
   - Data anonymization
   - Secure data deletion

4. **Infrastructure Security Testing**
   - Container security scanning
   - Dependency vulnerability scanning
   - Network security validation
   - API rate limiting effectiveness

### 5.2 Security Test Implementation

**Automated Security Scanning:**
```yaml
# .github/workflows/security-scan.yml
name: Security Scanning
on: [push, pull_request]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      # Backend security scanning
      - name: Run gosec
        uses: securecodewarrior/github-action-gosec@master
        with:
          args: '-fmt json -out gosec-report.json -stdout -verbose=text ./...'
      
      # Frontend dependency scanning
      - name: Run npm audit
        working-directory: ./frontend
        run: npm audit --audit-level=high
      
      # Container security scanning
      - name: Run Trivy scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'crm-backend:latest'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      # OWASP ZAP scanning
      - name: ZAP Scan
        uses: zaproxy/action-full-scan@v0.4.0
        with:
          target: 'http://localhost:3000'
```

**Custom Security Tests:**
```go
// tests/security/auth_security_test.go
package security_test

import (
    "testing"
    "time"
    "net/http"
    
    "github.com/stretchr/testify/assert"
)

func TestJWTTokenSecurity(t *testing.T) {
    tests := []struct {
        name          string
        token         string
        expectedCode  int
        description   string
    }{
        {
            name:         "expired token should be rejected",
            token:        generateExpiredToken(),
            expectedCode: http.StatusUnauthorized,
            description:  "Expired JWT tokens must be rejected",
        },
        {
            name:         "malformed token should be rejected",
            token:        "invalid.jwt.token",
            expectedCode: http.StatusUnauthorized,
            description:  "Malformed JWT tokens must be rejected",
        },
        {
            name:         "token with invalid signature should be rejected",
            token:        generateTokenWithInvalidSignature(),
            expectedCode: http.StatusUnauthorized,
            description:  "Tokens with invalid signatures must be rejected",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            req := createTestRequest("GET", "/api/v1/protected", nil)
            req.Header.Set("Authorization", "Bearer "+tt.token)
            
            resp := executeRequest(req)
            
            assert.Equal(t, tt.expectedCode, resp.StatusCode, tt.description)
        })
    }
}

func TestSQLInjectionProtection(t *testing.T) {
    injectionAttempts := []string{
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin' /*",
        "1; SELECT * FROM users",
    }

    for _, attempt := range injectionAttempts {
        t.Run("SQL injection attempt: "+attempt, func(t *testing.T) {
            // Test search endpoint with injection attempt
            req := createTestRequest("GET", "/api/v1/cards?search="+attempt, nil)
            addAuthHeader(req, getValidToken())
            
            resp := executeRequest(req)
            
            // Should not return 500 (indicating SQL error)
            assert.NotEqual(t, http.StatusInternalServerError, resp.StatusCode)
            
            // Response should not contain sensitive data
            body := getResponseBody(resp)
            assert.NotContains(t, body, "users")
            assert.NotContains(t, body, "password")
        })
    }
}
```

---

## 6. Accessibility Testing Strategy

### 6.1 Accessibility Standards

**Target Compliance:** WCAG 2.1 AA

**Framework:** axe-core + Pa11y + Manual testing

**Test Categories:**
1. Keyboard navigation testing
2. Screen reader compatibility
3. Color contrast validation
4. Focus management
5. ARIA implementation
6. Form accessibility

### 6.2 Automated Accessibility Testing

**axe-core Integration:**
```javascript
// tests/accessibility/axe-tests.spec.js
const { test, expect } = require('@playwright/test');
const AxeBuilder = require('@axe-core/playwright').default;

test.describe('Accessibility Tests', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/dashboard');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto('/pipeline');
    
    // Test tab navigation through all interactive elements
    const focusableElements = await page.locator('button, input, select, textarea, a[href], [tabindex]').all();
    
    for (let i = 0; i < focusableElements.length; i++) {
      await page.keyboard.press('Tab');
      const focused = await page.locator(':focus').first();
      await expect(focused).toBeVisible();
    }
  });

  test('should have proper ARIA labels', async ({ page }) => {
    await page.goto('/contacts');
    
    // Check for ARIA labels on buttons
    const buttons = await page.locator('button').all();
    for (const button of buttons) {
      const ariaLabel = await button.getAttribute('aria-label');
      const buttonText = await button.textContent();
      
      expect(ariaLabel || buttonText).toBeTruthy();
    }
  });
});
```

**Pa11y Configuration:**
```javascript
// pa11y-config.js
module.exports = {
  standard: 'WCAG2AA',
  ignore: [
    'WCAG2AA.Principle1.Guideline1_4.1_4_3.G18.Fail' // Color contrast issues in third-party components
  ],
  urls: [
    'http://localhost:3000/auth/login',
    'http://localhost:3000/dashboard',
    'http://localhost:3000/pipeline',
    'http://localhost:3000/contacts',
    'http://localhost:3000/companies'
  ],
  actions: [
    'click element button[type="submit"]',
    'wait for element [data-testid="dashboard-content"] to be visible'
  ]
};
```

---

## 7. Mobile Testing Strategy

### 7.1 Mobile Test Coverage

**Devices to Test:**
- iOS: iPhone 12, iPhone 14, iPad Pro
- Android: Pixel 5, Samsung Galaxy S21, Samsung Galaxy Tab
- Various screen sizes: 320px to 1024px width

**Test Categories:**
1. Responsive design validation
2. Touch interaction testing
3. Performance on mobile devices
4. Mobile-specific features
5. Network connectivity scenarios

### 7.2 Mobile Test Implementation

**Responsive Design Tests:**
```javascript
// tests/mobile/responsive-tests.spec.js
const { test, expect } = require('@playwright/test');

const viewports = [
  { name: 'Mobile Small', width: 320, height: 568 },
  { name: 'Mobile Medium', width: 375, height: 667 },
  { name: 'Mobile Large', width: 414, height: 896 },
  { name: 'Tablet', width: 768, height: 1024 },
  { name: 'Desktop Small', width: 1024, height: 768 },
];

viewports.forEach(viewport => {
  test.describe(`Responsive tests - ${viewport.name}`, () => {
    test.beforeEach(async ({ page }) => {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
    });

    test('should display navigation correctly', async ({ page }) => {
      await page.goto('/dashboard');
      
      if (viewport.width < 768) {
        // Mobile navigation should show hamburger menu
        await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
        await expect(page.locator('[data-testid="desktop-sidebar"]')).not.toBeVisible();
      } else {
        // Desktop navigation should show sidebar
        await expect(page.locator('[data-testid="desktop-sidebar"]')).toBeVisible();
        await expect(page.locator('[data-testid="mobile-menu-button"]')).not.toBeVisible();
      }
    });

    test('should handle touch interactions', async ({ page }) => {
      if (viewport.width < 768) {
        await page.goto('/pipeline');
        
        // Test touch-based card interactions
        const card = page.locator('[data-testid="pipeline-card"]').first();
        await expect(card).toBeVisible();
        
        // Test long press for context menu
        await card.hover();
        await page.mouse.down();
        await page.waitForTimeout(500); // Simulate long press
        await page.mouse.up();
        
        await expect(page.locator('[data-testid="card-context-menu"]')).toBeVisible();
      }
    });
  });
});
```

---

## 8. Load Testing Strategy

### 8.1 Load Testing Scenarios

**Test Scenarios:**
1. **Baseline Load:** 50 concurrent users
2. **Normal Load:** 200 concurrent users  
3. **Peak Load:** 500 concurrent users
4. **Stress Test:** 1000+ concurrent users
5. **Spike Test:** Sudden load increases
6. **Soak Test:** Extended duration testing

### 8.2 Load Testing Implementation

**k6 Load Testing Scripts:**
```javascript
// tests/load/api-load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';

// Custom metrics
const authFailures = new Counter('auth_failures');
const apiResponseTime = new Trend('api_response_time');
const successRate = new Rate('success_rate');

export let options = {
  stages: [
    { duration: '2m', target: 50 },   // Ramp-up to 50 users
    { duration: '5m', target: 50 },   // Stay at 50 users
    { duration: '2m', target: 200 },  // Ramp-up to 200 users  
    { duration: '10m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 500 },  // Ramp-up to 500 users
    { duration: '5m', target: 500 },  // Stay at 500 users
    { duration: '3m', target: 0 },    // Ramp-down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must be below 500ms
    http_req_failed: ['rate<0.01'],   // Error rate must be below 1%
    success_rate: ['rate>0.99'],      // Success rate must be above 99%
  },
};

export function setup() {
  // Setup test data
  return {
    baseUrl: __ENV.BASE_URL || 'http://localhost:8080',
    testUsers: generateTestUsers(100)
  };
}

export default function(data) {
  const baseUrl = data.baseUrl;
  const user = data.testUsers[Math.floor(Math.random() * data.testUsers.length)];
  
  // Authentication
  const authResponse = http.post(`${baseUrl}/api/v1/auth/login`, {
    email: user.email,
    password: user.password
  });
  
  const authSuccess = check(authResponse, {
    'auth status is 200': (r) => r.status === 200,
    'auth response time < 200ms': (r) => r.timings.duration < 200,
  });
  
  if (!authSuccess) {
    authFailures.add(1);
    return;
  }
  
  const token = authResponse.json().token;
  const headers = { Authorization: `Bearer ${token}` };
  
  // API workflow simulation
  const scenarios = [
    () => testPipelineOperations(baseUrl, headers),
    () => testContactOperations(baseUrl, headers),
    () => testCompanyOperations(baseUrl, headers),
    () => testAIFeatures(baseUrl, headers),
  ];
  
  const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
  const success = scenario();
  
  successRate.add(success);
  sleep(1);
}

function testPipelineOperations(baseUrl, headers) {
  // Get pipelines
  const pipelinesResponse = http.get(`${baseUrl}/api/v1/pipelines`, { headers });
  
  const success = check(pipelinesResponse, {
    'pipelines status is 200': (r) => r.status === 200,
    'pipelines response time < 300ms': (r) => r.timings.duration < 300,
  });
  
  apiResponseTime.add(pipelinesResponse.timings.duration);
  
  if (success && pipelinesResponse.json().data.length > 0) {
    const pipelineId = pipelinesResponse.json().data[0].id;
    
    // Get pipeline cards
    const cardsResponse = http.get(`${baseUrl}/api/v1/cards?pipeline_id=${pipelineId}`, { headers });
    
    return check(cardsResponse, {
      'cards status is 200': (r) => r.status === 200,
      'cards response time < 400ms': (r) => r.timings.duration < 400,
    });
  }
  
  return success;
}

function testAIFeatures(baseUrl, headers) {
  // Test lead scoring
  const leadScoreResponse = http.get(`${baseUrl}/api/v1/ai/leads/score`, { headers });
  
  return check(leadScoreResponse, {
    'lead score status is 200': (r) => r.status === 200,
    'lead score response time < 1000ms': (r) => r.timings.duration < 1000,
  });
}

export function teardown(data) {
  // Cleanup test data
  console.log('Load test completed');
}
```

---

## 9. AI Features Testing Strategy

### 9.1 AI Testing Categories

**Test Areas:**
1. **Model Performance Testing**
   - Prediction accuracy validation
   - Model drift detection  
   - Performance regression testing
   - A/B testing for model versions

2. **Data Pipeline Testing**
   - Feature engineering validation
   - Data quality assurance
   - Pipeline reliability testing
   - Error handling and recovery

3. **Integration Testing**
   - AI service API testing
   - Real-time prediction testing
   - Cache integration validation
   - Fallback mechanism testing

4. **User Experience Testing**
   - AI result presentation
   - Loading states and feedback
   - Error message clarity
   - Feature adoption metrics

### 9.2 AI Testing Implementation

**Model Performance Tests:**
```python
# tests/ai/model_performance_test.py
import pytest
import pandas as pd
import numpy as np
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from src.models.lead_scoring_model import LeadScoringModel

class TestLeadScoringModel:
    @pytest.fixture
    def model(self):
        return LeadScoringModel.load_from_file('models/lead_scoring_v1.pkl')
    
    @pytest.fixture
    def test_data(self):
        return pd.read_csv('tests/fixtures/lead_scoring_test_data.csv')
    
    def test_model_accuracy_benchmark(self, model, test_data):
        """Test that model meets minimum accuracy requirements"""
        X_test = test_data.drop(['lead_id', 'actual_outcome'], axis=1)
        y_test = test_data['actual_outcome']
        
        predictions = model.predict(X_test)
        accuracy = accuracy_score(y_test, predictions)
        
        assert accuracy >= 0.85, f"Model accuracy {accuracy:.3f} below threshold 0.85"
    
    def test_model_precision_recall(self, model, test_data):
        """Test precision and recall for high-value leads"""
        X_test = test_data.drop(['lead_id', 'actual_outcome'], axis=1)
        y_test = test_data['actual_outcome']
        
        predictions = model.predict(X_test)
        precision = precision_score(y_test, predictions, pos_label='won')
        recall = recall_score(y_test, predictions, pos_label='won')
        f1 = f1_score(y_test, predictions, pos_label='won')
        
        assert precision >= 0.80, f"Precision {precision:.3f} below threshold 0.80"
        assert recall >= 0.75, f"Recall {recall:.3f} below threshold 0.75"
        assert f1 >= 0.77, f"F1-score {f1:.3f} below threshold 0.77"
    
    def test_prediction_confidence(self, model, test_data):
        """Test that confidence scores are properly calibrated"""
        X_test = test_data.drop(['lead_id', 'actual_outcome'], axis=1).head(100)
        
        predictions_proba = model.predict_proba(X_test)
        confidence_scores = np.max(predictions_proba, axis=1)
        
        # Confidence scores should be well distributed
        assert np.mean(confidence_scores) > 0.7, "Average confidence too low"
        assert np.std(confidence_scores) > 0.1, "Confidence scores not well distributed"
    
    def test_feature_importance_stability(self, model):
        """Test that feature importance is stable and interpretable"""
        feature_importance = model.get_feature_importance()
        
        # Top features should be business-relevant
        top_features = feature_importance.nlargest(5).index.tolist()
        expected_important_features = [
            'company_size', 'industry_category', 'engagement_score', 
            'website_visits', 'email_response_rate'
        ]
        
        overlap = len(set(top_features) & set(expected_important_features))
        assert overlap >= 3, f"Only {overlap} expected features in top 5 important features"
    
    def test_edge_case_handling(self, model):
        """Test model behavior with edge cases"""
        # Test with missing values
        edge_case_data = pd.DataFrame({
            'company_size': [None, 0, 1000000],
            'industry_category': ['unknown', '', 'technology'],
            'engagement_score': [-1, 0, 100],
            # ... other features
        })
        
        try:
            predictions = model.predict(edge_case_data)
            assert len(predictions) == len(edge_case_data)
            assert all(pred in ['won', 'lost'] for pred in predictions)
        except Exception as e:
            pytest.fail(f"Model failed on edge cases: {str(e)}")

class TestAIServiceIntegration:
    def test_lead_scoring_api_performance(self):
        """Test AI service API performance benchmarks"""
        import requests
        import time
        
        api_url = "http://localhost:5000/api/v1/ai/lead-score"
        test_lead_data = {
            "lead_id": "test-lead-123",
            "company_size": 500,
            "industry": "technology",
            "engagement_metrics": {
                "website_visits": 15,
                "email_opens": 8,
                "content_downloads": 3
            }
        }
        
        # Test single prediction performance
        start_time = time.time()
        response = requests.post(api_url, json=test_lead_data)
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 0.5, "Single prediction took too long"
        
        result = response.json()
        assert 'score' in result
        assert 'confidence' in result
        assert 0 <= result['score'] <= 100
        assert 0 <= result['confidence'] <= 1
    
    def test_batch_prediction_performance(self):
        """Test batch prediction capabilities"""
        import requests
        
        api_url = "http://localhost:5000/api/v1/ai/lead-score/batch"
        batch_data = {
            "leads": [
                {"lead_id": f"test-{i}", "company_size": 100 + i*10, "industry": "technology"}
                for i in range(100)
            ]
        }
        
        start_time = time.time()
        response = requests.post(api_url, json=batch_data)
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 5.0, "Batch prediction took too long"
        
        results = response.json()
        assert len(results['predictions']) == 100
```

**AI Feature E2E Tests:**
```javascript
// tests/e2e/ai-features/lead-scoring.spec.js
const { test, expect } = require('@playwright/test');

test.describe('AI Lead Scoring Feature', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button:has-text("Sign in")');
    await page.waitForURL('**/dashboard');
  });

  test('should display lead scores for new contacts', async ({ page }) => {
    // Navigate to contacts page
    await page.click('a:has-text("Contacts")');
    
    // Add new contact with high-value characteristics
    await page.click('button:has-text("Add Contact")');
    await page.fill('input[name="name"]', 'John Smith');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="company"]', 'Enterprise Corp');
    await page.fill('input[name="company_size"]', '1000');
    await page.selectOption('select[name="industry"]', 'technology');
    
    await page.click('button:has-text("Save")');
    
    // Wait for AI processing
    await page.waitForSelector('[data-testid="lead-score"]', { timeout: 10000 });
    
    // Verify lead score appears and is reasonable
    const leadScore = await page.textContent('[data-testid="lead-score"]');
    const score = parseInt(leadScore.match(/\d+/)[0]);
    
    expect(score).toBeGreaterThan(70); // Enterprise client should have high score
    
    // Verify score explanation is available
    await page.hover('[data-testid="lead-score"]');
    await expect(page.locator('[data-testid="score-explanation"]')).toBeVisible();
  });

  test('should update lead scores when contact data changes', async ({ page }) => {
    await page.goto('/contacts');
    
    // Find existing contact
    const contactRow = page.locator('[data-testid="contact-row"]').first();
    const initialScore = await contactRow.locator('[data-testid="lead-score"]').textContent();
    
    // Edit contact to improve profile
    await contactRow.locator('button:has-text("Edit")').click();
    await page.fill('input[name="company_size"]', '5000'); // Increase company size
    await page.selectOption('select[name="industry"]', 'enterprise-software');
    await page.click('button:has-text("Save")');
    
    // Wait for score recalculation
    await page.waitForTimeout(3000);
    
    const updatedScore = await contactRow.locator('[data-testid="lead-score"]').textContent();
    
    // Score should have improved
    const initialScoreNum = parseInt(initialScore.match(/\d+/)[0]);
    const updatedScoreNum = parseInt(updatedScore.match(/\d+/)[0]);
    
    expect(updatedScoreNum).toBeGreaterThan(initialScoreNum);
  });

  test('should handle AI service failures gracefully', async ({ page }) => {
    // Mock AI service failure
    await page.route('**/api/v1/ai/lead-score', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'AI service unavailable' })
      });
    });
    
    await page.goto('/contacts');
    await page.click('button:has-text("Add Contact")');
    
    await page.fill('input[name="name"]', 'Test Contact');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button:has-text("Save")');
    
    // Should show fallback indicator instead of score
    await expect(page.locator('[data-testid="score-unavailable"]')).toBeVisible();
    await expect(page.locator('[data-testid="score-unavailable"]')).toHaveText(/Score unavailable/i);
  });
});
```

---

## 10. Real-time Features (SSE) Testing

### 10.1 SSE Testing Strategy

**Test Categories:**
1. Connection establishment and maintenance
2. Event broadcasting and delivery
3. Connection recovery and reconnection
4. Multi-client synchronization
5. Error handling and failover

### 10.2 SSE Test Implementation

**Connection and Event Tests:**
```javascript
// tests/e2e/realtime/sse-functionality.spec.js
const { test, expect } = require('@playwright/test');

test.describe('Server-Sent Events', () => {
  test('should establish SSE connection after login', async ({ page }) => {
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button:has-text("Sign in")');
    
    // Check SSE connection
    const sseConnected = await page.evaluate(() => {
      return new Promise((resolve) => {
        const checkConnection = () => {
          if (window.sseConnection && window.sseConnection.readyState === EventSource.OPEN) {
            resolve(true);
          } else if (Date.now() - startTime > 5000) {
            resolve(false);
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        
        const startTime = Date.now();
        checkConnection();
      });
    });
    
    expect(sseConnected).toBe(true);
  });

  test('should receive real-time updates across multiple clients', async ({ context }) => {
    // Create two browser contexts for two users
    const page1 = await context.newPage();
    const page2 = await context.newPage();
    
    // Login both users
    for (const page of [page1, page2]) {
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button:has-text("Sign in")');
      await page.waitForURL('**/dashboard');
    }
    
    // Navigate both to pipeline
    await page1.goto('/pipeline');
    await page2.goto('/pipeline');
    
    // Listen for updates on page2
    const updateReceived = page2.evaluate(() => {
      return new Promise((resolve) => {
        window.sseConnection.addEventListener('card-created', (event) => {
          resolve(JSON.parse(event.data));
        });
        
        setTimeout(() => resolve(null), 10000); // Timeout after 10s
      });
    });
    
    // Create card on page1
    await page1.click('button:has-text("Add Card")');
    await page1.fill('input[name="title"]', 'Real-time Test Card');
    await page1.fill('input[name="value"]', '50000');
    await page1.click('button:has-text("Save")');
    
    // Wait for update on page2
    const update = await updateReceived;
    expect(update).not.toBeNull();
    expect(update.title).toBe('Real-time Test Card');
    
    // Verify card appears on page2
    await expect(page2.locator('text=Real-time Test Card')).toBeVisible();
    
    await page1.close();
    await page2.close();
  });

  test('should handle connection interruption and recovery', async ({ page }) => {
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button:has-text("Sign in")');
    await page.goto('/pipeline');
    
    // Simulate network interruption
    await page.setOfflineMode(true);
    await page.waitForTimeout(2000);
    
    // Re-enable network
    await page.setOfflineMode(false);
    
    // Check if connection recovers
    const connectionRecovered = await page.evaluate(() => {
      return new Promise((resolve) => {
        let attempts = 0;
        const checkConnection = () => {
          attempts++;
          if (window.sseConnection && window.sseConnection.readyState === EventSource.OPEN) {
            resolve(true);
          } else if (attempts > 30) { // 3 seconds with 100ms intervals
            resolve(false);
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        
        checkConnection();
      });
    });
    
    expect(connectionRecovered).toBe(true);
  });
});
```

---

## 11. Test Data Management Strategy

### 11.1 Test Data Categories

1. **Static Test Data**
   - Reference data (countries, industries)
   - User roles and permissions
   - System configuration data

2. **Generated Test Data**
   - Synthetic user accounts
   - Generated business entities
   - Performance testing datasets

3. **Anonymized Production Data**
   - Sanitized customer data
   - Historical transaction patterns
   - Real usage scenarios

### 11.2 Test Data Implementation

**Data Generation Framework:**
```go
// tests/testdata/generators.go
package testdata

import (
    "fmt"
    "math/rand"
    "time"
    
    "github.com/google/uuid"
    "crm/internal/domain/entities"
)

type DataGenerator struct {
    rand *rand.Rand
}

func NewDataGenerator() *DataGenerator {
    return &DataGenerator{
        rand: rand.New(rand.NewSource(time.Now().UnixNano())),
    }
}

func (g *DataGenerator) GenerateUser(role string) *entities.User {
    return &entities.User{
        ID:       uuid.New(),
        Name:     g.randomName(),
        Email:    g.randomEmail(),
        Role:     role,
        Active:   true,
        CreatedAt: time.Now().AddDate(0, -g.rand.Intn(12), -g.rand.Intn(30)),
    }
}

func (g *DataGenerator) GenerateCompany() *entities.Company {
    sizes := []int{10, 50, 100, 500, 1000, 5000}
    industries := []string{"technology", "finance", "healthcare", "manufacturing", "retail"}
    
    return &entities.Company{
        ID:          uuid.New(),
        Name:        g.randomCompanyName(),
        Industry:    industries[g.rand.Intn(len(industries))],
        Size:        sizes[g.rand.Intn(len(sizes))],
        Website:     g.randomWebsite(),
        Revenue:     float64(g.rand.Intn(10000000)),
        CreatedAt:   time.Now().AddDate(0, -g.rand.Intn(6), -g.rand.Intn(30)),
    }
}

func (g *DataGenerator) GenerateCard(pipelineID, stageID uuid.UUID) *entities.Card {
    values := []float64{5000, 25000, 50000, 100000, 250000, 500000}
    
    return &entities.Card{
        ID:          uuid.New(),
        Title:       g.randomDealTitle(),
        Description: g.randomDescription(),
        Value:       values[g.rand.Intn(len(values))],
        PipelineID:  pipelineID,
        StageID:     stageID,
        Priority:    g.randomPriority(),
        CreatedAt:   time.Now().AddDate(0, 0, -g.rand.Intn(60)),
    }
}

// Generate test dataset for AI models
func (g *DataGenerator) GenerateLeadScoringDataset(size int) []entities.LeadScoringData {
    dataset := make([]entities.LeadScoringData, size)
    
    for i := 0; i < size; i++ {
        dataset[i] = entities.LeadScoringData{
            LeadID:             uuid.New().String(),
            CompanySize:        g.rand.Intn(10000),
            Industry:           g.randomIndustry(),
            WebsiteVisits:      g.rand.Intn(50),
            EmailOpens:         g.rand.Intn(20),
            ContentDownloads:   g.rand.Intn(10),
            EngagementScore:    g.rand.Float64() * 100,
            ActualOutcome:      g.randomOutcome(),
        }
    }
    
    return dataset
}
```

**Test Data Seeding:**
```go
// tests/testdata/seeder.go
package testdata

import (
    "context"
    "database/sql"
    
    "crm/internal/infrastructure/database"
)

type TestSeeder struct {
    db        *sql.DB
    generator *DataGenerator
}

func NewTestSeeder(db *sql.DB) *TestSeeder {
    return &TestSeeder{
        db:        db,
        generator: NewDataGenerator(),
    }
}

func (s *TestSeeder) SeedTestData(ctx context.Context) error {
    tx, err := s.db.BeginTx(ctx, nil)
    if err != nil {
        return err
    }
    defer tx.Rollback()
    
    // Seed users
    users := []*entities.User{
        s.generator.GenerateUser("admin"),
        s.generator.GenerateUser("manager"),
        s.generator.GenerateUser("sales_rep"),
    }
    
    for _, user := range users {
        if err := s.insertUser(tx, user); err != nil {
            return err
        }
    }
    
    // Seed companies
    for i := 0; i < 50; i++ {
        company := s.generator.GenerateCompany()
        if err := s.insertCompany(tx, company); err != nil {
            return err
        }
    }
    
    // Seed pipeline and cards
    pipeline := s.generator.GeneratePipeline()
    if err := s.insertPipeline(tx, pipeline); err != nil {
        return err
    }
    
    for i := 0; i < 100; i++ {
        card := s.generator.GenerateCard(pipeline.ID, pipeline.Stages[0].ID)
        if err := s.insertCard(tx, card); err != nil {
            return err
        }
    }
    
    return tx.Commit()
}

func (s *TestSeeder) CleanupTestData(ctx context.Context) error {
    tables := []string{"cards", "stages", "pipelines", "companies", "users"}
    
    for _, table := range tables {
        _, err := s.db.ExecContext(ctx, fmt.Sprintf("DELETE FROM %s WHERE created_at > NOW() - INTERVAL '1 day'", table))
        if err != nil {
            return err
        }
    }
    
    return nil
}
```

---

## 12. Automated vs Manual Testing Strategy

### 12.1 Automation Strategy (80% of total effort)

**High Priority for Automation:**
1. **Regression Testing** - All existing functionality
2. **API Testing** - All endpoints and data validation
3. **Performance Testing** - Load, stress, and benchmark tests
4. **Security Testing** - Vulnerability scans and penetration testing
5. **AI Model Validation** - Prediction accuracy and performance
6. **Cross-browser Testing** - Major browsers and versions

**Automation Framework:**
- Backend: Go testing package + Testify + Gomock
- Frontend: Jest + React Testing Library + Playwright
- AI/ML: pytest + MLflow + Great Expectations
- Performance: k6 + Artillery + Lighthouse CI
- Security: OWASP ZAP + Snyk + Gosec

### 12.2 Manual Testing Strategy (20% of total effort)

**High Priority for Manual Testing:**
1. **User Experience Testing** - Workflow usability and intuition
2. **Exploratory Testing** - Edge cases and creative scenarios  
3. **Visual Design Testing** - Layout, colors, animations
4. **Accessibility Testing** - Screen reader and keyboard navigation
5. **Mobile Device Testing** - Real device testing
6. **Business Logic Validation** - Complex scenarios requiring human judgment

**Manual Test Categories:**

1. **Usability Testing Sessions**
   - User journey mapping
   - Task completion analysis
   - User feedback collection
   - Pain point identification

2. **Exploratory Testing**
   - Unscripted testing scenarios
   - Edge case discovery
   - Integration point validation
   - Error handling verification

3. **Visual and UX Testing**
   - Design consistency validation
   - Animation and transition smoothness
   - Responsive design on real devices
   - Cross-browser visual comparison

---

## 13. CI/CD Integration Strategy

### 13.1 Testing Pipeline Architecture

**Pipeline Stages:**
```yaml
# .github/workflows/test-pipeline.yml
name: Comprehensive Testing Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  # Stage 1: Static Analysis and Linting
  static-analysis:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      # Go static analysis
      - name: Setup Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.21
      
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v3
        with:
          version: latest
      
      # Frontend static analysis
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Install frontend dependencies
        working-directory: frontend
        run: npm ci
      
      - name: ESLint
        working-directory: frontend
        run: npm run lint
      
      - name: TypeScript check
        working-directory: frontend
        run: npm run type-check

  # Stage 2: Unit Tests
  unit-tests:
    runs-on: ubuntu-latest
    needs: static-analysis
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: crm_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      # Backend unit tests
      - name: Setup Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.21
      
      - name: Run Go unit tests
        run: |
          go test -v -race -coverprofile=coverage.out ./...
          go tool cover -html=coverage.out -o coverage.html
        env:
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: crm_test
          DB_USER: postgres
          DB_PASSWORD: testpassword
          REDIS_HOST: localhost
          REDIS_PORT: 6379
      
      - name: Upload Go coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage.out
          flags: backend
      
      # Frontend unit tests
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Install frontend dependencies
        working-directory: frontend
        run: npm ci
      
      - name: Run frontend unit tests
        working-directory: frontend
        run: npm test -- --coverage --watchAll=false
      
      - name: Upload frontend coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./frontend/coverage/lcov.info
          flags: frontend

  # Stage 3: Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: crm_test
      redis:
        image: redis:7
      minio:
        image: minio/minio:latest
        command: server /data
        env:
          MINIO_ROOT_USER: minioadmin
          MINIO_ROOT_PASSWORD: minioadmin
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.21
      
      - name: Run integration tests
        run: go test -v -tags=integration ./tests/integration/...
        env:
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: crm_test
          DB_USER: postgres
          DB_PASSWORD: testpassword

  # Stage 4: Security Testing
  security-tests:
    runs-on: ubuntu-latest
    needs: static-analysis
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Gosec Security Scanner
        uses: securecodewarrior/github-action-gosec@master
        with:
          args: '-fmt sarif -out gosec.sarif -stdout -verbose=text ./...'
      
      - name: Upload Gosec results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: gosec.sarif
      
      - name: Run npm audit
        working-directory: frontend
        run: npm audit --audit-level=high

  # Stage 5: Build and E2E Tests
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Build application
        run: docker-compose -f docker-compose.test.yml build
      
      - name: Start services
        run: docker-compose -f docker-compose.test.yml up -d
      
      - name: Wait for services
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:3000/health; do sleep 2; done'
          timeout 60 bash -c 'until curl -f http://localhost:8080/health; do sleep 2; done'
      
      - name: Setup Node.js for Playwright
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install Playwright
        run: |
          npm install @playwright/test
          npx playwright install chromium firefox webkit
      
      - name: Run E2E tests
        run: npx playwright test --reporter=html
      
      - name: Upload E2E results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/

  # Stage 6: Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    needs: e2e-tests
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup k6
        run: |
          wget https://github.com/grafana/k6/releases/latest/download/k6-linux-amd64.tar.gz
          tar -xzf k6-linux-amd64.tar.gz
          sudo mv k6-*/k6 /usr/local/bin/
      
      - name: Start services for performance testing
        run: docker-compose -f docker-compose.test.yml up -d
      
      - name: Run performance tests
        run: k6 run tests/performance/load-test.js
      
      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-results.json

  # Stage 7: AI Model Validation
  ai-model-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    if: contains(github.event.head_commit.message, '[ai]') || github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install Python dependencies
        run: |
          pip install -r ai-services/requirements-test.txt
      
      - name: Run AI model tests
        run: |
          pytest ai-services/tests/ -v --cov=ai_services --cov-report=xml
      
      - name: Upload AI coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage.xml
          flags: ai-models

  # Stage 8: Deployment to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [e2e-tests, security-tests, performance-tests]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Deployment commands here
      
      - name: Run smoke tests on staging
        run: |
          npx playwright test tests/smoke/ --config=playwright.staging.config.js
```

### 13.2 Test Result Reporting

**Reporting Tools:**
1. **Code Coverage:** Codecov integration
2. **Test Results:** GitHub Actions summaries
3. **Performance:** k6 Cloud dashboards  
4. **Security:** GitHub Security tab
5. **AI Models:** MLflow tracking

**Quality Gates:**
- Unit test coverage > 80%
- Integration test coverage > 70%
- E2E test success rate > 95%
- Security scan - no high/critical issues
- Performance benchmarks met
- No failing smoke tests

---

## 14. Risk-Based Testing Approach

### 14.1 Risk Assessment Matrix

**Risk Categories:**
1. **Business Critical (High Impact, High Likelihood)**
   - User authentication and authorization
   - Data loss or corruption scenarios
   - Payment processing (if applicable)
   - Core CRM workflows (pipeline management)

2. **High Impact, Medium Likelihood**
   - Performance degradation under load
   - AI model prediction failures  
   - Real-time update failures
   - Data import/export issues

3. **Medium Impact, High Likelihood**
   - UI/UX inconsistencies
   - Mobile responsiveness issues
   - Third-party integration failures
   - Search functionality problems

4. **Low Impact, Low Likelihood**
   - Edge case scenarios
   - Rarely used features
   - Non-critical UI animations
   - Optional integrations

### 14.2 Risk-Based Test Prioritization

**Priority 1 (Critical - 40% of testing effort):**
- Authentication and security flows
- Core business logic (cards, pipelines, contacts)
- Data integrity and CRUD operations
- AI model accuracy and availability
- Performance under normal load

**Priority 2 (High - 35% of testing effort):**
- Real-time features and SSE
- Search and filtering functionality  
- Mobile responsiveness and touch interactions
- Integration with external services
- Error handling and recovery scenarios

**Priority 3 (Medium - 20% of testing effort):**
- Advanced UI interactions and animations
- Accessibility compliance
- Cross-browser compatibility
- Performance optimization features
- Advanced reporting features

**Priority 4 (Low - 5% of testing effort):**
- Edge cases and corner scenarios
- Rarely used administrative features
- Optional integrations
- Advanced customization options

### 14.3 Risk Mitigation Strategies

**High-Risk Area Mitigations:**
1. **Authentication Security**
   - Implement comprehensive security testing suite
   - Regular penetration testing
   - Multi-factor authentication testing
   - Session management validation

2. **Data Integrity**
   - Implement database transaction testing
   - Regular backup and recovery testing
   - Data validation at all input points
   - Audit trail verification

3. **AI Model Reliability**
   - Implement model performance monitoring
   - Fallback mechanisms testing
   - A/B testing for model versions
   - Continuous model validation

---

## 15. Test Environment Strategy

### 15.1 Environment Configuration

**Environment Tiers:**
1. **Development** - Individual developer environments
2. **Testing** - Automated test execution environment
3. **Staging** - Production-like environment for final validation
4. **Production** - Live environment (monitoring only)

**Infrastructure as Code:**
```yaml
# docker-compose.test.yml
version: '3.8'

services:
  crm-backend-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - NODE_ENV=test
      - DB_HOST=postgres-test
      - REDIS_HOST=redis-test
      - AI_SERVICE_URL=http://ai-service-test:5000
    depends_on:
      - postgres-test
      - redis-test
      - ai-service-test
    ports:
      - "8080:8080"
  
  crm-frontend-test:
    build:
      context: frontend
      dockerfile: Dockerfile.test
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8080
    ports:
      - "3000:3000"
    depends_on:
      - crm-backend-test
  
  postgres-test:
    image: postgres:15
    environment:
      - POSTGRES_DB=crm_test
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=testpassword
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
  
  redis-test:
    image: redis:7
    ports:
      - "6379:6379"
  
  ai-service-test:
    build:
      context: ai-services
      dockerfile: Dockerfile.test
    environment:
      - MODEL_PATH=/app/models/test
    ports:
      - "5000:5000"
    volumes:
      - ./ai-services/models:/app/models

volumes:
  postgres_test_data:
```

### 15.2 Test Data Isolation

**Database Strategy:**
- Separate test database per test suite
- Automatic cleanup after test completion
- Isolated transaction scopes
- Parallel test execution support

**File Storage:**
- Separate MinIO buckets for testing
- Automatic file cleanup
- Mock file storage for unit tests

---

## 16. Conclusion and Implementation Timeline

### 16.1 Implementation Phases

**Phase 1: Foundation (Weeks 1-2)**
- Set up testing infrastructure and CI/CD pipeline
- Implement core unit testing framework
- Establish test data management system
- Configure basic security and performance testing

**Phase 2: Core Testing (Weeks 3-4)**  
- Implement comprehensive unit tests for backend and frontend
- Develop integration testing suite
- Create E2E testing framework with critical user journeys
- Implement AI model validation testing

**Phase 3: Advanced Testing (Weeks 5-6)**
- Implement performance and load testing
- Develop comprehensive security testing suite  
- Create accessibility and mobile testing framework
- Implement real-time feature testing

**Phase 4: Optimization (Weeks 7-8)**
- Optimize test execution performance
- Implement comprehensive reporting and monitoring
- Create manual testing procedures and documentation
- Conduct training and knowledge transfer

### 16.2 Success Metrics

**Test Coverage Targets:**
- Backend Unit Tests: 90%
- Frontend Unit Tests: 85%
- Integration Tests: 80%
- E2E Critical Paths: 100%

**Quality Metrics:**
- Test Execution Time: <15 minutes for full suite
- Test Flakiness Rate: <2%
- Bug Detection Rate: 95% in pre-production
- Performance Benchmark Compliance: 100%

**Business Impact:**
- Reduced production incidents: 60% decrease
- Faster development velocity: 40% improvement
- Improved user satisfaction: 25% increase in NPS
- Reduced manual testing effort: 70% reduction

### 16.3 Maintenance and Evolution

**Continuous Improvement Process:**
1. Weekly test metrics review
2. Monthly test strategy assessment
3. Quarterly framework updates
4. Bi-annual comprehensive review

**Test Automation Evolution:**
- Machine learning for test case optimization
- Automated test generation from user behavior
- Predictive test execution based on code changes
- Self-healing test automation

This comprehensive testing strategy provides a solid foundation for ensuring the quality, performance, and reliability of the CRM system while supporting rapid development and deployment cycles. The risk-based approach ensures that testing efforts are focused on the most critical areas, while the comprehensive automation strategy provides fast feedback and high confidence in releases.