# CRM System Brand Style Guide

## 1. Brand Personality & Voice

### Brand Personality
**Professional Intelligence** - Our CRM embodies sophisticated professionalism with accessible intelligence. We bridge the gap between enterprise-grade functionality and human-centered design.

**Core Attributes:**
- **Trustworthy**: Reliable data handling and consistent interactions
- **Intelligent**: Smart workflows and predictive insights
- **Efficient**: Streamlined processes that save time
- **Inclusive**: Accessible to users of all technical levels
- **Progressive**: Forward-thinking without being overwhelming

### Brand Voice Guidelines

**Tone Attributes:**
- Professional yet approachable
- Clear and direct
- Confident without arrogance
- Helpful and supportive
- Consistent across all touchpoints

**Writing Style:**
- Use active voice for clarity
- Write in present tense
- Keep sentences concise (under 20 words)
- Use inclusive, gender-neutral language
- Avoid jargon unless necessary for accuracy

**Do's:**
- "Update contact information" (direct action)
- "Your pipeline shows 12 active deals" (personal ownership)
- "Complete" instead of "finish"
- "Choose" instead of "select"

**Don'ts:**
- "Leverage synergies" (corporate speak)
- "Please click here" (redundant)
- "Obviously" or "Simply" (assumptions)
- "Guys" or gendered terms

## 2. Color System

### Primary Brand Palette

```css
/* Brand Foundation */
--brand-primary: 221.2 83.2% 53.3%;    /* Blue #3B82F6 */
--brand-secondary: 142.1 76.2% 36.3%;  /* Green #059669 */
--brand-accent: 262.1 83.3% 57.8%;     /* Purple #8B5CF6 */

/* Extended Brand Colors */
--brand-success: 142.1 70.6% 45.3%;    /* Success Green #10B981 */
--brand-warning: 32.2 94.6% 43.7%;     /* Warning Amber #F59E0B */
--brand-error: 0 84.2% 60.2%;          /* Error Red #EF4444 */
--brand-info: 199.89 89.47% 49.41%;    /* Info Blue #0EA5E9 */
```

### Data State Colors

```css
/* Pipeline & Deal States */
--status-new: 142.1 76.2% 36.3%;       /* Fresh leads - Green */
--status-qualified: 32.2 94.6% 43.7%;  /* Qualified - Amber */
--status-proposal: 262.1 83.3% 57.8%;  /* Proposal - Purple */
--status-negotiation: 25.95 95.05% 53.92%; /* Negotiation - Orange */
--status-closed-won: 142.1 70.6% 45.3%; /* Won - Success Green */
--status-closed-lost: 0 84.2% 60.2%;   /* Lost - Error Red */

/* Priority Levels */
--priority-critical: 0 84.2% 60.2%;    /* Red */
--priority-high: 32.2 94.6% 43.7%;     /* Amber */
--priority-medium: 199.89 89.47% 49.41%; /* Blue */
--priority-low: 142.1 76.2% 36.3%;     /* Green */

/* Activity States */
--activity-overdue: 0 84.2% 60.2%;     /* Red */
--activity-due-today: 32.2 94.6% 43.7%; /* Amber */
--activity-scheduled: 199.89 89.47% 49.41%; /* Blue */
--activity-completed: 142.1 70.6% 45.3%; /* Green */
```

### Neutral System

```css
/* Light Theme Neutrals */
--neutral-50: 0 0% 98%;
--neutral-100: 0 0% 96%;
--neutral-200: 0 0% 90%;
--neutral-300: 0 0% 83%;
--neutral-400: 0 0% 64%;
--neutral-500: 0 0% 45%;
--neutral-600: 0 0% 32%;
--neutral-700: 0 0% 25%;
--neutral-800: 0 0% 15%;
--neutral-900: 0 0% 9%;

/* Dark Theme Neutrals */
--neutral-dark-50: 222.2 84% 4.9%;
--neutral-dark-100: 217.2 32.6% 17.5%;
--neutral-dark-200: 215 20.2% 65.1%;
--neutral-dark-300: 210 40% 98%;
```

### Semantic Color Usage

| Use Case | Light | Dark | Purpose |
|----------|-------|------|---------|
| Primary Actions | Blue | Lighter Blue | CTAs, links, focus states |
| Success States | Green | Brighter Green | Confirmations, positive metrics |
| Warning States | Amber | Warmer Amber | Cautions, pending items |
| Error States | Red | Softer Red | Errors, failures, deletions |
| Information | Light Blue | Cyan | Tips, additional context |

## 3. Typography System

### Font Stack
```css
/* Primary Font - System Stack */
--font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;

/* Monospace for Data */
--font-family-mono: 'SF Mono', Monaco, 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
```

### Type Scale for Data Interfaces

```css
/* Display Scale - Marketing/Hero only */
--text-display-2xl: 4.5rem; /* 72px - Hero sections */
--text-display-xl: 3.75rem; /* 60px - Page heroes */
--text-display-lg: 3rem;    /* 48px - Section heroes */

/* Heading Scale - Interface Structure */
--text-h1: 2.25rem;  /* 36px - Page titles */
--text-h2: 1.875rem; /* 30px - Section headers */
--text-h3: 1.5rem;   /* 24px - Card headers */
--text-h4: 1.25rem;  /* 20px - Subsection headers */
--text-h5: 1.125rem; /* 18px - List headers */
--text-h6: 1rem;     /* 16px - Small headers */

/* Body Scale - Content & Data */
--text-lg: 1.125rem; /* 18px - Important body text */
--text-base: 1rem;   /* 16px - Default body text */
--text-sm: 0.875rem; /* 14px - Secondary text */
--text-xs: 0.75rem;  /* 12px - Caption, metadata */
--text-2xs: 0.6875rem; /* 11px - Micro text (use sparingly) */

/* Data Display Scale */
--text-data-xl: 2rem;    /* 32px - Key metrics */
--text-data-lg: 1.5rem;  /* 24px - Dashboard stats */
--text-data-base: 1rem;  /* 16px - Table data */
--text-data-sm: 0.875rem; /* 14px - Dense tables */
```

### Font Weights

```css
--font-thin: 100;     /* Avoid in UI */
--font-light: 300;    /* Large display text only */
--font-normal: 400;   /* Body text, most UI */
--font-medium: 500;   /* UI elements, emphasis */
--font-semibold: 600; /* Headings, important UI */
--font-bold: 700;     /* Strong emphasis only */
--font-extrabold: 800; /* Avoid in interface */
```

### Typography Usage Guidelines

| Element | Size | Weight | Usage |
|---------|------|--------|-------|
| Page Titles | H1 (36px) | Semibold | Dashboard, main page headers |
| Card Headers | H3 (24px) | Semibold | Pipeline stages, contact cards |
| Table Headers | SM (14px) | Medium | Column headers |
| Body Text | Base (16px) | Normal | Descriptions, content |
| Metadata | XS (12px) | Normal | Timestamps, secondary info |
| Key Metrics | Data XL (32px) | Semibold | Revenue, conversion rates |

## 4. Icon System

### Icon Philosophy
Icons serve as visual shortcuts to enhance comprehension and reduce cognitive load. Our icon system prioritizes clarity, consistency, and universal recognition.

### Icon Library: Lucide React

**Primary Reasons:**
- Consistent stroke width (1.5px)
- Professional appearance
- Comprehensive CRM-relevant icons
- React component integration
- Excellent accessibility support

### Icon Sizing Scale

```css
--icon-2xs: 12px; /* Inline with small text */
--icon-xs: 14px;  /* Inline with body text */
--icon-sm: 16px;  /* Default UI icons */
--icon-base: 20px; /* Navigation, buttons */
--icon-lg: 24px;  /* Page headers, prominent actions */
--icon-xl: 32px;  /* Dashboard metrics, empty states */
--icon-2xl: 48px; /* Feature illustrations */
```

### CRM-Specific Icon Mapping

```typescript
// Core Entities
const entityIcons = {
  contact: 'User',
  company: 'Building2',
  deal: 'HandCoins',
  task: 'CheckSquare',
  email: 'Mail',
  call: 'Phone',
  meeting: 'Calendar',
  note: 'FileText',
  document: 'File',
  pipeline: 'GitBranch'
};

// Actions
const actionIcons = {
  add: 'Plus',
  edit: 'Pencil',
  delete: 'Trash2',
  save: 'Check',
  cancel: 'X',
  search: 'Search',
  filter: 'Filter',
  sort: 'ArrowUpDown',
  expand: 'ChevronDown',
  collapse: 'ChevronUp',
  view: 'Eye',
  copy: 'Copy',
  move: 'Move'
};

// Status Indicators
const statusIcons = {
  success: 'CheckCircle',
  warning: 'AlertTriangle',
  error: 'AlertCircle',
  info: 'Info',
  pending: 'Clock',
  completed: 'CheckCircle2'
};
```

### Icon Color System

```css
/* Default States */
--icon-primary: hsl(var(--primary));
--icon-secondary: hsl(var(--muted-foreground));
--icon-muted: hsl(var(--muted-foreground) / 0.5);

/* Interactive States */
--icon-hover: hsl(var(--primary) / 0.8);
--icon-active: hsl(var(--primary));
--icon-disabled: hsl(var(--muted-foreground) / 0.3);

/* Status Colors */
--icon-success: hsl(var(--success));
--icon-warning: hsl(var(--warning));
--icon-error: hsl(var(--destructive));
--icon-info: hsl(var(--info));
```

## 5. Component Naming Conventions

### File Structure
```
/components
  /ui              # shadcn/ui base components
    /button.tsx
    /input.tsx
    /card.tsx
  /forms           # Form-specific components
    /contact-form.tsx
    /deal-form.tsx
  /data-display    # Tables, lists, metrics
    /data-table.tsx
    /metric-card.tsx
    /pipeline-board.tsx
  /navigation      # Nav components
    /sidebar.tsx
    /breadcrumbs.tsx
  /layout          # Layout components
    /page-header.tsx
    /content-container.tsx
```

### Component Naming Patterns

```typescript
// Base Components (shadcn/ui style)
Button, Input, Card, Dialog, Sheet, Tabs

// Compound Components
DataTable, MetricCard, ContactForm, DealCard

// Layout Components
PageHeader, ContentContainer, Sidebar, Dashboard

// Feature Components
PipelineBoard, ContactList, ActivityFeed, ReportChart

// Modifier Patterns
ContactFormDialog, DealCardCompact, MetricCardLarge
```

### Props Naming Conventions

```typescript
// Size variants
size: 'sm' | 'md' | 'lg' | 'xl'

// Visual variants
variant: 'default' | 'secondary' | 'outline' | 'ghost' | 'destructive'

// State props
isLoading: boolean
isSelected: boolean
isDisabled: boolean
isOpen: boolean

// Handler props
onSelect: (item: T) => void
onUpdate: (data: T) => void
onDelete: (id: string) => void
```

## 6. Interaction Patterns & Animations

### Animation Principles

1. **Purposeful**: Every animation serves user understanding
2. **Subtle**: Enhance, don't distract from content
3. **Fast**: Complete within 200-300ms for UI feedback
4. **Natural**: Follow real-world physics principles

### Micro-Interactions

```css
/* Button Interactions */
.button-primary {
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.button-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.15);
}

.button-primary:active {
  transform: translateY(0);
  transition-duration: 75ms;
}

/* Card Hover Effects */
.card-interactive {
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px hsl(var(--foreground) / 0.1);
}

/* Loading States */
@keyframes pulse-subtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.loading-skeleton {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--muted) / 0.8) 50%,
    hsl(var(--muted)) 100%
  );
}
```

### State Transitions

```typescript
// Page transitions
const pageTransition = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.3, ease: [0.4, 0, 0.2, 1] }
};

// Modal animations
const modalAnimation = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
  transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] }
};

// Drag and drop feedback
const dragFeedback = {
  whileDrag: {
    scale: 1.05,
    rotate: 3,
    zIndex: 10,
    boxShadow: "0 10px 30px rgba(0,0,0,0.15)"
  }
};
```

### Focus Management

```css
/* Enhanced focus visibility */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: calc(var(--radius) - 2px);
}

/* Keyboard navigation indicators */
.kbd-focus {
  position: relative;
}

.kbd-focus::after {
  content: '';
  position: absolute;
  inset: -2px;
  border: 2px solid hsl(var(--primary));
  border-radius: var(--radius);
  opacity: 0;
  transition: opacity 150ms ease;
}

.kbd-focus:focus-visible::after {
  opacity: 1;
}
```

## 7. Data Visualization Standards

### Chart Color Palette

```css
/* Primary Chart Colors */
--chart-1: 221.2 83.2% 53.3%;  /* Blue - Primary metrics */
--chart-2: 142.1 76.2% 36.3%;  /* Green - Success metrics */
--chart-3: 32.2 94.6% 43.7%;   /* Amber - Warning metrics */
--chart-4: 262.1 83.3% 57.8%;  /* Purple - Secondary metrics */
--chart-5: 199.89 89.47% 49.41%; /* Light Blue - Info metrics */
--chart-6: 25.95 95.05% 53.92%; /* Orange - Conversion metrics */

/* Extended Palette for Complex Charts */
--chart-7: 340 82% 52%;   /* Pink */
--chart-8: 291 64% 42%;   /* Indigo */
--chart-9: 173 58% 39%;   /* Teal */
--chart-10: 43 74% 66%;   /* Yellow */
```

### Chart Guidelines

**Bar Charts:**
- Use for comparing categories
- Maximum 12 categories for readability
- Sort by value when possible
- Include value labels for precision

**Line Charts:**
- Use for trends over time
- Maximum 5 lines for clarity
- Use different line styles for accessibility
- Always include data points

**Pie Charts:**
- Use sparingly (bar charts often better)
- Maximum 6 segments
- Start at 12 o'clock position
- Include percentages and values

**Dashboard Metrics:**
```css
.metric-positive { color: hsl(var(--chart-2)); }
.metric-negative { color: hsl(var(--chart-3)); }
.metric-neutral { color: hsl(var(--foreground)); }
```

### Data Table Standards

```css
/* Table Styling */
.data-table {
  --table-header-bg: hsl(var(--muted) / 0.5);
  --table-border: hsl(var(--border));
  --table-row-hover: hsl(var(--muted) / 0.1);
  --table-selected: hsl(var(--primary) / 0.1);
}

/* Density Options */
.table-compact { --table-padding: 8px 12px; }
.table-normal { --table-padding: 12px 16px; }
.table-comfortable { --table-padding: 16px 20px; }
```

## 8. Accessibility Guidelines

### WCAG 2.1 AA Compliance

**Color Contrast Requirements:**
- Normal text: 4.5:1 minimum contrast ratio
- Large text (18px+): 3:1 minimum contrast ratio
- UI elements: 3:1 minimum contrast ratio

**Keyboard Navigation:**
- All interactive elements must be keyboard accessible
- Visible focus indicators required
- Logical tab order maintained
- Skip links for main content areas

**Screen Reader Support:**
```typescript
// Proper ARIA labeling
<button 
  aria-label="Delete contact John Smith"
  aria-describedby="delete-help"
>
  <TrashIcon />
</button>

// Status announcements
<div 
  aria-live="polite" 
  aria-atomic="true"
  className="sr-only"
>
  {statusMessage}
</div>

// Form associations
<label htmlFor="contact-name">
  Contact Name <span className="text-destructive">*</span>
</label>
<input 
  id="contact-name"
  aria-required="true"
  aria-describedby="name-error"
/>
```

### Inclusive Design Practices

**Color Independence:**
- Never rely solely on color to convey information
- Use icons, text, or patterns as additional indicators
- Test with color blindness simulators

**Motor Accessibility:**
- Minimum 44x44px touch targets
- Adequate spacing between interactive elements
- Support for voice control and switch navigation

**Cognitive Accessibility:**
- Clear, simple language
- Consistent navigation patterns
- Error prevention and clear error messages
- Progressive disclosure for complex features

## 9. Responsive Design Principles

### Breakpoint System

```css
/* Tailwind-compatible breakpoints */
--breakpoint-sm: 640px;   /* Small tablets, large phones */
--breakpoint-md: 768px;   /* Tablets */
--breakpoint-lg: 1024px;  /* Small laptops */
--breakpoint-xl: 1280px;  /* Desktops */
--breakpoint-2xl: 1536px; /* Large screens */
```

### Container Sizes

```css
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container { max-width: 640px; padding: 0 1.5rem; }
}

@media (min-width: 768px) {
  .container { max-width: 768px; padding: 0 2rem; }
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; }
}

@media (min-width: 1280px) {
  .container { max-width: 1280px; }
}
```

### Mobile-First Design Strategy

**Layout Principles:**
1. Start with single-column layouts
2. Add columns as screen size increases
3. Prioritize essential actions on mobile
4. Use progressive disclosure for complex features

**Component Responsiveness:**
```typescript
// Responsive component patterns
const CardGrid = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {cards.map(card => <Card key={card.id} {...card} />)}
  </div>
);

// Responsive text sizing
const PageTitle = ({ children }: { children: React.ReactNode }) => (
  <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
    {children}
  </h1>
);
```

### Touch-Friendly Interface

```css
/* Touch target sizing */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  padding: 12px;
}

/* Hover states on touch devices */
@media (hover: hover) {
  .hover-enabled:hover {
    background-color: hsl(var(--muted) / 0.1);
  }
}

/* Touch-specific interactions */
.touch-drag {
  touch-action: none;
  user-select: none;
}
```

## 10. Content Guidelines

### Writing for CRM Interfaces

**Microcopy Principles:**
- Action-oriented language for buttons
- Present tense for current states
- Future tense for consequences
- Positive framing when possible

**Button Labels:**
```
✅ Good: "Save Contact", "Add Deal", "Send Email"
❌ Avoid: "Submit", "OK", "Click Here"

✅ Good: "Delete Contact"
❌ Avoid: "Are you sure you want to delete?"
```

**Error Messages:**
```
✅ Good: "Email address is required"
❌ Avoid: "Invalid input"

✅ Good: "Choose a deal value between $1 and $10,000,000"
❌ Avoid: "Deal value out of range"
```

**Empty States:**
```
✅ Good: "No contacts yet. Add your first contact to get started."
❌ Avoid: "No data available"

✅ Good: "Your pipeline is empty. Create your first deal to begin tracking."
❌ Avoid: "0 results found"
```

### Data Formatting Standards

**Numbers:**
- Revenue: $1,234,567 (with commas, currency symbol)
- Percentages: 23.5% (one decimal place)
- Large numbers: 1.2M, 45.3K (abbreviated when appropriate)

**Dates:**
- Absolute: Jan 15, 2024 (medium format)
- Relative: 2 days ago (when recent)
- Time: 2:30 PM EST (12-hour format with timezone)

**Status Labels:**
- Title case: "In Progress", "Closed Won"
- Consistent terminology across all features
- Color-coded with accessible contrast

### Internationalization Considerations

**Text Expansion:**
- Design for 30-50% text expansion
- Use flexible layouts
- Avoid hard-coded text widths

**Cultural Sensitivity:**
- Date formats: MM/DD/YYYY vs DD/MM/YYYY
- Number formats: 1,234.56 vs 1.234,56
- Currency placement: $100 vs 100$
- Right-to-left language support

---

## Implementation Checklist

### Phase 1: Foundation
- [ ] Implement color tokens in CSS variables
- [ ] Set up typography scale
- [ ] Create icon mapping system
- [ ] Establish component naming conventions

### Phase 2: Components
- [ ] Update base components with brand styles
- [ ] Implement interaction patterns
- [ ] Add animation system
- [ ] Create accessibility utilities

### Phase 3: Experience
- [ ] Apply data visualization standards
- [ ] Implement responsive patterns
- [ ] Add content guidelines to development process
- [ ] Create brand compliance checklist

### Phase 4: Validation
- [ ] Accessibility audit (automated + manual)
- [ ] Cross-browser testing
- [ ] Mobile device testing
- [ ] Performance impact assessment

---

This style guide serves as the single source of truth for all design decisions in the CRM system. Regular updates and team training ensure consistent implementation across all features and platforms.

**Last Updated:** August 29, 2025
**Version:** 1.0
**Next Review:** September 29, 2025