# Interactive Onboarding Wizard Design

## Problem Statement
New users experience confusion and abandonment due to lack of guided setup process. Without proper onboarding, users don't understand core features, miss important configurations, and fail to see immediate value.

## Design Goals
- **Guided Discovery**: Introduce core features progressively
- **Quick Value**: Help users achieve first success within 5 minutes
- **Personalization**: Adapt experience based on user role and industry
- **Non-intrusive**: Allow advanced users to skip or customize
- **Progressive Engagement**: Multiple engagement levels from basic to advanced

## Onboarding Journey Architecture

### User Segmentation
```typescript
interface UserProfile {
  role: 'admin' | 'sales_rep' | 'manager' | 'marketing';
  experience: 'beginner' | 'intermediate' | 'expert';
  company_size: 'startup' | 'small' | 'medium' | 'enterprise';
  industry: string;
  primary_goals: string[];
}

const onboardingPaths = {
  'sales_rep_beginner': {
    duration: '10-15 minutes',
    focus: ['contact_management', 'pipeline_basics', 'activity_tracking'],
    optional: ['reporting', 'automation']
  },
  'admin_intermediate': {
    duration: '15-20 minutes',
    focus: ['system_setup', 'user_management', 'pipeline_design', 'integrations'],
    optional: ['advanced_reporting', 'custom_fields']
  },
  'manager_experienced': {
    duration: '5-10 minutes',
    focus: ['reporting', 'team_management', 'performance_tracking'],
    optional: ['data_import', 'api_access']
  }
};
```

### Onboarding Stages

#### Stage 1: Welcome & Profile Setup (2 minutes)
```tsx
// Welcome screen with role selection
export function WelcomeStep() {
  return (
    <div className="max-w-2xl mx-auto text-center space-y-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-4"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 
                        rounded-full flex items-center justify-center">
          <Sparkles className="w-8 h-8 text-white" />
        </div>
        <h1 className="text-3xl font-bold">Welcome to your CRM!</h1>
        <p className="text-lg text-muted-foreground">
          Let's set up your workspace in just a few minutes
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-6"
      >
        <div>
          <h3 className="text-lg font-semibold mb-4">What's your role?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {roles.map((role) => (
              <RoleCard key={role.id} role={role} />
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">How familiar are you with CRMs?</h3>
          <div className="flex flex-col sm:flex-row gap-3">
            {experienceLevels.map((level) => (
              <ExperienceButton key={level.id} level={level} />
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
}

const RoleCard = ({ role, selected, onSelect }) => (
  <Card 
    className={cn(
      "cursor-pointer transition-all hover:shadow-md border-2",
      selected ? "border-primary bg-primary/5" : "border-border"
    )}
    onClick={() => onSelect(role.id)}
  >
    <CardContent className="p-6 text-center">
      <role.icon className="w-8 h-8 mx-auto mb-3 text-primary" />
      <h4 className="font-semibold">{role.name}</h4>
      <p className="text-sm text-muted-foreground mt-1">{role.description}</p>
    </CardContent>
  </Card>
);
```

#### Stage 2: Core Setup (3-5 minutes)
```tsx
// Dynamic setup based on user profile
export function CoreSetupStep({ userProfile }) {
  const setupTasks = getSetupTasks(userProfile);
  
  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Let's set up the essentials</h2>
        <p className="text-muted-foreground">
          We've customized these steps based on your role as a {userProfile.role}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {setupTasks.map((task, index) => (
          <SetupTaskCard 
            key={task.id} 
            task={task} 
            index={index}
          />
        ))}
      </div>
    </div>
  );
}

const SetupTaskCard = ({ task, index }) => {
  const [completed, setCompleted] = useState(false);
  const [expanded, setExpanded] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
    >
      <Card className={cn(
        "transition-all hover:shadow-md",
        completed && "border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800"
      )}>
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full",
                completed ? "bg-green-500 text-white" : "bg-muted text-muted-foreground"
              )}>
                {completed ? <Check className="w-4 h-4" /> : <span>{index + 1}</span>}
              </div>
              <div>
                <h4 className="font-semibold">{task.title}</h4>
                <p className="text-sm text-muted-foreground">{task.description}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <Clock className="w-3 h-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">{task.estimatedTime}</span>
                </div>
              </div>
            </div>
            
            <Button
              variant={completed ? "secondary" : "default"}
              size="sm"
              onClick={() => setExpanded(!expanded)}
            >
              {completed ? "Review" : "Start"}
            </Button>
          </div>

          <AnimatePresence>
            {expanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="mt-4 pt-4 border-t overflow-hidden"
              >
                <TaskContent task={task} onComplete={() => setCompleted(true)} />
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
};
```

#### Stage 3: First Success Experience (3-5 minutes)
```tsx
// Guided creation of first contact/deal
export function FirstSuccessStep() {
  const [currentAction, setCurrentAction] = useState('contact');
  const [completed, setCompleted] = useState({
    contact: false,
    deal: false,
    activity: false
  });

  return (
    <div className="max-w-3xl mx-auto space-y-8">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Create your first records</h2>
        <p className="text-muted-foreground">
          Let's add some real data to see how everything works
        </p>
      </div>

      {/* Progress indicator */}
      <div className="flex justify-center">
        <div className="flex space-x-2">
          {Object.entries(completed).map(([key, isCompleted]) => (
            <div
              key={key}
              className={cn(
                "w-3 h-3 rounded-full transition-colors",
                isCompleted ? "bg-green-500" : "bg-muted"
              )}
            />
          ))}
        </div>
      </div>

      {/* Interactive creation flow */}
      <Card>
        <CardContent className="p-6">
          {currentAction === 'contact' && (
            <GuidedContactCreation 
              onComplete={() => {
                setCompleted(prev => ({ ...prev, contact: true }));
                setCurrentAction('deal');
              }}
            />
          )}
          
          {currentAction === 'deal' && (
            <GuidedDealCreation 
              onComplete={() => {
                setCompleted(prev => ({ ...prev, deal: true }));
                setCurrentAction('activity');
              }}
            />
          )}
          
          {currentAction === 'activity' && (
            <GuidedActivityLogging 
              onComplete={() => {
                setCompleted(prev => ({ ...prev, activity: true }));
              }}
            />
          )}
        </CardContent>
      </Card>

      {/* Success celebration */}
      <AnimatePresence>
        {Object.values(completed).every(Boolean) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center space-y-4"
          >
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full 
                            flex items-center justify-center mx-auto">
              <Trophy className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-xl font-semibold text-green-600 dark:text-green-400">
              Great job! You've created your first records
            </h3>
            <p className="text-muted-foreground">
              You can now see how your CRM organizes and tracks customer information
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
```

#### Stage 4: Feature Discovery (Optional, 2-3 minutes)
```tsx
export function FeatureDiscoveryStep({ userProfile }) {
  const features = getRelevantFeatures(userProfile);
  const [currentFeature, setCurrentFeature] = useState(0);

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Discover powerful features</h2>
        <p className="text-muted-foreground">
          Here are some features that will help you as a {userProfile.role}
        </p>
      </div>

      {/* Feature carousel */}
      <div className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentFeature}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.3 }}
          >
            <FeatureSpotlight feature={features[currentFeature]} />
          </motion.div>
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-6">
          <Button
            variant="outline"
            onClick={() => setCurrentFeature(Math.max(0, currentFeature - 1))}
            disabled={currentFeature === 0}
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Previous
          </Button>

          <div className="flex space-x-2">
            {features.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentFeature(index)}
                className={cn(
                  "w-2 h-2 rounded-full transition-colors",
                  currentFeature === index ? "bg-primary" : "bg-muted"
                )}
              />
            ))}
          </div>

          <Button
            variant="outline"
            onClick={() => setCurrentFeature(Math.min(features.length - 1, currentFeature + 1))}
            disabled={currentFeature === features.length - 1}
          >
            Next
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </div>
    </div>
  );
}

const FeatureSpotlight = ({ feature }) => (
  <Card>
    <CardContent className="p-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <feature.icon className="w-6 h-6 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">{feature.title}</h3>
          </div>
          
          <p className="text-muted-foreground">{feature.description}</p>
          
          <div className="space-y-2">
            <h4 className="font-medium">Key benefits:</h4>
            <ul className="space-y-1">
              {feature.benefits.map((benefit, index) => (
                <li key={index} className="flex items-center space-x-2 text-sm">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="flex space-x-2">
            <Button size="sm">Try it now</Button>
            <Button variant="outline" size="sm">Learn more</Button>
          </div>
        </div>
        
        <div className="relative">
          <div className="aspect-video bg-muted rounded-lg overflow-hidden">
            {feature.demo ? (
              <feature.demo />
            ) : (
              <div className="flex items-center justify-center h-full">
                <Play className="w-12 h-12 text-muted-foreground" />
              </div>
            )}
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);
```

## Onboarding Components

### Progress Tracking
```tsx
// components/onboarding/progress-tracker.tsx
export function OnboardingProgress({ 
  steps, 
  currentStep, 
  completedSteps 
}: OnboardingProgressProps) {
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className="space-y-4">
      {/* Overall progress */}
      <div className="flex items-center justify-between text-sm">
        <span className="font-medium">Setup Progress</span>
        <span className="text-muted-foreground">{Math.round(progress)}% complete</span>
      </div>
      
      <div className="w-full bg-muted rounded-full h-2">
        <motion.div
          className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
      
      {/* Step indicators - desktop only */}
      <div className="hidden md:flex justify-between">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.includes(index);
          const isCurrent = currentStep === index;
          
          return (
            <div key={step.id} className="flex flex-col items-center space-y-2">
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all",
                isCompleted && "bg-green-500 text-white",
                isCurrent && !isCompleted && "bg-primary text-primary-foreground",
                !isCompleted && !isCurrent && "bg-muted text-muted-foreground"
              )}>
                {isCompleted ? <Check className="w-4 h-4" /> : index + 1}
              </div>
              <span className={cn(
                "text-xs text-center max-w-20",
                (isCompleted || isCurrent) ? "text-foreground" : "text-muted-foreground"
              )}>
                {step.title}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
```

### Interactive Tutorials
```tsx
// components/onboarding/interactive-tutorial.tsx
export function InteractiveTutorial({ 
  target, 
  content, 
  position = 'bottom' 
}: TutorialProps) {
  return (
    <Popover open={true}>
      <PopoverTrigger asChild>
        <div className="absolute inset-0 z-50" />
      </PopoverTrigger>
      
      <PopoverContent 
        side={position}
        className="w-80 p-4 space-y-3"
      >
        <div className="space-y-2">
          <h4 className="font-semibold">{content.title}</h4>
          <p className="text-sm text-muted-foreground">{content.description}</p>
        </div>
        
        {content.steps && (
          <ol className="space-y-2">
            {content.steps.map((step, index) => (
              <li key={index} className="flex items-start space-x-2 text-sm">
                <span className="flex items-center justify-center w-5 h-5 bg-primary 
                                 text-primary-foreground rounded-full text-xs font-medium 
                                 flex-shrink-0 mt-0.5">
                  {index + 1}
                </span>
                <span>{step}</span>
              </li>
            ))}
          </ol>
        )}
        
        <div className="flex justify-between">
          <Button variant="ghost" size="sm">Skip</Button>
          <Button size="sm">Got it!</Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
```

### Completion Celebration
```tsx
// components/onboarding/completion-celebration.tsx
export function CompletionCelebration({ userProfile, stats }) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className="max-w-2xl mx-auto text-center space-y-8"
    >
      {/* Celebration animation */}
      <div className="relative">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", duration: 0.6 }}
          className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 
                     rounded-full flex items-center justify-center mx-auto"
        >
          <Trophy className="w-12 h-12 text-white" />
        </motion.div>
        
        {/* Confetti effect */}
        <div className="absolute inset-0 flex justify-center">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, scale: 0, y: 0 }}
              animate={{ 
                opacity: [0, 1, 0], 
                scale: [0, 1, 0.5], 
                y: [-20, -40, -60],
                x: [(i - 4) * 20, (i - 4) * 40, (i - 4) * 60]
              }}
              transition={{ 
                duration: 2, 
                delay: 0.5 + i * 0.1,
                ease: "easeOut"
              }}
              className="w-2 h-2 bg-yellow-400 rounded-full absolute"
            />
          ))}
        </div>
      </div>
      
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">
          🎉 Welcome to your CRM, {userProfile.firstName}!
        </h1>
        <p className="text-lg text-muted-foreground">
          You've successfully set up your workspace and you're ready to go!
        </p>
      </div>
      
      {/* Stats summary */}
      <Card>
        <CardContent className="p-6">
          <h3 className="font-semibold mb-4">What you've accomplished:</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { label: "Contacts Added", value: stats.contacts, icon: Users },
              { label: "Deals Created", value: stats.deals, icon: Target },
              { label: "Activities Logged", value: stats.activities, icon: Activity },
              { label: "Setup Complete", value: "100%", icon: CheckCircle }
            ].map((stat) => (
              <div key={stat.label} className="text-center space-y-2">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center 
                               justify-center mx-auto">
                  <stat.icon className="w-5 h-5 text-primary" />
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <div className="text-xs text-muted-foreground">{stat.label}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Next steps */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold">What's next?</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {getNextSteps(userProfile).map((step) => (
            <Card key={step.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4 flex items-center space-x-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <step.icon className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium">{step.title}</h4>
                  <p className="text-sm text-muted-foreground">{step.description}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
      
      <div className="flex justify-center space-x-4">
        <Button size="lg">Go to Dashboard</Button>
        <Button variant="outline" size="lg">Take a Tour</Button>
      </div>
    </motion.div>
  );
}
```

## Contextual Help System

### Progressive Help Tooltips
```tsx
// Help system that appears contextually
export function ContextualHelp({ trigger, children }) {
  const [showHelp, setShowHelp] = useState(false);
  const { isOnboardingMode } = useOnboarding();
  
  return (
    <div className="relative">
      {trigger}
      
      {isOnboardingMode && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute -top-2 -right-2 z-50"
        >
          <button
            onClick={() => setShowHelp(true)}
            className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center 
                       justify-center text-xs font-bold animate-pulse hover:animate-none
                       hover:bg-blue-600 transition-colors"
          >
            ?
          </button>
        </motion.div>
      )}
      
      <Dialog open={showHelp} onOpenChange={setShowHelp}>
        <DialogContent className="max-w-md">
          {children}
        </DialogContent>
      </Dialog>
    </div>
  );
}
```

This comprehensive onboarding system ensures users understand the CRM's value and know how to use it effectively, reducing abandonment and increasing long-term engagement.