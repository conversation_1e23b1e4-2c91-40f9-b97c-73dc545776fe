# CRM UI Design System - Comprehensive Specifications

## Overview
This design system provides a complete UI framework for the CRM system built on Next.js 14, shadcn/ui, and Tailwind CSS. It addresses critical issues identified in the audit: mobile responsiveness, form complexity, accessibility, and user guidance.

## Design Principles

### 1. Mobile-First Responsive Design
- **Breakpoints**: 
  - `sm`: 640px (mobile landscape)
  - `md`: 768px (tablet)
  - `lg`: 1024px (desktop)
  - `xl`: 1280px (large desktop)
  - `2xl`: 1536px (ultra-wide)

### 2. Progressive Disclosure
- Complex forms broken into digestible steps
- Smart defaults with optional advanced fields
- Context-aware field revelation

### 3. Consistent Visual Language
- Unified color system with semantic meanings
- Standardized spacing and typography
- Predictable interaction patterns

### 4. Accessibility-First
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader optimization
- High contrast ratios

## Color System

### Primary Colors
```css
/* Brand Colors */
--primary: 217 87% 56%;           /* #3B82F6 - Blue 500 */
--primary-foreground: 210 40% 98%; /* #F8FAFC - Slate 50 */

/* Secondary Colors */
--secondary: 210 40% 95%;         /* #F1F5F9 - Slate 100 */
--secondary-foreground: 222 84% 5%; /* #0F172A - Slate 900 */

/* Accent Colors */
--accent: 142 76% 73%;            /* #86EFAC - Green 300 */
--accent-foreground: 159 100% 6%; /* #003D16 - Green 950 */
```

### Semantic Colors
```css
/* Status Colors */
--success: 142 71% 45%;           /* #16A34A - Green 600 */
--warning: 43 96% 56%;            /* #EAB308 - Yellow 500 */
--error: 0 84% 60%;               /* #EF4444 - Red 500 */
--info: 201 96% 32%;              /* #0284C7 - Sky 600 */

/* Background System */
--background: 0 0% 100%;          /* #FFFFFF - White */
--foreground: 222 84% 5%;         /* #0F172A - Slate 900 */

--card: 0 0% 100%;                /* #FFFFFF - White */
--card-foreground: 222 84% 5%;    /* #0F172A - Slate 900 */

--muted: 210 40% 98%;             /* #F8FAFC - Slate 50 */
--muted-foreground: 215 16% 47%;  /* #64748B - Slate 500 */

--border: 214 32% 91%;            /* #E2E8F0 - Slate 200 */
--input: 214 32% 91%;             /* #E2E8F0 - Slate 200 */
--ring: 217 87% 56%;              /* #3B82F6 - Blue 500 */
```

### Dark Mode Colors
```css
/* Dark Mode Overrides */
--background: 222 84% 5%;         /* #0F172A - Slate 900 */
--foreground: 210 40% 98%;        /* #F8FAFC - Slate 50 */

--card: 222 84% 5%;               /* #0F172A - Slate 900 */
--card-foreground: 210 40% 98%;   /* #F8FAFC - Slate 50 */

--muted: 217 33% 17%;             /* #1E293B - Slate 800 */
--muted-foreground: 215 20% 65%;  /* #94A3B8 - Slate 400 */

--border: 217 33% 17%;            /* #1E293B - Slate 800 */
--input: 217 33% 17%;             /* #1E293B - Slate 800 */
```

## Typography Scale

### Font System
```css
/* Font Families */
--font-sans: 'Inter', system-ui, -apple-system, sans-serif;
--font-mono: 'JetBrains Mono', 'Fira Code', monospace;

/* Font Weights */
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;
```

### Type Scale (Mobile-First)
```css
/* Display Typography */
.text-display {
  font-size: 2.25rem;    /* 36px */
  line-height: 2.5rem;   /* 40px */
  font-weight: 800;
  letter-spacing: -0.025em;
}

/* Heading Scale */
.text-h1 {
  font-size: 1.875rem;   /* 30px */
  line-height: 2.25rem;  /* 36px */
  font-weight: 700;
  letter-spacing: -0.025em;
}

.text-h2 {
  font-size: 1.5rem;     /* 24px */
  line-height: 2rem;     /* 32px */
  font-weight: 600;
  letter-spacing: -0.025em;
}

.text-h3 {
  font-size: 1.25rem;    /* 20px */
  line-height: 1.75rem;  /* 28px */
  font-weight: 600;
}

.text-h4 {
  font-size: 1.125rem;   /* 18px */
  line-height: 1.5rem;   /* 24px */
  font-weight: 600;
}

/* Body Typography */
.text-body {
  font-size: 1rem;       /* 16px */
  line-height: 1.5rem;   /* 24px */
  font-weight: 400;
}

.text-body-sm {
  font-size: 0.875rem;   /* 14px */
  line-height: 1.25rem;  /* 20px */
  font-weight: 400;
}

.text-caption {
  font-size: 0.75rem;    /* 12px */
  line-height: 1rem;     /* 16px */
  font-weight: 400;
}

/* Desktop Responsive Scaling */
@media (min-width: 1024px) {
  .text-display {
    font-size: 3rem;      /* 48px */
    line-height: 3.5rem;  /* 56px */
  }
  
  .text-h1 {
    font-size: 2.5rem;    /* 40px */
    line-height: 3rem;    /* 48px */
  }
  
  .text-h2 {
    font-size: 2rem;      /* 32px */
    line-height: 2.5rem;  /* 40px */
  }
}
```

## Spacing System

### Consistent Spacing Scale
```css
/* Base spacing units (8px grid) */
--space-0: 0;           /* 0px */
--space-1: 0.25rem;     /* 4px */
--space-2: 0.5rem;      /* 8px */
--space-3: 0.75rem;     /* 12px */
--space-4: 1rem;        /* 16px */
--space-5: 1.25rem;     /* 20px */
--space-6: 1.5rem;      /* 24px */
--space-8: 2rem;        /* 32px */
--space-10: 2.5rem;     /* 40px */
--space-12: 3rem;       /* 48px */
--space-16: 4rem;       /* 64px */
--space-20: 5rem;       /* 80px */
--space-24: 6rem;       /* 96px */
```

### Component Spacing Guidelines
```css
/* Form Field Spacing */
.form-field-gap: var(--space-4);        /* 16px between fields */
.form-group-gap: var(--space-6);        /* 24px between groups */
.form-section-gap: var(--space-12);     /* 48px between sections */

/* Card Padding */
.card-padding-sm: var(--space-4);       /* 16px - mobile */
.card-padding-md: var(--space-6);       /* 24px - tablet */
.card-padding-lg: var(--space-8);       /* 32px - desktop */

/* Layout Spacing */
.container-padding: var(--space-4);     /* 16px - mobile container */
.container-padding-lg: var(--space-8);  /* 32px - desktop container */
.section-gap: var(--space-16);          /* 64px between major sections */
```

## Component Architecture

### Base Component Pattern
```typescript
interface ComponentProps {
  children?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
}
```

### State Management
```typescript
// Component states to always design for
interface ComponentStates {
  default: boolean;
  hover: boolean;
  focus: boolean;
  active: boolean;
  disabled: boolean;
  loading: boolean;
  error: boolean;
  success: boolean;
}
```

## Accessibility Standards

### WCAG 2.1 AA Compliance
```css
/* Minimum contrast ratios */
.text-normal: 4.5:1;     /* Normal text */
.text-large: 3:1;        /* Large text (18px+ or 14px+ bold) */
.ui-components: 3:1;     /* UI components and graphics */

/* Focus indicators */
.focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
  border-radius: 0.375rem;
}

/* Touch targets */
.touch-target {
  min-height: 44px;      /* Minimum touch target size */
  min-width: 44px;
}
```

### Keyboard Navigation
```css
/* Tab order and focus management */
.focusable {
  position: relative;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: var(--primary-foreground);
  padding: 8px;
  text-decoration: none;
  border-radius: 0.375rem;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
```

## Animation Guidelines

### Motion Tokens
```css
/* Duration tokens */
--motion-instant: 0ms;
--motion-fast: 150ms;
--motion-normal: 300ms;
--motion-slow: 500ms;
--motion-slower: 750ms;

/* Easing functions */
--ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
--ease-in-cubic: cubic-bezier(0.32, 0, 0.67, 0);
--ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
--ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
```

### Animation Patterns
```css
/* Micro-interactions */
.hover-lift {
  transition: transform var(--motion-fast) var(--ease-out-cubic);
}
.hover-lift:hover {
  transform: translateY(-2px);
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, 
    var(--muted) 0%, 
    var(--muted-foreground/10%) 50%, 
    var(--muted) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Page transitions */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}
.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all var(--motion-normal) var(--ease-out-cubic);
}
```

## Responsive Design Strategy

### Breakpoint System
```css
/* Mobile-first approach */
@media (min-width: 640px) {  /* sm: Small tablets */
  /* Tablet optimizations */
}

@media (min-width: 768px) {  /* md: Large tablets */
  /* Sidebar transitions from mobile drawer to fixed */
}

@media (min-width: 1024px) { /* lg: Desktop */
  /* Full desktop experience */
}

@media (min-width: 1280px) { /* xl: Large desktop */
  /* Enhanced spacing and typography */
}
```

### Layout Patterns
```css
/* Container patterns */
.container {
  width: 100%;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

@media (min-width: 768px) {
  .container {
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
}

/* Grid systems */
.grid-responsive {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
}

@media (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
  }
}
```

## Implementation Guidelines

### Component Organization
```
/components/
  /ui/                 # Base shadcn/ui components
  /forms/              # Form components and patterns
  /layouts/            # Layout components
  /data-display/       # Charts, tables, lists
  /feedback/           # Toasts, alerts, loading states
  /overlays/           # Modals, popovers, tooltips
  /navigation/         # Navbars, breadcrumbs, pagination
```

### Development Best Practices
1. **Mobile-first CSS**: Always start with mobile styles
2. **Component composition**: Build complex UIs from simple components
3. **Semantic HTML**: Use proper HTML elements for accessibility
4. **Progressive enhancement**: Core functionality works without JavaScript
5. **Performance optimization**: Lazy loading and code splitting
6. **Type safety**: Full TypeScript coverage for all components

This design system provides the foundation for creating a cohesive, accessible, and responsive CRM interface that scales from mobile to desktop while maintaining consistency and usability.