# Accessible Drag & Drop Interface Design

## Problem Statement
Current drag & drop functionality lacks proper accessibility support, keyboard navigation, and mobile touch optimization. Screen reader users cannot interact with the pipeline, and touch devices have poor drag experiences.

## Design Goals
- **WCAG 2.1 AA Compliance**: Full accessibility support with screen readers
- **Keyboard Navigation**: Complete keyboard-only operation
- **Touch Optimization**: Smooth mobile drag experience with haptic feedback
- **Multi-Modal Interaction**: Mouse, touch, keyboard, and assistive technology support
- **Visual Feedback**: Clear drag states and drop zones
- **Auto-scroll**: Smooth scrolling during long drags

## Accessibility Standards

### ARIA Implementation
```typescript
// Drag & Drop ARIA attributes
interface DragDropARIA {
  // Draggable items
  'aria-grabbed'?: boolean;
  'aria-describedby'?: string;
  'role': 'button' | 'listitem';
  'tabindex': number;
  
  // Drop zones
  'aria-dropeffect'?: 'none' | 'copy' | 'move' | 'link';
  'aria-label': string;
  'aria-expanded'?: boolean;
  
  // Live regions for announcements
  'aria-live': 'polite' | 'assertive';
  'aria-atomic': boolean;
}
```

### Screen Reader Announcements
```typescript
// Announcement messages for screen readers
const announcements = {
  dragStart: (item: string) => `Started dragging ${item}`,
  dragOver: (zone: string) => `Dragging over ${zone}`,
  dragCancel: () => `Drag cancelled`,
  dropSuccess: (item: string, zone: string) => `${item} moved to ${zone}`,
  dropError: (error: string) => `Drop failed: ${error}`,
  keyboardInstructions: `Use arrow keys to navigate, Space to pick up, Enter to drop, Escape to cancel`,
};
```

## Enhanced Drag & Drop Components

### 1. Accessible Draggable Item
```tsx
// components/drag-drop/accessible-draggable.tsx
interface AccessibleDraggableProps {
  id: string;
  data: any;
  children: React.ReactNode;
  disabled?: boolean;
  dragHandle?: boolean;
  preview?: React.ReactNode;
  ariaLabel?: string;
  ariaDescription?: string;
  onDragStart?: (data: any) => void;
  onDragEnd?: (result: DragResult) => void;
}

export function AccessibleDraggable({
  id,
  data,
  children,
  disabled = false,
  dragHandle = false,
  preview,
  ariaLabel,
  ariaDescription,
  onDragStart,
  onDragEnd
}: AccessibleDraggableProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isKeyboardDragging, setIsKeyboardDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [touchStartPos, setTouchStartPos] = useState({ x: 0, y: 0 });
  
  const elementRef = useRef<HTMLDivElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);
  const announceRef = useRef<HTMLDivElement>(null);

  // Keyboard drag & drop implementation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case ' ': // Space to start/stop drag
        e.preventDefault();
        if (isKeyboardDragging) {
          handleKeyboardDrop();
        } else {
          handleKeyboardDragStart();
        }
        break;
        
      case 'Enter': // Enter to confirm drop
        if (isKeyboardDragging) {
          e.preventDefault();
          handleKeyboardDrop();
        }
        break;
        
      case 'Escape': // Escape to cancel
        if (isKeyboardDragging) {
          e.preventDefault();
          handleKeyboardCancel();
        }
        break;
        
      case 'ArrowUp':
      case 'ArrowDown':
      case 'ArrowLeft':
      case 'ArrowRight':
        if (isKeyboardDragging) {
          e.preventDefault();
          handleKeyboardNavigation(e.key);
        }
        break;
    }
  };

  const handleKeyboardDragStart = () => {
    setIsKeyboardDragging(true);
    onDragStart?.(data);
    announce(announcements.dragStart(ariaLabel || id));
    
    // Add keyboard drag mode class
    document.body.classList.add('keyboard-drag-mode');
  };

  const handleKeyboardDrop = () => {
    const dropZone = findActiveDropZone();
    if (dropZone) {
      const result = { draggableId: id, destination: dropZone };
      onDragEnd?.(result);
      announce(announcements.dropSuccess(ariaLabel || id, dropZone.label));
    } else {
      announce(announcements.dropError('No valid drop zone'));
    }
    
    setIsKeyboardDragging(false);
    document.body.classList.remove('keyboard-drag-mode');
  };

  const handleKeyboardCancel = () => {
    setIsKeyboardDragging(false);
    announce(announcements.dragCancel());
    document.body.classList.remove('keyboard-drag-mode');
  };

  const handleKeyboardNavigation = (direction: string) => {
    const dropZones = findAvailableDropZones();
    const currentZone = findActiveDropZone();
    const currentIndex = currentZone ? dropZones.indexOf(currentZone) : -1;
    
    let nextIndex = 0;
    switch (direction) {
      case 'ArrowUp':
        nextIndex = Math.max(0, currentIndex - 1);
        break;
      case 'ArrowDown':
        nextIndex = Math.min(dropZones.length - 1, currentIndex + 1);
        break;
      case 'ArrowLeft':
        nextIndex = Math.max(0, currentIndex - 1);
        break;
      case 'ArrowRight':
        nextIndex = Math.min(dropZones.length - 1, currentIndex + 1);
        break;
    }
    
    const nextZone = dropZones[nextIndex];
    if (nextZone) {
      focusDropZone(nextZone);
      announce(announcements.dragOver(nextZone.label));
    }
  };

  // Touch handling for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    if (disabled) return;
    
    const touch = e.touches[0];
    setTouchStartPos({ x: touch.clientX, y: touch.clientY });
    
    // Haptic feedback on supported devices
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (disabled || !isDragging) return;
    
    const touch = e.touches[0];
    const deltaX = touch.clientX - touchStartPos.x;
    const deltaY = touch.clientY - touchStartPos.y;
    
    setDragOffset({ x: deltaX, y: deltaY });
    
    // Find drop zone under touch
    const elementUnderTouch = document.elementFromPoint(touch.clientX, touch.clientY);
    const dropZone = elementUnderTouch?.closest('[data-drop-zone]');
    
    if (dropZone) {
      dropZone.classList.add('drop-zone-active');
      // Haptic feedback when over drop zone
      if ('vibrate' in navigator) {
        navigator.vibrate(25);
      }
    }
  };

  // Drag & Drop API integration
  const dragProps = useDrag({
    type: 'DRAGGABLE_ITEM',
    item: () => {
      setIsDragging(true);
      onDragStart?.(data);
      return { id, data };
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: (item, monitor) => {
      setIsDragging(false);
      const dropResult = monitor.getDropResult();
      onDragEnd?.({ draggableId: id, destination: dropResult });
    },
  });

  const announce = (message: string) => {
    if (announceRef.current) {
      announceRef.current.textContent = message;
    }
  };

  return (
    <>
      {/* Screen reader announcements */}
      <div
        ref={announceRef}
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />

      <div
        ref={elementRef}
        role="button"
        tabIndex={disabled ? -1 : 0}
        aria-grabbed={isKeyboardDragging}
        aria-describedby={ariaDescription ? `${id}-description` : undefined}
        aria-label={ariaLabel || `Draggable item ${id}`}
        className={cn(
          "relative focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary",
          "transition-all duration-200",
          isDragging && "opacity-50 scale-105",
          isKeyboardDragging && "ring-2 ring-primary shadow-lg",
          disabled && "opacity-50 cursor-not-allowed",
          !disabled && "cursor-grab active:cursor-grabbing"
        )}
        style={{
          transform: isDragging ? `translate(${dragOffset.x}px, ${dragOffset.y}px)` : undefined,
        }}
        onKeyDown={handleKeyDown}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        {...dragProps}
      >
        {/* Drag handle */}
        {dragHandle && (
          <div
            ref={dragHandleRef}
            className="absolute left-2 top-1/2 -translate-y-1/2 cursor-grab active:cursor-grabbing
                       opacity-0 group-hover:opacity-100 transition-opacity"
            aria-label="Drag handle"
          >
            <GripVertical className="w-4 h-4 text-muted-foreground" />
          </div>
        )}

        {children}

        {/* Keyboard instructions */}
        {ariaDescription && (
          <div id={`${id}-description`} className="sr-only">
            {ariaDescription} {announcements.keyboardInstructions}
          </div>
        )}
      </div>

      {/* Drag preview */}
      {isDragging && preview && (
        <div className="fixed pointer-events-none z-50 opacity-80 transform -translate-x-1/2 -translate-y-1/2">
          {preview}
        </div>
      )}
    </>
  );
}
```

### 2. Accessible Drop Zone
```tsx
// components/drag-drop/accessible-drop-zone.tsx
interface AccessibleDropZoneProps {
  id: string;
  accepts: string[];
  children: React.ReactNode;
  disabled?: boolean;
  ariaLabel?: string;
  onDrop?: (item: any, monitor: any) => void;
  onDragOver?: (item: any) => void;
  onDragLeave?: () => void;
  className?: string;
}

export function AccessibleDropZone({
  id,
  accepts,
  children,
  disabled = false,
  ariaLabel,
  onDrop,
  onDragOver,
  onDragLeave,
  className
}: AccessibleDropZoneProps) {
  const [isOver, setIsOver] = useState(false);
  const [canDrop, setCanDrop] = useState(false);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  const [{ isOver: isDragOver, canDrop: canDropItem }, drop] = useDrop({
    accept: accepts,
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
    hover: (item) => {
      setIsOver(true);
      onDragOver?.(item);
    },
    drop: (item, monitor) => {
      setIsOver(false);
      onDrop?.(item, monitor);
      
      // Success haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([50, 50, 50]);
      }
    },
  });

  // Keyboard focus management
  const handleFocus = () => {
    if (document.body.classList.contains('keyboard-drag-mode')) {
      setIsOver(true);
      onDragOver?.(getCurrentDragItem());
    }
  };

  const handleBlur = () => {
    if (document.body.classList.contains('keyboard-drag-mode')) {
      setIsOver(false);
      onDragLeave?.();
    }
  };

  // Auto-scroll during drag
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    
    const rect = dropZoneRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const scrollThreshold = 50;
    const scrollSpeed = 10;
    
    // Scroll up
    if (e.clientY - rect.top < scrollThreshold) {
      dropZoneRef.current?.scrollBy(0, -scrollSpeed);
    }
    // Scroll down
    else if (rect.bottom - e.clientY < scrollThreshold) {
      dropZoneRef.current?.scrollBy(0, scrollSpeed);
    }
  };

  drop(dropZoneRef);

  return (
    <div
      ref={dropZoneRef}
      data-drop-zone={id}
      role="region"
      tabIndex={document.body.classList.contains('keyboard-drag-mode') ? 0 : -1}
      aria-label={ariaLabel || `Drop zone ${id}`}
      aria-dropeffect={disabled ? 'none' : 'move'}
      className={cn(
        "transition-all duration-200 rounded-lg",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary",
        
        // Drop zone states
        isOver && canDropItem && !disabled && [
          "border-2 border-dashed border-primary bg-primary/5",
          "shadow-lg transform scale-[1.02]"
        ],
        
        isOver && !canDropItem && [
          "border-2 border-dashed border-destructive bg-destructive/5"
        ],
        
        disabled && "opacity-50 cursor-not-allowed",
        
        className
      )}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onDragOver={handleDragOver}
    >
      {/* Drop zone indicator */}
      <AnimatePresence>
        {isOver && canDropItem && !disabled && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute inset-0 flex items-center justify-center
                       bg-primary/10 border-2 border-dashed border-primary rounded-lg z-10"
          >
            <div className="text-center space-y-2">
              <Upload className="w-8 h-8 text-primary mx-auto" />
              <p className="text-sm font-medium text-primary">Drop here</p>
            </div>
          </motion.div>
        )}
        
        {isOver && !canDropItem && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute inset-0 flex items-center justify-center
                       bg-destructive/10 border-2 border-dashed border-destructive rounded-lg z-10"
          >
            <div className="text-center space-y-2">
              <X className="w-8 h-8 text-destructive mx-auto" />
              <p className="text-sm font-medium text-destructive">Cannot drop here</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {children}
    </div>
  );
}
```

### 3. Pipeline Kanban with Accessibility
```tsx
// components/pipeline/accessible-kanban.tsx
export function AccessibleKanban({ 
  stages, 
  cards, 
  onCardMove,
  onStageScroll 
}: AccessibleKanbanProps) {
  const [activeCard, setActiveCard] = useState<string | null>(null);
  const [dragInstructions, setDragInstructions] = useState('');
  
  const handleCardDragStart = (cardId: string) => {
    setActiveCard(cardId);
    const card = cards.find(c => c.id === cardId);
    const instructions = `Moving ${card?.title}. Use arrow keys to navigate between stages, Enter to drop, Escape to cancel.`;
    setDragInstructions(instructions);
    announceToScreenReader(instructions);
  };

  const handleCardMove = (cardId: string, fromStage: string, toStage: string) => {
    const card = cards.find(c => c.id === cardId);
    const fromStageName = stages.find(s => s.id === fromStage)?.name;
    const toStageName = stages.find(s => s.id === toStage)?.name;
    
    onCardMove(cardId, fromStage, toStage);
    
    announceToScreenReader(`${card?.title} moved from ${fromStageName} to ${toStageName}`);
    setActiveCard(null);
    setDragInstructions('');
  };

  return (
    <div className="flex space-x-6 overflow-x-auto pb-6">
      {/* Live region for announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {dragInstructions}
      </div>

      {stages.map((stage, stageIndex) => (
        <div key={stage.id} className="flex-shrink-0 w-80">
          {/* Stage Header */}
          <div className="mb-4 p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg">{stage.name}</h3>
              <Badge variant="secondary">
                {cards.filter(card => card.stageId === stage.id).length}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {stage.description}
            </p>
          </div>

          {/* Drop Zone */}
          <AccessibleDropZone
            id={stage.id}
            accepts={['CARD']}
            ariaLabel={`${stage.name} stage - drop cards here`}
            onDrop={(item) => handleCardMove(item.id, item.stageId, stage.id)}
            className="min-h-[400px] p-4 bg-background border border-dashed border-muted-foreground/25"
          >
            <div className="space-y-3">
              {cards
                .filter(card => card.stageId === stage.id)
                .map((card, cardIndex) => (
                  <AccessibleDraggable
                    key={card.id}
                    id={card.id}
                    data={card}
                    ariaLabel={`${card.title} - ${card.value ? formatCurrency(card.value) : 'No value'}`}
                    ariaDescription={`Card in ${stage.name} stage. ${card.description || ''}`}
                    onDragStart={() => handleCardDragStart(card.id)}
                    dragHandle={true}
                  >
                    <KanbanCard
                      card={card}
                      isActive={activeCard === card.id}
                      stageIndex={stageIndex}
                      cardIndex={cardIndex}
                    />
                  </AccessibleDraggable>
                ))}

              {/* Empty state */}
              {cards.filter(card => card.stageId === stage.id).length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Inbox className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">No cards in this stage</p>
                </div>
              )}
            </div>
          </AccessibleDropZone>
        </div>
      ))}
    </div>
  );
}

// Individual card component with keyboard navigation
function KanbanCard({ 
  card, 
  isActive, 
  stageIndex, 
  cardIndex 
}: KanbanCardProps) {
  const cardRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to active card
  useEffect(() => {
    if (isActive && cardRef.current) {
      cardRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }, [isActive]);

  return (
    <Card
      ref={cardRef}
      className={cn(
        "group cursor-pointer transition-all duration-200",
        "hover:shadow-md hover:scale-[1.02]",
        isActive && "ring-2 ring-primary shadow-lg scale-105"
      )}
    >
      <CardContent className="p-4">
        {/* Card header */}
        <div className="flex items-start justify-between mb-3">
          <h4 className="font-medium line-clamp-2">{card.title}</h4>
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Card content */}
        <div className="space-y-2">
          {card.value && (
            <p className="text-lg font-semibold text-green-600">
              {formatCurrency(card.value)}
            </p>
          )}
          
          {card.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {card.description}
            </p>
          )}

          {/* Card footer */}
          <div className="flex items-center justify-between pt-2">
            {card.assignee && (
              <div className="flex items-center space-x-2">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={card.assignee.avatar} />
                  <AvatarFallback className="text-xs">
                    {card.assignee.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <span className="text-xs text-muted-foreground">
                  {card.assignee.name}
                </span>
              </div>
            )}

            {card.dueDate && (
              <span className={cn(
                "text-xs px-2 py-1 rounded",
                isOverdue(card.dueDate) 
                  ? "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300"
                  : "bg-muted text-muted-foreground"
              )}>
                {formatDate(card.dueDate)}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

### 4. Touch Gestures Enhancement
```tsx
// hooks/use-touch-drag.ts
export function useTouchDrag({
  onDragStart,
  onDrag,
  onDragEnd,
  threshold = 5
}: UseTouchDragProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [currentPos, setCurrentPos] = useState({ x: 0, y: 0 });
  const touchStartTime = useRef(0);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    const touch = e.touches[0];
    touchStartTime.current = Date.now();
    setStartPos({ x: touch.clientX, y: touch.clientY });
    setCurrentPos({ x: touch.clientX, y: touch.clientY });
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!e.touches[0]) return;

    const touch = e.touches[0];
    const deltaX = touch.clientX - startPos.x;
    const deltaY = touch.clientY - startPos.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    setCurrentPos({ x: touch.clientX, y: touch.clientY });

    if (!isDragging && distance > threshold) {
      setIsDragging(true);
      onDragStart?.({ x: startPos.x, y: startPos.y });
      
      // Prevent scrolling during drag
      e.preventDefault();
      
      // Haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
    } else if (isDragging) {
      onDrag?.({
        x: touch.clientX,
        y: touch.clientY,
        deltaX,
        deltaY
      });
      
      // Prevent default to avoid scrolling
      e.preventDefault();
    }
  }, [isDragging, startPos, threshold, onDragStart, onDrag]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (isDragging) {
      const touchDuration = Date.now() - touchStartTime.current;
      onDragEnd?.({
        startPos,
        endPos: currentPos,
        duration: touchDuration
      });
      
      // Success haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([50, 50, 50]);
      }
    }
    
    setIsDragging(false);
  }, [isDragging, startPos, currentPos, onDragEnd]);

  return {
    isDragging,
    startPos,
    currentPos,
    handlers: {
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
    }
  };
}
```

### 5. Keyboard Navigation Helper
```tsx
// components/drag-drop/keyboard-navigation-overlay.tsx
export function KeyboardNavigationOverlay() {
  const [isActive, setIsActive] = useState(false);
  const [currentFocus, setCurrentFocus] = useState<string | null>(null);

  useEffect(() => {
    const handleKeyboardDragMode = () => {
      setIsActive(document.body.classList.contains('keyboard-drag-mode'));
    };

    // Listen for keyboard drag mode changes
    const observer = new MutationObserver(handleKeyboardDragMode);
    observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });

    return () => observer.disconnect();
  }, []);

  if (!isActive) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-40">
      {/* Keyboard instructions overlay */}
      <div className="absolute top-4 right-4 bg-background border rounded-lg p-4 shadow-lg pointer-events-auto">
        <h3 className="font-medium mb-2">Keyboard Drag Mode</h3>
        <ul className="text-sm space-y-1 text-muted-foreground">
          <li>→ ← ↑ ↓ Navigate between drop zones</li>
          <li><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Enter</kbd> Drop item</li>
          <li><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Esc</kbd> Cancel drag</li>
        </ul>
      </div>

      {/* Focus indicators for drop zones */}
      {currentFocus && (
        <div className="absolute inset-0">
          <div className="absolute border-2 border-primary rounded-lg animate-pulse" />
        </div>
      )}
    </div>
  );
}
```

This accessible drag & drop system ensures that all users, regardless of ability or device, can fully interact with the CRM's pipeline and organizational features while maintaining smooth performance and clear visual feedback.