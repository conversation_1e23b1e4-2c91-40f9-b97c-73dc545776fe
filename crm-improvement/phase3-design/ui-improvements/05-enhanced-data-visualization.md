# Enhanced Data Visualization Design

## Problem Statement
Current data visualization lacks depth, interactivity, and mobile optimization. Users struggle to gain insights from static charts and basic metrics, leading to poor decision-making and reduced engagement.

## Design Goals
- **Interactive Charts**: Click, hover, and drill-down capabilities
- **Mobile-First**: Charts that work perfectly on small screens
- **Real-Time Updates**: Live data with smooth animations
- **Contextual Insights**: AI-powered insights and anomaly detection
- **Customizable Dashboards**: User-configurable chart layouts
- **Export Capabilities**: Share and export visualizations

## Visualization Components Architecture

### 1. Responsive Chart System
```typescript
// Base chart configuration
interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'funnel' | 'heatmap';
  responsive: boolean;
  animation: boolean;
  interactive: boolean;
  realTime?: boolean;
  theme: 'light' | 'dark' | 'auto';
}

// Responsive breakpoints for charts
const chartBreakpoints = {
  mobile: {
    maxWidth: 480,
    height: 200,
    showLegend: false,
    axisFontSize: 10,
    padding: { top: 20, right: 20, bottom: 40, left: 40 }
  },
  tablet: {
    maxWidth: 768,
    height: 300,
    showLegend: true,
    axisFontSize: 12,
    padding: { top: 30, right: 30, bottom: 50, left: 60 }
  },
  desktop: {
    maxWidth: Infinity,
    height: 400,
    showLegend: true,
    axisFontSize: 14,
    padding: { top: 40, right: 40, bottom: 60, left: 80 }
  }
};
```

### 2. Interactive Dashboard Components
```tsx
// components/charts/interactive-dashboard.tsx
export function InteractiveDashboard() {
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [timeRange, setTimeRange] = useState('30d');
  const [chartType, setChartType] = useState('line');
  
  return (
    <div className="space-y-6">
      {/* Dashboard Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center 
                          justify-between">
            <div className="flex flex-wrap gap-2">
              <MetricSelector 
                value={selectedMetric} 
                onChange={setSelectedMetric} 
              />
              <TimeRangeSelector 
                value={timeRange} 
                onChange={setTimeRange} 
              />
              <ChartTypeSelector 
                value={chartType} 
                onChange={setChartType} 
              />
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Share className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Chart Area */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Primary Chart */}
        <div className="xl:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{getMetricTitle(selectedMetric)}</span>
                <InsightBadge metric={selectedMetric} />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveChart
                type={chartType}
                data={data}
                metric={selectedMetric}
                timeRange={timeRange}
                height="400px"
              />
            </CardContent>
          </Card>
        </div>

        {/* Secondary Charts */}
        <div className="space-y-6">
          <DistributionChart />
          <TrendIndicators />
        </div>
      </div>

      {/* Detail Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard metric="totalRevenue" />
        <MetricCard metric="dealCount" />
        <MetricCard metric="conversionRate" />
        <MetricCard metric="avgDealSize" />
      </div>
    </div>
  );
}
```

### 3. Mobile-Optimized Chart Component
```tsx
// components/charts/responsive-chart.tsx
export function ResponsiveChart({ 
  type, 
  data, 
  metric, 
  height = "300px",
  interactive = true 
}: ResponsiveChartProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width } = containerRef.current.getBoundingClientRect();
        const isMobileView = width < 768;
        setIsMobile(isMobileView);
        setDimensions({ 
          width: width - 40, // Account for padding
          height: parseInt(height) 
        });
      }
    };

    updateDimensions();
    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => resizeObserver.disconnect();
  }, [height]);

  const chartConfig = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
    plugins: {
      legend: {
        display: !isMobile,
        position: isMobile ? 'bottom' : 'top' as const,
      },
      tooltip: {
        enabled: true,
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#3B82F6',
        borderWidth: 1,
      },
      zoom: interactive ? {
        pan: {
          enabled: true,
          mode: 'x' as const,
        },
        zoom: {
          enabled: true,
          mode: 'x' as const,
        },
      } : undefined,
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: !isMobile,
        },
        ticks: {
          maxRotation: isMobile ? 45 : 0,
          font: {
            size: isMobile ? 10 : 12,
          },
        },
      },
      y: {
        display: true,
        grid: {
          display: true,
        },
        ticks: {
          font: {
            size: isMobile ? 10 : 12,
          },
          callback: (value: any) => formatMetricValue(value, metric),
        },
      },
    },
  }), [isMobile, interactive, metric]);

  return (
    <div ref={containerRef} className="relative">
      <div style={{ height }}>
        {type === 'line' && (
          <Line data={transformedData} options={chartConfig} />
        )}
        {type === 'bar' && (
          <Bar data={transformedData} options={chartConfig} />
        )}
        {type === 'pie' && (
          <Pie data={transformedData} options={chartConfig} />
        )}
        {type === 'area' && (
          <Area data={transformedData} options={chartConfig} />
        )}
      </div>
      
      {/* Mobile-specific touch controls */}
      {isMobile && interactive && (
        <div className="absolute bottom-2 right-2 flex gap-1">
          <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
            <ZoomIn className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
            <ZoomOut className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
```

### 4. Sales Funnel Visualization
```tsx
// components/charts/sales-funnel.tsx
export function SalesFunnel({ data, interactive = true }) {
  const [selectedStage, setSelectedStage] = useState(null);
  
  const funnelData = data.map((stage, index) => ({
    ...stage,
    conversionRate: index > 0 ? (stage.count / data[index - 1].count) * 100 : 100,
    dropoffCount: index > 0 ? data[index - 1].count - stage.count : 0,
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Sales Funnel Analysis</CardTitle>
        <CardDescription>
          Track conversion rates through your sales pipeline
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {funnelData.map((stage, index) => (
            <motion.div
              key={stage.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: interactive ? 1.02 : 1 }}
              className={cn(
                "relative cursor-pointer transition-all",
                selectedStage === stage.id && "scale-105 z-10"
              )}
              onClick={() => interactive && setSelectedStage(
                selectedStage === stage.id ? null : stage.id
              )}
            >
              <FunnelStage 
                stage={stage} 
                index={index} 
                total={funnelData[0].count}
                isSelected={selectedStage === stage.id}
              />
            </motion.div>
          ))}
        </div>

        {/* Stage details */}
        <AnimatePresence>
          {selectedStage && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 p-4 bg-muted rounded-lg"
            >
              <FunnelStageDetails 
                stage={funnelData.find(s => s.id === selectedStage)} 
              />
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}

const FunnelStage = ({ stage, index, total, isSelected }) => {
  const widthPercentage = (stage.count / total) * 100;
  const conversionColor = stage.conversionRate > 80 ? 'green' : 
                         stage.conversionRate > 60 ? 'yellow' : 'red';

  return (
    <div className="relative">
      {/* Stage bar */}
      <div className="h-16 flex items-center relative overflow-hidden rounded-lg border">
        <div 
          className={cn(
            "h-full transition-all duration-300 flex items-center justify-center",
            "bg-gradient-to-r from-blue-500 to-purple-600",
            isSelected && "opacity-100",
            !isSelected && "opacity-80"
          )}
          style={{ width: `${widthPercentage}%` }}
        >
          <div className="text-white font-medium text-sm px-4 truncate">
            {stage.name}
          </div>
        </div>
        
        {/* Remaining area */}
        <div 
          className="h-full bg-muted flex items-center justify-end px-4"
          style={{ width: `${100 - widthPercentage}%` }}
        >
          <span className="text-xs text-muted-foreground">
            {stage.count} / {total}
          </span>
        </div>
      </div>

      {/* Conversion rate indicator */}
      {index > 0 && (
        <div className="absolute -right-2 top-1/2 -translate-y-1/2 
                        bg-background border rounded-full px-2 py-1">
          <span className={cn(
            "text-xs font-medium",
            conversionColor === 'green' && "text-green-600",
            conversionColor === 'yellow' && "text-yellow-600",
            conversionColor === 'red' && "text-red-600"
          )}>
            {stage.conversionRate.toFixed(1)}%
          </span>
        </div>
      )}

      {/* Connection line to next stage */}
      {index < total - 1 && (
        <div className="absolute left-1/2 -bottom-2 w-0.5 h-4 bg-border" />
      )}
    </div>
  );
};
```

### 5. Real-Time Activity Feed
```tsx
// components/charts/activity-feed.tsx
export function ActivityFeed() {
  const { data: activities, isLoading } = useRealtimeActivities();
  const [filter, setFilter] = useState('all');

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            Live Activity Feed
          </CardTitle>
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="deals">Deals</SelectItem>
              <SelectItem value="contacts">Contacts</SelectItem>
              <SelectItem value="meetings">Meetings</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-80 overflow-y-auto">
          <AnimatePresence initial={false}>
            {activities.map((activity) => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                layout
                className="flex items-start gap-3 p-3 hover:bg-muted/50 
                           rounded-lg transition-colors"
              >
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
                  getActivityColor(activity.type)
                )}>
                  <ActivityIcon type={activity.type} className="w-4 h-4" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {activity.title}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {activity.description}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-muted-foreground">
                      {formatRelativeTime(activity.createdAt)}
                    </span>
                    <span className="w-1 h-1 bg-muted-foreground rounded-full" />
                    <span className="text-xs text-muted-foreground">
                      {activity.user.name}
                    </span>
                  </div>
                </div>

                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                  <ExternalLink className="w-3 h-3" />
                </Button>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </CardContent>
    </Card>
  );
}
```

### 6. Interactive KPI Cards
```tsx
// components/charts/kpi-card.tsx
export function KPICard({ 
  title, 
  value, 
  change, 
  trend, 
  sparklineData,
  onClick 
}) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Card 
        className="cursor-pointer transition-all hover:shadow-lg relative overflow-hidden"
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {title}
              </p>
              <div className="flex items-baseline gap-2">
                <h3 className="text-2xl font-bold">{value}</h3>
                <div className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                  trend === 'up' && "bg-green-100 text-green-600 dark:bg-green-950 dark:text-green-400",
                  trend === 'down' && "bg-red-100 text-red-600 dark:bg-red-950 dark:text-red-400",
                  trend === 'neutral' && "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
                )}>
                  <TrendIcon trend={trend} className="w-3 h-3" />
                  {change}
                </div>
              </div>
            </div>

            {/* Sparkline */}
            <div className="w-20 h-12">
              <Sparkline 
                data={sparklineData} 
                color={trend === 'up' ? '#10B981' : trend === 'down' ? '#EF4444' : '#6B7280'}
                animate={isHovered}
              />
            </div>
          </div>

          {/* Hover details */}
          <AnimatePresence>
            {isHovered && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute inset-0 bg-background/95 backdrop-blur-sm 
                           flex items-center justify-center"
              >
                <div className="text-center space-y-2">
                  <p className="text-sm font-medium">Click to explore</p>
                  <ArrowUpRight className="w-4 h-4 mx-auto text-muted-foreground" />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
}
```

### 7. Heatmap Component
```tsx
// components/charts/activity-heatmap.tsx
export function ActivityHeatmap({ data, metric = 'activity' }) {
  const [selectedCell, setSelectedCell] = useState(null);
  
  // Calculate intensity for color coding
  const maxValue = Math.max(...data.flat().map(d => d.value));
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Activity Heatmap</CardTitle>
        <CardDescription>
          Visualize activity patterns throughout the week
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Time labels */}
          <div className="grid grid-cols-8 gap-1 text-xs text-muted-foreground">
            <div></div>
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
              <div key={day} className="text-center">{day}</div>
            ))}
          </div>

          {/* Heatmap grid */}
          {data.map((hourData, hourIndex) => (
            <div key={hourIndex} className="grid grid-cols-8 gap-1">
              <div className="text-xs text-muted-foreground text-right py-2">
                {String(hourIndex).padStart(2, '0')}:00
              </div>
              {hourData.map((cell, dayIndex) => (
                <Tooltip key={`${hourIndex}-${dayIndex}`}>
                  <TooltipTrigger asChild>
                    <button
                      className={cn(
                        "h-8 w-full rounded transition-all hover:scale-110",
                        "border border-transparent hover:border-primary/50"
                      )}
                      style={{
                        backgroundColor: `hsl(${cell.value > 0 ? '142' : '0'} ${
                          cell.value > 0 ? '70%' : '0%'
                        } ${90 - (cell.value / maxValue) * 40}%)`
                      }}
                      onClick={() => setSelectedCell(cell)}
                    />
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-center space-y-1">
                      <p className="font-medium">
                        {getDayName(dayIndex)} at {String(hourIndex).padStart(2, '0')}:00
                      </p>
                      <p className="text-sm">
                        {cell.value} {metric}s
                      </p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          ))}

          {/* Color scale legend */}
          <div className="flex items-center justify-center gap-2 pt-4">
            <span className="text-xs text-muted-foreground">Less</span>
            <div className="flex gap-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <div
                  key={i}
                  className="w-3 h-3 rounded"
                  style={{
                    backgroundColor: `hsl(142 70% ${90 - i * 10}%)`
                  }}
                />
              ))}
            </div>
            <span className="text-xs text-muted-foreground">More</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

## Chart Customization System

### Drag-and-Drop Dashboard Builder
```tsx
// components/charts/dashboard-builder.tsx
export function DashboardBuilder() {
  const [layout, setLayout] = useState(defaultLayout);
  const [availableCharts, setAvailableCharts] = useState(chartLibrary);

  return (
    <div className="h-screen flex">
      {/* Chart Library Sidebar */}
      <div className="w-80 border-r bg-muted/30 p-4">
        <h3 className="font-semibold mb-4">Available Charts</h3>
        <div className="space-y-2">
          {availableCharts.map(chart => (
            <Draggable key={chart.id} id={chart.id}>
              <ChartPreview chart={chart} />
            </Draggable>
          ))}
        </div>
      </div>

      {/* Dashboard Canvas */}
      <div className="flex-1 p-6">
        <Droppable id="dashboard">
          <DashboardGrid layout={layout} onLayoutChange={setLayout} />
        </Droppable>
      </div>
    </div>
  );
}
```

This enhanced data visualization system provides rich, interactive, and mobile-optimized charts that help users gain meaningful insights from their CRM data while maintaining excellent performance across all devices.