# Progressive Disclosure Forms Design

## Problem Statement
Current forms overwhelm users with 10+ fields displayed simultaneously, creating cognitive overload and poor completion rates. Users abandon forms due to complexity and unclear field relationships.

## Design Philosophy
**Progressive Disclosure**: Reveal information and options gradually to reduce cognitive load while maintaining full functionality for power users.

**Smart Defaults**: Use intelligent defaults based on context, user history, and common patterns to minimize required input.

**Contextual Guidance**: Provide inline help and field descriptions exactly when and where users need them.

## Form Architecture Patterns

### 1. Multi-Step Wizard Pattern

#### Use Cases
- New contact/company creation
- Deal/opportunity setup
- User onboarding
- Complex data imports

#### Implementation Structure
```tsx
// Multi-step form hook
interface StepConfig {
  id: string;
  title: string;
  description?: string;
  fields: string[];
  validation: ValidationSchema;
  optional?: boolean;
}

const useMultiStepForm = (steps: StepConfig[]) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(prev => prev + 1);
    }
  };
  
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };
  
  return {
    currentStep,
    steps,
    formData,
    setFormData,
    nextStep,
    prevStep,
    completedSteps,
    isFirstStep: currentStep === 0,
    isLastStep: currentStep === steps.length - 1,
    progress: ((currentStep + 1) / steps.length) * 100
  };
};
```

#### Step Progress Component
```tsx
// components/forms/step-progress.tsx
export function StepProgress({ 
  steps, 
  currentStep, 
  completedSteps 
}: StepProgressProps) {
  return (
    <div className="mb-8">
      {/* Mobile: Simple progress bar */}
      <div className="md:hidden">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-muted-foreground">
            Step {currentStep + 1} of {steps.length}
          </span>
          <span className="text-sm font-medium">
            {Math.round(((currentStep + 1) / steps.length) * 100)}%
          </span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div 
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Desktop: Detailed step indicators */}
      <div className="hidden md:block">
        <nav aria-label="Progress">
          <ol className="flex items-center">
            {steps.map((step, index) => {
              const isCompleted = completedSteps.has(index);
              const isCurrent = currentStep === index;
              
              return (
                <li key={step.id} className="flex items-center">
                  <div className={cn(
                    "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all",
                    isCompleted && "bg-primary border-primary text-primary-foreground",
                    isCurrent && !isCompleted && "border-primary text-primary bg-primary/10",
                    !isCompleted && !isCurrent && "border-muted-foreground/25 text-muted-foreground"
                  )}>
                    {isCompleted ? (
                      <Check className="w-5 h-5" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  
                  <div className="ml-4 min-w-0 flex-1">
                    <p className={cn(
                      "text-sm font-medium transition-colors",
                      (isCompleted || isCurrent) ? "text-foreground" : "text-muted-foreground"
                    )}>
                      {step.title}
                    </p>
                    {step.description && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {step.description}
                      </p>
                    )}
                  </div>
                  
                  {index < steps.length - 1 && (
                    <div className={cn(
                      "w-16 h-0.5 ml-4 transition-colors",
                      isCompleted ? "bg-primary" : "bg-muted-foreground/25"
                    )} />
                  )}
                </li>
              );
            })}
          </ol>
        </nav>
      </div>
    </div>
  );
}
```

### 2. Collapsible Sections Pattern

#### Advanced Fields Section
```tsx
// components/forms/collapsible-section.tsx
export function CollapsibleSection({ 
  title, 
  description, 
  children, 
  defaultOpen = false,
  badge
}: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  
  return (
    <div className="border rounded-lg overflow-hidden">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-4 py-3 flex items-center justify-between 
                   bg-muted/50 hover:bg-muted transition-colors text-left"
      >
        <div className="flex items-center space-x-3">
          <div className={cn(
            "flex items-center justify-center w-6 h-6 rounded transition-transform",
            isOpen && "rotate-90"
          )}>
            <ChevronRight className="w-4 h-4" />
          </div>
          <div>
            <h3 className="font-medium">{title}</h3>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>
        </div>
        
        {badge && (
          <Badge variant="secondary" className="ml-2">
            {badge}
          </Badge>
        )}
      </button>
      
      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-4 border-t bg-background">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
```

### 3. Smart Field Revelation

#### Context-Aware Field Display
```tsx
// Smart field revelation based on previous selections
export function SmartFormFields({ formData, onFieldChange }) {
  const [revealedFields, setRevealedFields] = useState<Set<string>>(new Set(['name', 'email']));
  
  useEffect(() => {
    const newFields = new Set(revealedFields);
    
    // Reveal company fields if contact type is business
    if (formData.contactType === 'business') {
      newFields.add('companyName');
      newFields.add('jobTitle');
      newFields.add('industry');
    }
    
    // Reveal address fields if shipping needed
    if (formData.needsShipping) {
      newFields.add('address');
      newFields.add('city');
      newFields.add('postalCode');
    }
    
    // Reveal social fields if professional contact
    if (formData.relationship === 'professional') {
      newFields.add('linkedinUrl');
      newFields.add('website');
    }
    
    setRevealedFields(newFields);
  }, [formData, revealedFields]);
  
  return (
    <div className="space-y-4">
      {/* Always visible core fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField name="firstName" label="First Name" required />
        <FormField name="lastName" label="Last Name" required />
        <FormField name="email" label="Email" type="email" required />
        <FormField name="phone" label="Phone" type="tel" />
      </div>
      
      {/* Context trigger fields */}
      <div className="space-y-4">
        <FormField 
          name="contactType" 
          label="Contact Type"
          type="select"
          options={[
            { value: 'personal', label: 'Personal Contact' },
            { value: 'business', label: 'Business Contact' }
          ]}
        />
        
        <FormField 
          name="relationship" 
          label="Relationship"
          type="select"
          options={[
            { value: 'personal', label: 'Personal' },
            { value: 'professional', label: 'Professional' },
            { value: 'vendor', label: 'Vendor' },
            { value: 'client', label: 'Client' }
          ]}
        />
      </div>
      
      {/* Conditionally revealed fields with animation */}
      <AnimatePresence>
        {Array.from(revealedFields).map(fieldName => {
          if (['name', 'email', 'phone', 'contactType', 'relationship'].includes(fieldName)) {
            return null; // Already shown above
          }
          
          return (
            <motion.div
              key={fieldName}
              initial={{ height: 0, opacity: 0, y: -10 }}
              animate={{ height: "auto", opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <ConditionalField fieldName={fieldName} />
            </motion.div>
          );
        })}
      </AnimatePresence>
      
      {/* Field suggestion buttons */}
      <div className="flex flex-wrap gap-2 pt-4">
        {getAvailableFields(formData, revealedFields).map(field => (
          <Button
            key={field.name}
            variant="outline"
            size="sm"
            onClick={() => setRevealedFields(prev => new Set([...prev, field.name]))}
            className="text-xs"
          >
            <Plus className="w-3 h-3 mr-1" />
            Add {field.label}
          </Button>
        ))}
      </div>
    </div>
  );
}
```

## Form Component Patterns

### 1. Enhanced Form Field Component
```tsx
// components/forms/enhanced-form-field.tsx
interface EnhancedFormFieldProps {
  name: string;
  label: string;
  type?: string;
  required?: boolean;
  help?: string;
  placeholder?: string;
  suggestions?: string[];
  validation?: ValidationRule;
  showCharCount?: boolean;
  maxLength?: number;
}

export function EnhancedFormField({ 
  name, 
  label, 
  type = "text", 
  required, 
  help, 
  suggestions,
  showCharCount,
  maxLength,
  ...props 
}: EnhancedFormFieldProps) {
  const [focused, setFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { formState: { errors }, watch, setValue } = useFormContext();
  
  const value = watch(name);
  const error = errors[name];
  
  return (
    <div className="space-y-2">
      {/* Label with required indicator */}
      <div className="flex items-center justify-between">
        <Label htmlFor={name} className="text-sm font-medium">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>
        
        {showCharCount && maxLength && (
          <span className={cn(
            "text-xs transition-colors",
            (value?.length || 0) > maxLength * 0.9 
              ? "text-warning" 
              : "text-muted-foreground"
          )}>
            {value?.length || 0}/{maxLength}
          </span>
        )}
      </div>
      
      {/* Input with enhanced styling */}
      <div className="relative">
        <Input
          id={name}
          type={type}
          onFocus={() => {
            setFocused(true);
            if (suggestions?.length) setShowSuggestions(true);
          }}
          onBlur={() => {
            setFocused(false);
            setTimeout(() => setShowSuggestions(false), 200);
          }}
          className={cn(
            "transition-all duration-200",
            focused && "ring-2 ring-primary/20 border-primary",
            error && "border-destructive focus:border-destructive ring-destructive/20"
          )}
          {...props}
        />
        
        {/* Auto-suggestions dropdown */}
        {showSuggestions && suggestions && suggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 z-50 mt-1 
                          bg-popover border rounded-md shadow-lg max-h-48 overflow-y-auto">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                type="button"
                onClick={() => {
                  setValue(name, suggestion);
                  setShowSuggestions(false);
                }}
                className="w-full px-3 py-2 text-left hover:bg-muted 
                           text-sm transition-colors"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>
      
      {/* Help text and errors */}
      <AnimatePresence mode="wait">
        {error ? (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-sm text-destructive flex items-center"
          >
            <AlertCircle className="w-4 h-4 mr-1" />
            {error.message}
          </motion.p>
        ) : help && focused ? (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-sm text-muted-foreground flex items-center"
          >
            <Info className="w-4 h-4 mr-1" />
            {help}
          </motion.p>
        ) : null}
      </AnimatePresence>
    </div>
  );
}
```

### 2. Smart Default System
```tsx
// Smart defaults based on context and user history
export function useSmartDefaults(formType: string, context?: any) {
  const { user } = useAuth();
  const { data: userHistory } = useUserHistory();
  
  const getDefaults = useCallback(() => {
    const defaults: Record<string, any> = {};
    
    switch (formType) {
      case 'contact':
        // Use company info if creating from company page
        if (context?.companyId) {
          defaults.companyId = context.companyId;
          defaults.industry = context.companyIndustry;
        }
        
        // Use most common values from user history
        if (userHistory?.contacts) {
          defaults.contactType = userHistory.contacts.mostCommonType;
          defaults.source = userHistory.contacts.mostCommonSource;
        }
        
        // Default country/timezone from user profile
        defaults.country = user?.country || 'US';
        defaults.timezone = user?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
        break;
        
      case 'deal':
        if (context?.contactId) {
          defaults.contactId = context.contactId;
          defaults.companyId = context.companyId;
        }
        
        // Use typical deal values
        defaults.currency = user?.defaultCurrency || 'USD';
        defaults.pipelineId = userHistory?.deals?.preferredPipeline;
        defaults.probability = 25; // Default probability for new deals
        
        // Auto-set close date to 30 days from now
        defaults.expectedCloseDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
          .toISOString().split('T')[0];
        break;
    }
    
    return defaults;
  }, [formType, context, user, userHistory]);
  
  return getDefaults();
}
```

### 3. Field Groups with Smart Layout
```tsx
// components/forms/field-group.tsx
export function FieldGroup({ 
  title, 
  description, 
  children, 
  columns = 1, 
  priority = 'normal' 
}: FieldGroupProps) {
  return (
    <div className={cn(
      "space-y-4 p-4 rounded-lg border",
      priority === 'high' && "border-primary/20 bg-primary/5",
      priority === 'normal' && "border-border bg-card"
    )}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className={cn(
              "font-medium",
              priority === 'high' ? "text-primary" : "text-foreground"
            )}>
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      
      <div className={cn(
        "grid gap-4",
        columns === 1 && "grid-cols-1",
        columns === 2 && "grid-cols-1 md:grid-cols-2",
        columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
      )}>
        {children}
      </div>
    </div>
  );
}
```

## Form Templates

### 1. Contact Creation Form
```tsx
// Contact form with progressive disclosure
const contactFormSteps = [
  {
    id: 'basic',
    title: 'Basic Information',
    description: 'Essential contact details',
    fields: ['firstName', 'lastName', 'email', 'phone'],
    validation: basicContactSchema
  },
  {
    id: 'professional',
    title: 'Professional Details',
    description: 'Work-related information',
    fields: ['jobTitle', 'companyId', 'department', 'industry'],
    validation: professionalContactSchema,
    optional: true
  },
  {
    id: 'additional',
    title: 'Additional Information',
    description: 'Extra details and preferences',
    fields: ['notes', 'tags', 'source', 'socialProfiles'],
    validation: additionalContactSchema,
    optional: true
  }
];

export function ContactCreationForm() {
  const form = useMultiStepForm(contactFormSteps);
  const smartDefaults = useSmartDefaults('contact');
  
  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create New Contact</CardTitle>
        <CardDescription>
          Add a new contact to your CRM system
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <StepProgress 
          steps={form.steps} 
          currentStep={form.currentStep}
          completedSteps={form.completedSteps}
        />
        
        <Form {...form}>
          <motion.div
            key={form.currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {form.currentStep === 0 && <BasicContactStep />}
            {form.currentStep === 1 && <ProfessionalContactStep />}
            {form.currentStep === 2 && <AdditionalContactStep />}
          </motion.div>
        </Form>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={form.prevStep}
          disabled={form.isFirstStep}
        >
          Previous
        </Button>
        
        <div className="space-x-2">
          {!form.isLastStep && form.steps[form.currentStep].optional && (
            <Button 
              variant="ghost" 
              onClick={form.nextStep}
            >
              Skip
            </Button>
          )}
          
          <Button 
            onClick={form.isLastStep ? form.handleSubmit : form.nextStep}
          >
            {form.isLastStep ? 'Create Contact' : 'Continue'}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
```

### 2. Deal Creation with Context
```tsx
// Deal form with context-aware field revelation
export function DealCreationForm({ contactId, companyId }: DealFormProps) {
  const [formData, setFormData] = useState(getSmartDefaults());
  const [activeSection, setActiveSection] = useState('basic');
  
  return (
    <div className="space-y-6">
      {/* Quick creation section */}
      <FieldGroup 
        title="Deal Essentials" 
        description="The minimum required information"
        priority="high"
        columns={2}
      >
        <EnhancedFormField 
          name="title" 
          label="Deal Title" 
          required 
          placeholder="e.g., Q4 Software License"
          help="Be descriptive to identify the deal quickly"
        />
        
        <EnhancedFormField 
          name="value" 
          label="Deal Value" 
          type="currency"
          required 
          placeholder="0.00"
        />
        
        <EnhancedFormField 
          name="pipelineId" 
          label="Sales Pipeline" 
          type="select"
          required
          suggestions={recentPipelines}
        />
        
        <EnhancedFormField 
          name="stageId" 
          label="Current Stage" 
          type="select"
          required
        />
      </FieldGroup>
      
      {/* Expandable sections */}
      <CollapsibleSection 
        title="Contact & Company Details"
        description="Associate this deal with contacts and companies"
        badge={contactId ? "Pre-filled" : undefined}
      >
        <ContactCompanyFields contactId={contactId} companyId={companyId} />
      </CollapsibleSection>
      
      <CollapsibleSection 
        title="Timeline & Probability"
        description="Expected close date and success probability"
      >
        <TimelineProbabilityFields />
      </CollapsibleSection>
      
      <CollapsibleSection 
        title="Additional Information"
        description="Notes, tags, and custom fields"
      >
        <AdditionalDealFields />
      </CollapsibleSection>
    </div>
  );
}
```

This progressive disclosure system reduces cognitive load while maintaining full functionality, creating a more intuitive and efficient form experience for users.