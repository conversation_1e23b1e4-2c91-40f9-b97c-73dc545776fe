# State Management Patterns Design

## Problem Statement
Inconsistent state handling across components leads to UI glitches, poor loading experiences, and confusing error states. Users encounter empty screens, eternal loading spinners, and unclear error messages without actionable solutions.

## Design Philosophy
**Predictable States**: Every UI element has well-defined states and transitions
**Graceful Degradation**: Components work partially when data is unavailable
**Progressive Loading**: Show data as it becomes available
**Error Recovery**: Clear paths for users to resolve errors
**Optimistic Updates**: Immediate feedback with rollback capability

## State Architecture

### Component State Types
```typescript
// Base state interface for all components
interface ComponentState<TData = any, TError = Error> {
  // Data state
  data: TData | null;
  
  // Loading states
  isLoading: boolean;
  isValidating: boolean;
  isRefreshing: boolean;
  isMutating: boolean;
  
  // Error states
  error: TError | null;
  hasError: boolean;
  
  // UI states
  isEmpty: boolean;
  isStale: boolean;
  lastUpdated: Date | null;
  
  // Interaction states
  isDisabled: boolean;
  isReadOnly: boolean;
  
  // Network states
  isOnline: boolean;
  retryCount: number;
  canRetry: boolean;
}

// Specialized state for data fetching
interface DataFetchState<T> extends ComponentState<T> {
  isInitialLoading: boolean;
  isLoadingMore: boolean;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
}

// Specialized state for mutations
interface MutationState<T, E = Error> {
  data: T | null;
  error: E | null;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  submittedAt: Date | null;
  reset: () => void;
}
```

## Loading State Patterns

### 1. Progressive Loading Component
```tsx
// components/states/progressive-loader.tsx
interface ProgressiveLoaderProps<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  isEmpty?: boolean;
  children: (data: T) => React.ReactNode;
  loadingState?: React.ReactNode;
  errorState?: (error: Error, retry: () => void) => React.ReactNode;
  emptyState?: React.ReactNode;
  skeletonLines?: number;
  showStaleData?: boolean;
  retryFn?: () => void;
}

export function ProgressiveLoader<T>({
  data,
  isLoading,
  error,
  isEmpty = false,
  children,
  loadingState,
  errorState,
  emptyState,
  skeletonLines = 3,
  showStaleData = true,
  retryFn
}: ProgressiveLoaderProps<T>) {
  // Show stale data while revalidating
  if (data && isLoading && showStaleData) {
    return (
      <div className="relative">
        {children(data)}
        <div className="absolute top-2 right-2">
          <div className="flex items-center space-x-2 bg-background/80 backdrop-blur-sm 
                          border rounded-full px-3 py-1">
            <Loader2 className="w-3 h-3 animate-spin" />
            <span className="text-xs text-muted-foreground">Updating...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state with retry option
  if (error && !data) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        {errorState ? (
          errorState(error, retryFn || (() => {}))
        ) : (
          <>
            <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-destructive" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium text-destructive">Something went wrong</h3>
              <p className="text-sm text-muted-foreground max-w-sm">
                {error.message || 'An unexpected error occurred'}
              </p>
            </div>
            {retryFn && (
              <Button onClick={retryFn} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            )}
          </>
        )}
      </div>
    );
  }

  // Empty state
  if (isEmpty && !isLoading && !error) {
    return (
      <div className="flex items-center justify-center py-12">
        {emptyState || (
          <div className="text-center space-y-2">
            <Inbox className="w-8 h-8 text-muted-foreground mx-auto" />
            <p className="text-sm text-muted-foreground">No data available</p>
          </div>
        )}
      </div>
    );
  }

  // Loading state
  if (isLoading && !data) {
    return (
      <div className="space-y-4">
        {loadingState || (
          <div className="space-y-3">
            {Array.from({ length: skeletonLines }, (_, i) => (
              <div
                key={i}
                className={cn(
                  "h-4 bg-muted rounded animate-pulse",
                  i === skeletonLines - 1 ? "w-3/4" : "w-full"
                )}
                style={{ animationDelay: `${i * 100}ms` }}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  // Success state with data
  if (data) {
    return <>{children(data)}</>;
  }

  // Fallback
  return null;
}
```

### 2. Smart Loading States Hook
```tsx
// hooks/use-smart-loading.ts
interface SmartLoadingConfig {
  minLoadingTime?: number;
  showSkeletonAfter?: number;
  showProgressAfter?: number;
  progressSteps?: string[];
}

export function useSmartLoading(
  isLoading: boolean,
  config: SmartLoadingConfig = {}
) {
  const {
    minLoadingTime = 500,
    showSkeletonAfter = 200,
    showProgressAfter = 2000,
    progressSteps = ['Loading...', 'Still loading...', 'Almost done...']
  } = config;

  const [loadingState, setLoadingState] = useState<'idle' | 'skeleton' | 'progress'>('idle');
  const [currentStep, setCurrentStep] = useState(0);
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    let skeletonTimer: NodeJS.Timeout;
    let progressTimer: NodeJS.Timeout;
    let stepTimer: NodeJS.Timeout;
    let minTimeTimer: NodeJS.Timeout;

    if (isLoading) {
      // Show skeleton after delay
      skeletonTimer = setTimeout(() => {
        setLoadingState('skeleton');
        setShouldShow(true);
      }, showSkeletonAfter);

      // Show progress after longer delay
      progressTimer = setTimeout(() => {
        setLoadingState('progress');
      }, showProgressAfter);

      // Cycle through progress steps
      if (showProgressAfter > 0) {
        stepTimer = setInterval(() => {
          setCurrentStep(prev => (prev + 1) % progressSteps.length);
        }, 1000);
      }
    } else if (shouldShow) {
      // Ensure minimum loading time for smooth UX
      minTimeTimer = setTimeout(() => {
        setShouldShow(false);
        setLoadingState('idle');
        setCurrentStep(0);
      }, minLoadingTime);
    }

    return () => {
      clearTimeout(skeletonTimer);
      clearTimeout(progressTimer);
      clearTimeout(minTimeTimer);
      clearInterval(stepTimer);
    };
  }, [isLoading, shouldShow, showSkeletonAfter, showProgressAfter, minLoadingTime, progressSteps.length]);

  return {
    shouldShow: shouldShow || isLoading,
    loadingState,
    currentStep,
    progressMessage: progressSteps[currentStep]
  };
}
```

### 3. Optimistic Updates Hook
```tsx
// hooks/use-optimistic-update.ts
interface OptimisticUpdateConfig<T> {
  mutationFn: (variables: any) => Promise<T>;
  onSuccess?: (data: T, variables: any) => void;
  onError?: (error: Error, variables: any) => void;
  rollbackDelay?: number;
  successMessage?: string;
  errorMessage?: string;
}

export function useOptimisticUpdate<T, TVariables = any>(
  queryKey: string[],
  updateFn: (previousData: T, variables: TVariables) => T,
  config: OptimisticUpdateConfig<T>
) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [rollbackTimer, setRollbackTimer] = useState<NodeJS.Timeout | null>(null);

  const mutation = useMutation({
    mutationFn: config.mutationFn,
    onMutate: async (variables: TVariables) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey });

      // Snapshot previous value
      const previousData = queryClient.getQueryData<T>(queryKey);

      // Optimistically update
      if (previousData) {
        const optimisticData = updateFn(previousData, variables);
        queryClient.setQueryData(queryKey, optimisticData);
      }

      // Return rollback function
      return { previousData, variables };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(queryKey, context.previousData);
      }

      toast({
        title: "Error",
        description: config.errorMessage || error.message,
        variant: "destructive"
      });

      config.onError?.(error, variables);
    },
    onSuccess: (data, variables, context) => {
      // Clear any pending rollback
      if (rollbackTimer) {
        clearTimeout(rollbackTimer);
        setRollbackTimer(null);
      }

      if (config.successMessage) {
        toast({
          title: "Success",
          description: config.successMessage,
          variant: "default"
        });
      }

      config.onSuccess?.(data, variables);
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey });
    }
  });

  const optimisticMutate = (variables: TVariables, rollbackAfter?: number) => {
    mutation.mutate(variables);

    // Auto-rollback after delay if no response
    if (rollbackAfter && rollbackAfter > 0) {
      const timer = setTimeout(() => {
        const currentData = queryClient.getQueryData<T>(queryKey);
        if (currentData && !mutation.isSuccess && !mutation.isError) {
          // Rollback optimistic update
          queryClient.invalidateQueries({ queryKey });
          toast({
            title: "Network Timeout",
            description: "The operation is taking longer than expected",
            variant: "destructive"
          });
        }
      }, rollbackAfter);

      setRollbackTimer(timer);
    }
  };

  return {
    ...mutation,
    optimisticMutate
  };
}
```

### 4. Error Boundary with Recovery
```tsx
// components/states/error-boundary.tsx
interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

export class ErrorBoundary extends Component<
  {
    children: ReactNode;
    fallback?: (error: Error, reset: () => void) => ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    maxRetries?: number;
  },
  ErrorBoundaryState
> {
  private resetTimer: NodeJS.Timeout | null = null;

  constructor(props: any) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      errorInfo
    });

    // Log error to monitoring service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  handleReset = () => {
    const maxRetries = this.props.maxRetries ?? 3;
    
    if (this.state.retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));

      // Auto-reset retry count after successful recovery
      this.resetTimer = setTimeout(() => {
        this.setState({ retryCount: 0 });
      }, 30000);
    }
  };

  componentWillUnmount() {
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
    }
  }

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleReset);
      }

      return (
        <ErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          onReset={this.handleReset}
          retryCount={this.state.retryCount}
          maxRetries={this.props.maxRetries ?? 3}
        />
      );
    }

    return this.props.children;
  }
}

function ErrorFallback({
  error,
  errorInfo,
  onReset,
  retryCount,
  maxRetries
}: {
  error: Error;
  errorInfo: ErrorInfo | null;
  onReset: () => void;
  retryCount: number;
  maxRetries: number;
}) {
  const canRetry = retryCount < maxRetries;

  return (
    <Card className="max-w-2xl mx-auto my-8">
      <CardContent className="p-8">
        <div className="text-center space-y-6">
          <div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mx-auto">
            <AlertTriangle className="w-8 h-8 text-destructive" />
          </div>

          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-destructive">
              Something went wrong
            </h2>
            <p className="text-muted-foreground">
              An unexpected error occurred while rendering this component.
            </p>
          </div>

          {process.env.NODE_ENV === 'development' && (
            <details className="text-left bg-muted p-4 rounded-lg">
              <summary className="cursor-pointer font-medium mb-2">
                Error Details
              </summary>
              <pre className="text-xs overflow-auto whitespace-pre-wrap">
                {error.message}
                {error.stack}
                {errorInfo?.componentStack}
              </pre>
            </details>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {canRetry && (
              <Button onClick={onReset} variant="default">
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again ({maxRetries - retryCount} attempts left)
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reload Page
            </Button>
          </div>

          {!canRetry && (
            <p className="text-sm text-muted-foreground">
              Maximum retry attempts reached. Please reload the page or contact support.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

### 5. Network Status Management
```tsx
// hooks/use-network-status.ts
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionType, setConnectionType] = useState<string>('unknown');
  const [isSlowConnection, setIsSlowConnection] = useState(false);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check connection quality
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      setConnectionType(connection.effectiveType || 'unknown');
      setIsSlowConnection(['slow-2g', '2g', '3g'].includes(connection.effectiveType));

      const handleConnectionChange = () => {
        setConnectionType(connection.effectiveType || 'unknown');
        setIsSlowConnection(['slow-2g', '2g', '3g'].includes(connection.effectiveType));
      };

      connection.addEventListener('change', handleConnectionChange);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
        connection.removeEventListener('change', handleConnectionChange);
      };
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return {
    isOnline,
    connectionType,
    isSlowConnection,
    isOffline: !isOnline
  };
}

// Network status indicator component
export function NetworkStatusIndicator() {
  const { isOnline, isSlowConnection, connectionType } = useNetworkStatus();
  const [showIndicator, setShowIndicator] = useState(false);

  useEffect(() => {
    if (!isOnline || isSlowConnection) {
      setShowIndicator(true);
    } else {
      // Hide indicator after a delay when back online
      const timer = setTimeout(() => setShowIndicator(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [isOnline, isSlowConnection]);

  if (!showIndicator) return null;

  return (
    <motion.div
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      exit={{ y: -100 }}
      className="fixed top-4 right-4 z-50"
    >
      <Card className="border-2 border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
        <CardContent className="p-3">
          <div className="flex items-center space-x-2">
            {!isOnline ? (
              <>
                <WifiOff className="w-4 h-4 text-destructive" />
                <span className="text-sm font-medium text-destructive">
                  No internet connection
                </span>
              </>
            ) : (
              <>
                <Wifi className="w-4 h-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-600">
                  Slow connection detected ({connectionType})
                </span>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
```

### 6. State Persistence Hook
```tsx
// hooks/use-persistent-state.ts
export function usePersistentState<T>(
  key: string,
  defaultValue: T,
  storage: 'localStorage' | 'sessionStorage' = 'localStorage'
) {
  const storageObject = storage === 'localStorage' ? localStorage : sessionStorage;
  
  const [state, setState] = useState<T>(() => {
    try {
      const item = storageObject.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.warn(`Error reading ${key} from ${storage}:`, error);
      return defaultValue;
    }
  });

  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(state) : value;
      setState(valueToStore);
      storageObject.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.warn(`Error setting ${key} in ${storage}:`, error);
    }
  }, [key, state, storageObject]);

  const removeValue = useCallback(() => {
    try {
      setState(defaultValue);
      storageObject.removeItem(key);
    } catch (error) {
      console.warn(`Error removing ${key} from ${storage}:`, error);
    }
  }, [key, defaultValue, storageObject]);

  // Sync across tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.storageArea === storageObject) {
        try {
          const newValue = e.newValue ? JSON.parse(e.newValue) : defaultValue;
          setState(newValue);
        } catch (error) {
          console.warn(`Error syncing ${key} from storage event:`, error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, defaultValue, storageObject]);

  return [state, setValue, removeValue] as const;
}
```

This comprehensive state management system ensures consistent, predictable, and user-friendly handling of all application states, providing clear feedback and recovery options for users while maintaining performance and reliability.