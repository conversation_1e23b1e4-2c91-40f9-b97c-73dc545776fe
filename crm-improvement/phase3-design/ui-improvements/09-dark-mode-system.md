# Dark Mode System Design

## Problem Statement
Lack of dark mode support reduces usability in low-light environments and doesn't meet modern user expectations. The current system needs a comprehensive dark mode implementation with smooth transitions and intelligent auto-switching.

## Design Goals
- **Seamless Transitions**: Smooth animations between light and dark modes
- **System Integration**: Respect user's OS preference with manual override
- **Color Accessibility**: Maintain WCAG contrast ratios in both modes
- **Component Coverage**: All components support both themes consistently
- **Smart Switching**: Time-based auto-switching with location awareness
- **Persistence**: Remember user preference across sessions

## Dark Mode Architecture

### Color System Design
```typescript
// Color token structure for both modes
interface ColorTokens {
  // Base colors
  background: string;
  foreground: string;
  
  // Component colors
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  
  // Interactive colors
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  
  // State colors
  muted: string;
  mutedForeground: string;
  accent: string;
  accentForeground: string;
  
  // Semantic colors
  destructive: string;
  destructiveForeground: string;
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
  
  // UI elements
  border: string;
  input: string;
  ring: string;
  
  // Chart colors
  chart: {
    1: string;
    2: string;
    3: string;
    4: string;
    5: string;
  };
}

// Light mode color definitions
const lightColors: ColorTokens = {
  background: "0 0% 100%",           // White
  foreground: "222 84% 4%",          // Very dark blue
  
  card: "0 0% 100%",                 // White
  cardForeground: "222 84% 4%",      // Very dark blue
  
  popover: "0 0% 100%",              // White
  popoverForeground: "222 84% 4%",   // Very dark blue
  
  primary: "217 87% 55%",            // Blue 500
  primaryForeground: "210 40% 98%",  // Very light blue
  
  secondary: "210 40% 96%",          // Light gray
  secondaryForeground: "222 47% 11%", // Dark gray
  
  muted: "210 40% 96%",              // Light gray
  mutedForeground: "215 16% 47%",    // Medium gray
  
  accent: "210 40% 96%",             // Light gray
  accentForeground: "222 47% 11%",   // Dark gray
  
  destructive: "0 84% 60%",          // Red 500
  destructiveForeground: "210 40% 98%", // Very light
  
  success: "142 71% 45%",            // Green 600
  successForeground: "0 0% 100%",    // White
  
  warning: "43 96% 56%",             // Yellow 500
  warningForeground: "0 0% 0%",      // Black
  
  border: "214 32% 91%",             // Light border
  input: "214 32% 91%",              // Light input background
  ring: "217 87% 55%",               // Blue 500 for focus rings
  
  chart: {
    1: "217 87% 55%",                // Blue
    2: "142 71% 45%",                // Green
    3: "43 96% 56%",                 // Yellow
    4: "262 83% 58%",                // Purple
    5: "0 84% 60%",                  // Red
  }
};

// Dark mode color definitions
const darkColors: ColorTokens = {
  background: "222 84% 4%",          // Very dark blue
  foreground: "210 40% 98%",         // Very light blue
  
  card: "222 84% 4%",                // Very dark blue
  cardForeground: "210 40% 98%",     // Very light blue
  
  popover: "222 84% 4%",             // Very dark blue
  popoverForeground: "210 40% 98%",  // Very light blue
  
  primary: "217 87% 65%",            // Lighter blue for dark mode
  primaryForeground: "222 84% 4%",   // Very dark for contrast
  
  secondary: "217 33% 17%",          // Dark secondary
  secondaryForeground: "210 40% 98%", // Light text
  
  muted: "217 33% 17%",              // Dark muted
  mutedForeground: "215 20% 65%",    // Light muted text
  
  accent: "217 33% 17%",             // Dark accent
  accentForeground: "210 40% 98%",   // Light accent text
  
  destructive: "0 62% 50%",          // Slightly darker red
  destructiveForeground: "210 40% 98%", // Light text
  
  success: "142 71% 55%",            // Brighter green for dark mode
  successForeground: "222 84% 4%",   // Dark text
  
  warning: "43 96% 66%",             // Brighter yellow for dark mode
  warningForeground: "222 84% 4%",   // Dark text
  
  border: "217 33% 17%",             // Dark border
  input: "217 33% 17%",              // Dark input background
  ring: "217 87% 65%",               // Lighter blue for focus rings
  
  chart: {
    1: "217 87% 65%",                // Lighter blue
    2: "142 71% 55%",                // Brighter green
    3: "43 96% 66%",                 // Brighter yellow
    4: "262 83% 68%",                // Lighter purple
    5: "0 62% 50%",                  // Adjusted red
  }
};
```

### Theme Provider Implementation
```tsx
// components/theme/theme-provider.tsx
type Theme = 'light' | 'dark' | 'system';

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  enableTransitions?: boolean;
  autoSwitch?: boolean;
  location?: { latitude: number; longitude: number };
}

interface ThemeProviderState {
  theme: Theme;
  resolvedTheme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isTransitioning: boolean;
}

const ThemeProviderContext = createContext<ThemeProviderState | undefined>(undefined);

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'ui-theme',
  enableTransitions = true,
  autoSwitch = false,
  location,
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      return (localStorage.getItem(storageKey) as Theme) || defaultTheme;
    }
    return defaultTheme;
  });

  const [isTransitioning, setIsTransitioning] = useState(false);
  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');

  // Resolve system theme
  const getSystemTheme = useCallback((): 'light' | 'dark' => {
    if (typeof window === 'undefined') return 'light';
    
    // Check if auto-switch is enabled and we have location
    if (autoSwitch && location) {
      const now = new Date();
      const sunrise = getSunriseTime(location, now);
      const sunset = getSunsetTime(location, now);
      const currentTime = now.getHours() * 60 + now.getMinutes();
      
      // Use dark mode between sunset and sunrise
      if (currentTime < sunrise || currentTime > sunset) {
        return 'dark';
      } else {
        return 'light';
      }
    }
    
    // Fall back to system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }, [autoSwitch, location]);

  // Update resolved theme
  useEffect(() => {
    const newResolvedTheme = theme === 'system' ? getSystemTheme() : theme;
    
    if (newResolvedTheme !== resolvedTheme) {
      if (enableTransitions) {
        setIsTransitioning(true);
        // Brief transition delay
        setTimeout(() => setIsTransitioning(false), 150);
      }
      setResolvedTheme(newResolvedTheme);
    }
  }, [theme, getSystemTheme, resolvedTheme, enableTransitions]);

  // Apply theme to document
  useEffect(() => {
    const root = window.document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark');
    
    // Add new theme class
    root.classList.add(resolvedTheme);
    
    // Add transition class if enabled
    if (enableTransitions && isTransitioning) {
      root.classList.add('theme-transitioning');
      setTimeout(() => {
        root.classList.remove('theme-transitioning');
      }, 150);
    }

    // Update CSS custom properties
    const colors = resolvedTheme === 'dark' ? darkColors : lightColors;
    Object.entries(colors).forEach(([key, value]) => {
      if (typeof value === 'object') {
        Object.entries(value).forEach(([subKey, subValue]) => {
          root.style.setProperty(`--${key}-${subKey}`, subValue);
        });
      } else {
        root.style.setProperty(`--${key}`, value);
      }
    });
  }, [resolvedTheme, enableTransitions, isTransitioning]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      if (theme === 'system') {
        const newSystemTheme = getSystemTheme();
        if (newSystemTheme !== resolvedTheme) {
          setResolvedTheme(newSystemTheme);
        }
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme, resolvedTheme, getSystemTheme]);

  // Auto-switch based on time (if enabled)
  useEffect(() => {
    if (!autoSwitch || !location || theme !== 'system') return;

    const checkTime = () => {
      const newTheme = getSystemTheme();
      if (newTheme !== resolvedTheme) {
        setResolvedTheme(newTheme);
      }
    };

    // Check every minute
    const interval = setInterval(checkTime, 60000);
    return () => clearInterval(interval);
  }, [autoSwitch, location, theme, resolvedTheme, getSystemTheme]);

  const updateTheme = (newTheme: Theme) => {
    setTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem(storageKey, newTheme);
    }
  };

  const toggleTheme = () => {
    if (theme === 'system') {
      updateTheme(resolvedTheme === 'dark' ? 'light' : 'dark');
    } else {
      updateTheme(theme === 'dark' ? 'light' : 'dark');
    }
  };

  const value: ThemeProviderState = {
    theme,
    resolvedTheme,
    setTheme: updateTheme,
    toggleTheme,
    isTransitioning
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
```

### Theme Toggle Component
```tsx
// components/theme/theme-toggle.tsx
export function ThemeToggle({ 
  variant = 'icon', 
  showLabel = false,
  size = 'default'
}: {
  variant?: 'icon' | 'select' | 'switch';
  showLabel?: boolean;
  size?: 'sm' | 'default' | 'lg';
}) {
  const { theme, resolvedTheme, setTheme, toggleTheme, isTransitioning } = useTheme();

  if (variant === 'select') {
    return (
      <div className="flex items-center space-x-2">
        {showLabel && <label className="text-sm font-medium">Theme</label>}
        <Select value={theme} onValueChange={setTheme}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="light">
              <div className="flex items-center space-x-2">
                <Sun className="w-4 h-4" />
                <span>Light</span>
              </div>
            </SelectItem>
            <SelectItem value="dark">
              <div className="flex items-center space-x-2">
                <Moon className="w-4 h-4" />
                <span>Dark</span>
              </div>
            </SelectItem>
            <SelectItem value="system">
              <div className="flex items-center space-x-2">
                <Monitor className="w-4 h-4" />
                <span>System</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    );
  }

  if (variant === 'switch') {
    return (
      <div className="flex items-center space-x-2">
        {showLabel && <label className="text-sm font-medium">Dark mode</label>}
        <Switch
          checked={resolvedTheme === 'dark'}
          onCheckedChange={() => toggleTheme()}
          disabled={isTransitioning}
        />
      </div>
    );
  }

  // Icon button variant
  return (
    <Button
      variant="ghost"
      size={size}
      onClick={toggleTheme}
      disabled={isTransitioning}
      className={cn(
        "relative overflow-hidden",
        isTransitioning && "pointer-events-none"
      )}
      aria-label={`Switch to ${resolvedTheme === 'light' ? 'dark' : 'light'} mode`}
    >
      <div className="relative w-4 h-4">
        <Sun 
          className={cn(
            "absolute inset-0 w-4 h-4 transition-all duration-300",
            resolvedTheme === 'dark' 
              ? "rotate-90 scale-0 opacity-0" 
              : "rotate-0 scale-100 opacity-100"
          )} 
        />
        <Moon 
          className={cn(
            "absolute inset-0 w-4 h-4 transition-all duration-300",
            resolvedTheme === 'dark' 
              ? "rotate-0 scale-100 opacity-100" 
              : "-rotate-90 scale-0 opacity-0"
          )} 
        />
      </div>
      
      {showLabel && (
        <span className="ml-2 text-sm">
          {resolvedTheme === 'light' ? 'Light' : 'Dark'}
        </span>
      )}
    </Button>
  );
}
```

### CSS Transition System
```css
/* styles/theme-transitions.css */

/* Enable smooth transitions between themes */
.theme-transitioning * {
  transition: 
    background-color 150ms ease,
    border-color 150ms ease,
    color 150ms ease,
    fill 150ms ease,
    stroke 150ms ease,
    box-shadow 150ms ease !important;
}

/* Prevent flash of unstyled content */
html:not(.light):not(.dark) {
  color-scheme: light;
}

/* System color scheme support */
@media (prefers-color-scheme: dark) {
  html:not(.light):not(.dark) {
    color-scheme: dark;
  }
}

/* Theme-specific component adjustments */
.light {
  color-scheme: light;
}

.dark {
  color-scheme: dark;
}

/* Chart theme adjustments */
.dark .recharts-cartesian-grid line {
  stroke: hsl(var(--border));
}

.dark .recharts-text {
  fill: hsl(var(--muted-foreground));
}

/* Code syntax highlighting adjustments */
.dark pre {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
}

/* Image adjustments for dark mode */
.dark img[data-theme-invert] {
  filter: invert(1) hue-rotate(180deg);
}

/* Loading skeleton adjustments */
.dark .animate-pulse {
  animation: dark-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes dark-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}
```

### Smart Auto-Switch System
```tsx
// utils/sun-times.ts
export function getSunriseTime(
  location: { latitude: number; longitude: number },
  date: Date
): number {
  // Simplified sunrise calculation (in minutes from midnight)
  const dayOfYear = getDayOfYear(date);
  const latitude = location.latitude * Math.PI / 180;
  
  // Solar declination
  const declination = 0.4095 * Math.sin(2 * Math.PI * (dayOfYear - 81) / 365);
  
  // Hour angle
  const hourAngle = Math.acos(-Math.tan(latitude) * Math.tan(declination));
  
  // Solar time correction
  const solarNoon = 12 - location.longitude / 15;
  const sunrise = solarNoon - hourAngle * 12 / Math.PI;
  
  return Math.max(0, Math.min(24 * 60, sunrise * 60)); // Convert to minutes
}

export function getSunsetTime(
  location: { latitude: number; longitude: number },
  date: Date
): number {
  const sunrise = getSunriseTime(location, date);
  const dayLength = getDayLength(location, date);
  return sunrise + dayLength;
}

function getDayOfYear(date: Date): number {
  const start = new Date(date.getFullYear(), 0, 0);
  const diff = date.getTime() - start.getTime();
  return Math.floor(diff / (1000 * 60 * 60 * 24));
}

function getDayLength(
  location: { latitude: number; longitude: number },
  date: Date
): number {
  // Simplified day length calculation in minutes
  const dayOfYear = getDayOfYear(date);
  const latitude = location.latitude * Math.PI / 180;
  
  const declination = 0.4095 * Math.sin(2 * Math.PI * (dayOfYear - 81) / 365);
  const hourAngle = Math.acos(-Math.tan(latitude) * Math.tan(declination));
  
  return 2 * hourAngle * 12 * 60 / Math.PI; // Convert to minutes
}

// Auto-switch hook
export function useAutoThemeSwitch(location?: { latitude: number; longitude: number }) {
  const [autoSwitchEnabled, setAutoSwitchEnabled] = usePersistentState(
    'auto-theme-switch',
    false
  );
  
  const { theme, setTheme } = useTheme();
  
  useEffect(() => {
    if (!autoSwitchEnabled || !location || theme !== 'system') return;
    
    const updateTheme = () => {
      const now = new Date();
      const sunrise = getSunriseTime(location, now);
      const sunset = getSunsetTime(location, now);
      const currentMinutes = now.getHours() * 60 + now.getMinutes();
      
      const shouldBeDark = currentMinutes < sunrise || currentMinutes > sunset;
      const currentTheme = document.documentElement.classList.contains('dark');
      
      if (shouldBeDark !== currentTheme) {
        // Theme will update automatically through system detection
        window.dispatchEvent(new Event('theme-auto-switch'));
      }
    };
    
    // Check immediately
    updateTheme();
    
    // Check every minute
    const interval = setInterval(updateTheme, 60000);
    
    return () => clearInterval(interval);
  }, [autoSwitchEnabled, location, theme]);
  
  return {
    autoSwitchEnabled,
    setAutoSwitchEnabled,
    canAutoSwitch: !!location
  };
}
```

### Theme-Aware Component Example
```tsx
// Example of a theme-aware component
export function ThemeAwareCard({ children, ...props }) {
  const { resolvedTheme } = useTheme();
  
  return (
    <Card 
      className={cn(
        "transition-colors duration-200",
        // Theme-specific styles
        resolvedTheme === 'dark' && "bg-card/50 backdrop-blur-sm border-border/50",
        resolvedTheme === 'light' && "bg-white shadow-sm border-border"
      )}
      {...props}
    >
      {children}
    </Card>
  );
}

// Theme-aware chart component
export function ThemedChart({ data, ...props }) {
  const { resolvedTheme } = useTheme();
  
  const chartColors = resolvedTheme === 'dark' ? {
    grid: 'hsl(var(--border))',
    text: 'hsl(var(--muted-foreground))',
    line: 'hsl(var(--primary))'
  } : {
    grid: 'hsl(var(--border))',
    text: 'hsl(var(--foreground))',
    line: 'hsl(var(--primary))'
  };
  
  return (
    <ResponsiveContainer width="100%" height={400}>
      <LineChart data={data}>
        <CartesianGrid stroke={chartColors.grid} />
        <XAxis tick={{ fill: chartColors.text }} />
        <YAxis tick={{ fill: chartColors.text }} />
        <Line 
          type="monotone" 
          dataKey="value" 
          stroke={chartColors.line}
          strokeWidth={2}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
```

This comprehensive dark mode system provides a seamless, accessible, and intelligent theming experience that adapts to user preferences and environmental conditions while maintaining excellent performance and visual consistency.