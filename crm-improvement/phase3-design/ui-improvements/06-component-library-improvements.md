# Component Library Improvements Design

## Problem Statement
The current component system lacks consistency, accessibility standards, and comprehensive state management. Components don't follow a unified design language, missing crucial states like loading, error, and empty states.

## Design Philosophy
**Atomic Design**: Build complex interfaces from simple, reusable components
**Accessibility First**: WCAG 2.1 AA compliance built into every component
**State-Driven**: Every component handles all possible states gracefully
**Theme-Aware**: Seamless light/dark mode transitions
**Mobile-Optimized**: Touch-friendly interactions across all components

## Component Architecture

### Base Component Pattern
```typescript
// Base component interface
interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  variant?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  'data-testid'?: string;
}

// Component state interface
interface ComponentState {
  isLoading: boolean;
  hasError: boolean;
  isEmpty: boolean;
  isDisabled: boolean;
  isFocused: boolean;
  isHovered: boolean;
  isActive: boolean;
}
```

## Enhanced Button Component System

### 1. Comprehensive Button Variants
```tsx
// components/ui/enhanced-button.tsx
interface EnhancedButtonProps extends BaseComponentProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'success' | 'warning';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  href?: string;
  target?: string;
  onClick?: (e: React.MouseEvent) => void | Promise<void>;
}

export function EnhancedButton({
  variant = 'default',
  size = 'md',
  loading = false,
  disabled = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  href,
  children,
  className,
  onClick,
  ...props
}: EnhancedButtonProps) {
  const [isLoading, setIsLoading] = useState(loading);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);

  const handleClick = async (e: React.MouseEvent) => {
    if (disabled || isLoading) return;

    // Add ripple effect
    const rect = e.currentTarget.getBoundingClientRect();
    const ripple = {
      id: Date.now(),
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
    setRipples(prev => [...prev, ripple]);

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(r => r.id !== ripple.id));
    }, 600);

    // Handle async onClick
    if (onClick) {
      try {
        setIsLoading(true);
        await onClick(e);
      } catch (error) {
        console.error('Button click error:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const buttonClasses = cn(
    // Base styles
    "relative inline-flex items-center justify-center rounded-md font-medium",
    "transition-all duration-200 focus-visible:outline-none focus-visible:ring-2",
    "focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50",
    "disabled:cursor-not-allowed overflow-hidden",

    // Size variants
    {
      "h-7 px-2 text-xs": size === 'xs',
      "h-8 px-3 text-sm": size === 'sm',
      "h-10 px-4 text-sm": size === 'md',
      "h-11 px-8 text-base": size === 'lg',
      "h-12 px-10 text-lg": size === 'xl',
    },

    // Color variants
    {
      "bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95": variant === 'default',
      "bg-destructive text-destructive-foreground hover:bg-destructive/90": variant === 'destructive',
      "border border-input bg-background hover:bg-accent hover:text-accent-foreground": variant === 'outline',
      "bg-secondary text-secondary-foreground hover:bg-secondary/80": variant === 'secondary',
      "hover:bg-accent hover:text-accent-foreground": variant === 'ghost',
      "text-primary underline-offset-4 hover:underline": variant === 'link',
      "bg-green-600 text-white hover:bg-green-700": variant === 'success',
      "bg-yellow-600 text-white hover:bg-yellow-700": variant === 'warning',
    },

    // Width
    fullWidth && "w-full",

    // Custom classes
    className
  );

  const content = (
    <>
      {/* Ripple effects */}
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute bg-white/30 rounded-full animate-ping"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
            pointerEvents: 'none',
          }}
        />
      ))}

      {/* Content */}
      {(isLoading || loading) && (
        <Loader2 className={cn("animate-spin", leftIcon ? "mr-2" : rightIcon ? "ml-2" : "")} size={16} />
      )}
      
      {!isLoading && !loading && leftIcon && (
        <span className="mr-2">{leftIcon}</span>
      )}
      
      {children}
      
      {!isLoading && !loading && rightIcon && (
        <span className="ml-2">{rightIcon}</span>
      )}
    </>
  );

  if (href) {
    return (
      <Link
        href={href}
        className={buttonClasses}
        {...props}
      >
        {content}
      </Link>
    );
  }

  return (
    <button
      className={buttonClasses}
      disabled={disabled || isLoading || loading}
      onClick={handleClick}
      {...props}
    >
      {content}
    </button>
  );
}

// Usage examples
export function ButtonExamples() {
  return (
    <div className="space-y-4 p-6">
      <div className="flex flex-wrap gap-2">
        <EnhancedButton>Default</EnhancedButton>
        <EnhancedButton variant="destructive">Delete</EnhancedButton>
        <EnhancedButton variant="outline">Cancel</EnhancedButton>
        <EnhancedButton variant="secondary">Secondary</EnhancedButton>
        <EnhancedButton variant="ghost">Ghost</EnhancedButton>
        <EnhancedButton variant="success" leftIcon={<Check />}>
          Success
        </EnhancedButton>
      </div>

      <div className="flex flex-wrap gap-2">
        <EnhancedButton size="xs">Extra Small</EnhancedButton>
        <EnhancedButton size="sm">Small</EnhancedButton>
        <EnhancedButton size="md">Medium</EnhancedButton>
        <EnhancedButton size="lg">Large</EnhancedButton>
        <EnhancedButton size="xl">Extra Large</EnhancedButton>
      </div>

      <div className="space-y-2">
        <EnhancedButton loading>Loading State</EnhancedButton>
        <EnhancedButton disabled>Disabled State</EnhancedButton>
        <EnhancedButton fullWidth>Full Width Button</EnhancedButton>
      </div>
    </div>
  );
}
```

### 2. Smart Input Components
```tsx
// components/ui/enhanced-input.tsx
interface EnhancedInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  leftAddon?: React.ReactNode;
  rightAddon?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'underlined';
  loading?: boolean;
  debounceMs?: number;
  onDebouncedChange?: (value: string) => void;
}

export function EnhancedInput({
  label,
  description,
  error,
  success,
  leftAddon,
  rightAddon,
  size = 'md',
  variant = 'default',
  loading = false,
  debounceMs = 0,
  onDebouncedChange,
  className,
  id,
  ...props
}: EnhancedInputProps) {
  const [focused, setFocused] = useState(false);
  const [value, setValue] = useState(props.value || '');
  const inputId = id || useId();

  // Debounced value for search/filter inputs
  const debouncedValue = useDebounce(value, debounceMs);

  useEffect(() => {
    if (onDebouncedChange && debouncedValue !== (props.value || '')) {
      onDebouncedChange(debouncedValue.toString());
    }
  }, [debouncedValue, onDebouncedChange, props.value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
    props.onChange?.(e);
  };

  const inputClasses = cn(
    // Base styles
    "flex w-full rounded-md border text-sm file:border-0 file:bg-transparent",
    "file:text-sm file:font-medium placeholder:text-muted-foreground",
    "focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
    "transition-all duration-200",

    // Size variants
    {
      "h-8 px-3 py-1": size === 'sm',
      "h-10 px-3 py-2": size === 'md',
      "h-12 px-4 py-3": size === 'lg',
    },

    // Variant styles
    {
      "border-input bg-background focus-visible:ring-2 focus-visible:ring-ring": variant === 'default',
      "border-transparent bg-muted focus-visible:bg-background focus-visible:ring-2 focus-visible:ring-ring": variant === 'filled',
      "border-0 border-b-2 border-input bg-transparent rounded-none focus-visible:border-primary": variant === 'underlined',
    },

    // State styles
    {
      "border-destructive focus-visible:ring-destructive": error,
      "border-green-500 focus-visible:ring-green-500": success && !error,
      "ring-2 ring-primary/20 border-primary": focused && !error && !success,
    },

    // Addon spacing
    {
      "pl-10": leftAddon,
      "pr-10": rightAddon || loading,
    },

    className
  );

  return (
    <div className="w-full space-y-2">
      {/* Label */}
      {label && (
        <Label 
          htmlFor={inputId}
          className={cn(
            "text-sm font-medium transition-colors",
            error && "text-destructive",
            success && "text-green-600"
          )}
        >
          {label}
          {props.required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}

      {/* Input container */}
      <div className="relative">
        {/* Left addon */}
        {leftAddon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            {leftAddon}
          </div>
        )}

        {/* Input */}
        <input
          id={inputId}
          className={inputClasses}
          value={value}
          onChange={handleChange}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          {...props}
        />

        {/* Right addon */}
        {(rightAddon || loading) && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            ) : (
              <span className="text-muted-foreground">{rightAddon}</span>
            )}
          </div>
        )}
      </div>

      {/* Help text, error, or success message */}
      {(description || error || success) && (
        <div className="space-y-1">
          {description && !error && !success && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
          {error && (
            <p className="text-xs text-destructive flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              {error}
            </p>
          )}
          {success && !error && (
            <p className="text-xs text-green-600 flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              {success}
            </p>
          )}
        </div>
      )}
    </div>
  );
}

// Specialized input variants
export function SearchInput(props: Omit<EnhancedInputProps, 'leftAddon' | 'type'>) {
  return (
    <EnhancedInput
      type="search"
      leftAddon={<Search className="h-4 w-4" />}
      placeholder="Search..."
      debounceMs={300}
      {...props}
    />
  );
}

export function PasswordInput(props: Omit<EnhancedInputProps, 'rightAddon' | 'type'>) {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <EnhancedInput
      type={showPassword ? 'text' : 'password'}
      rightAddon={
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="hover:text-foreground transition-colors"
        >
          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
        </button>
      }
      {...props}
    />
  );
}
```

### 3. Advanced Card Component
```tsx
// components/ui/enhanced-card.tsx
interface EnhancedCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled' | 'interactive';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  error?: string;
  empty?: boolean;
  emptyState?: React.ReactNode;
  loadingState?: React.ReactNode;
  errorState?: React.ReactNode;
  hover?: boolean;
  onClick?: () => void;
  badge?: React.ReactNode;
}

export function EnhancedCard({
  children,
  className,
  variant = 'default',
  size = 'md',
  loading = false,
  error,
  empty = false,
  emptyState,
  loadingState,
  errorState,
  hover = false,
  onClick,
  badge,
  ...props
}: EnhancedCardProps) {
  const cardClasses = cn(
    // Base styles
    "rounded-lg border bg-card text-card-foreground transition-all duration-200",
    "relative overflow-hidden",

    // Size variants
    {
      "p-3": size === 'sm',
      "p-6": size === 'md',
      "p-8": size === 'lg',
    },

    // Variant styles
    {
      "border-border shadow-sm": variant === 'default',
      "border-border shadow-lg hover:shadow-xl": variant === 'elevated',
      "border-2 border-border": variant === 'outlined',
      "bg-muted/50 border-transparent": variant === 'filled',
      "cursor-pointer hover:bg-muted/50 hover:shadow-md border-border": variant === 'interactive' || onClick,
    },

    // Hover effects
    hover && "hover:shadow-md hover:scale-[1.02]",

    // Interactive
    onClick && "cursor-pointer",

    className
  );

  const handleClick = () => {
    if (onClick && !loading) {
      onClick();
    }
  };

  return (
    <div
      className={cardClasses}
      onClick={handleClick}
      {...props}
    >
      {/* Badge */}
      {badge && (
        <div className="absolute top-2 right-2 z-10">
          {badge}
        </div>
      )}

      {/* Loading state */}
      {loading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm 
                        flex items-center justify-center z-20">
          {loadingState || (
            <div className="flex flex-col items-center space-y-2">
              <Loader2 className="h-8 w-8 animate-spin" />
              <p className="text-sm text-muted-foreground">Loading...</p>
            </div>
          )}
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm 
                        flex items-center justify-center z-20">
          {errorState || (
            <div className="flex flex-col items-center space-y-2 text-center p-4">
              <AlertCircle className="h-8 w-8 text-destructive" />
              <div>
                <p className="font-medium text-destructive">Error</p>
                <p className="text-sm text-muted-foreground">{error}</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Empty state */}
      {empty && !loading && !error ? (
        <div className="flex items-center justify-center py-12">
          {emptyState || (
            <div className="text-center space-y-2">
              <Inbox className="h-8 w-8 text-muted-foreground mx-auto" />
              <p className="text-sm text-muted-foreground">No data available</p>
            </div>
          )}
        </div>
      ) : (
        !loading && !error && children
      )}
    </div>
  );
}

// Card composition components
export function CardHeader({ 
  children, 
  className, 
  actions 
}: { 
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
}) {
  return (
    <div className={cn("flex items-center justify-between mb-4", className)}>
      <div className="space-y-1">{children}</div>
      {actions && <div className="flex items-center space-x-2">{actions}</div>}
    </div>
  );
}

export function CardTitle({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <h3 className={cn("text-2xl font-semibold leading-none tracking-tight", className)}>
      {children}
    </h3>
  );
}

export function CardDescription({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <p className={cn("text-sm text-muted-foreground", className)}>
      {children}
    </p>
  );
}
```

### 4. Data Display Components
```tsx
// components/ui/data-table.tsx
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  loading?: boolean;
  error?: string;
  empty?: boolean;
  searchable?: boolean;
  sortable?: boolean;
  filterable?: boolean;
  pagination?: boolean;
  pageSize?: number;
  onRowClick?: (row: T) => void;
  emptyState?: React.ReactNode;
  loadingState?: React.ReactNode;
}

export function DataTable<T>({
  data,
  columns,
  loading = false,
  error,
  empty = false,
  searchable = true,
  sortable = true,
  filterable = false,
  pagination = true,
  pageSize = 10,
  onRowClick,
  emptyState,
  loadingState
}: DataTableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: pageSize,
  });

  const table = useReactTable({
    data: data || [],
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: sortable ? getSortedRowModel() : undefined,
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: pagination ? getPaginationRowModel() : undefined,
  });

  return (
    <div className="space-y-4">
      {/* Table controls */}
      {(searchable || filterable) && (
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          {searchable && (
            <SearchInput
              placeholder="Search all columns..."
              value={globalFilter}
              onChange={(value) => setGlobalFilter(value)}
              className="max-w-sm"
            />
          )}
          {filterable && (
            <div className="flex gap-2">
              {/* Add column filters here */}
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <EnhancedCard loading={loading} error={error} empty={empty && !loading} emptyState={emptyState}>
        <div className="rounded-md border overflow-hidden">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : (
                        <div
                          className={cn(
                            "flex items-center space-x-2",
                            header.column.getCanSort() && "cursor-pointer select-none hover:bg-muted/50 p-2 -m-2 rounded"
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {sortable && header.column.getCanSort() && (
                            <div className="flex flex-col">
                              <ChevronUp 
                                className={cn(
                                  "h-3 w-3",
                                  header.column.getIsSorted() === 'asc' ? "text-primary" : "text-muted-foreground"
                                )}
                              />
                              <ChevronDown 
                                className={cn(
                                  "h-3 w-3 -mt-1",
                                  header.column.getIsSorted() === 'desc' ? "text-primary" : "text-muted-foreground"
                                )}
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    onClick={() => onRowClick?.(row.original)}
                    className={cn(
                      "transition-colors",
                      onRowClick && "cursor-pointer hover:bg-muted/50"
                    )}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {loading ? (
                      loadingState || <TableSkeleton columns={columns.length} />
                    ) : empty ? (
                      emptyState || "No results found."
                    ) : (
                      "No data available."
                    )}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination && data && data.length > 0 && (
          <DataTablePagination table={table} />
        )}
      </EnhancedCard>
    </div>
  );
}
```

### 5. Loading States System
```tsx
// components/ui/loading-states.tsx

// Skeleton components for different content types
export function TextSkeleton({ lines = 1, className }: { lines?: number; className?: string }) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            "h-4 bg-muted rounded animate-pulse",
            i === lines - 1 && lines > 1 ? "w-4/5" : "w-full"
          )}
        />
      ))}
    </div>
  );
}

export function CardSkeleton({ className }: { className?: string }) {
  return (
    <Card className={cn("p-6", className)}>
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-muted rounded-full animate-pulse" />
          <div className="space-y-2 flex-1">
            <div className="h-4 bg-muted rounded w-1/2 animate-pulse" />
            <div className="h-3 bg-muted rounded w-1/3 animate-pulse" />
          </div>
        </div>
        <TextSkeleton lines={3} />
        <div className="flex space-x-2">
          <div className="h-8 bg-muted rounded w-20 animate-pulse" />
          <div className="h-8 bg-muted rounded w-16 animate-pulse" />
        </div>
      </div>
    </Card>
  );
}

export function TableSkeleton({ columns = 4, rows = 5 }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={colIndex}
              className="h-4 bg-muted rounded animate-pulse flex-1"
              style={{ animationDelay: `${(rowIndex * columns + colIndex) * 100}ms` }}
            />
          ))}
        </div>
      ))}
    </div>
  );
}

// Shimmer effect for images
export function ImageSkeleton({ 
  width = "100%", 
  height = "200px", 
  className 
}: { 
  width?: string; 
  height?: string; 
  className?: string; 
}) {
  return (
    <div
      className={cn(
        "bg-muted rounded animate-pulse flex items-center justify-center",
        className
      )}
      style={{ width, height }}
    >
      <ImageIcon className="w-8 h-8 text-muted-foreground" />
    </div>
  );
}

// Loading spinner with text
export function LoadingSpinner({ 
  text = "Loading...", 
  size = "md" 
}: { 
  text?: string; 
  size?: 'sm' | 'md' | 'lg';
}) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8", 
    lg: "w-12 h-12"
  };

  return (
    <div className="flex flex-col items-center space-y-2">
      <Loader2 className={cn("animate-spin", sizeClasses[size])} />
      <p className="text-sm text-muted-foreground">{text}</p>
    </div>
  );
}
```

### 6. Empty States System
```tsx
// components/ui/empty-states.tsx
interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline';
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
  illustration?: React.ReactNode;
}

export function EmptyState({
  icon,
  title,
  description,
  action,
  secondaryAction,
  illustration
}: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center space-y-4">
      {/* Icon or illustration */}
      <div className="flex items-center justify-center w-16 h-16 rounded-full bg-muted">
        {illustration || icon || <Inbox className="w-8 h-8 text-muted-foreground" />}
      </div>

      {/* Content */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">{title}</h3>
        {description && (
          <p className="text-muted-foreground max-w-md">{description}</p>
        )}
      </div>

      {/* Actions */}
      {(action || secondaryAction) && (
        <div className="flex flex-col sm:flex-row gap-2">
          {action && (
            <EnhancedButton
              variant={action.variant || 'default'}
              onClick={action.onClick}
            >
              {action.label}
            </EnhancedButton>
          )}
          {secondaryAction && (
            <EnhancedButton
              variant="outline"
              onClick={secondaryAction.onClick}
            >
              {secondaryAction.label}
            </EnhancedButton>
          )}
        </div>
      )}
    </div>
  );
}

// Specialized empty states
export function NoContactsFound({ onCreateContact }: { onCreateContact: () => void }) {
  return (
    <EmptyState
      icon={<Users className="w-8 h-8 text-muted-foreground" />}
      title="No contacts found"
      description="Start building your network by adding your first contact."
      action={{
        label: "Add Contact",
        onClick: onCreateContact
      }}
      secondaryAction={{
        label: "Import Contacts",
        onClick: () => console.log("Import contacts")
      }}
    />
  );
}

export function NoDealsInPipeline({ onCreateDeal }: { onCreateDeal: () => void }) {
  return (
    <EmptyState
      icon={<Target className="w-8 h-8 text-muted-foreground" />}
      title="No deals in pipeline"
      description="Track opportunities and close more deals by adding them to your pipeline."
      action={{
        label: "Create Deal",
        onClick: onCreateDeal
      }}
    />
  );
}
```

This enhanced component library provides a comprehensive foundation for building consistent, accessible, and performant UI components throughout the CRM system. Each component handles multiple states gracefully and follows modern design patterns for optimal user experience.