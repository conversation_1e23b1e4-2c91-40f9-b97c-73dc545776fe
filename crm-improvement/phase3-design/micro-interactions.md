# CRM Micro-Interactions & Whimsy Design Specifications

## Overview
Transform the CRM from a functional tool into a delightful experience that sales teams love to use. These micro-interactions create emotional connections, reduce stress in high-pressure environments, and generate moments worth sharing.

## Design Philosophy

### Professional Whimsy Balance
- **Subtle Joy**: Delight without disruption to serious business tasks
- **Contextual Appropriateness**: Celebrations match achievement significance
- **Cultural Sensitivity**: Universal gestures and inclusive language
- **Stress Reduction**: Turn frustrating moments into encouraging ones
- **Team Spirit**: Foster collaboration and healthy competition

### Animation Principles
- **Easing**: Natural motion with `cubic-bezier(0.34, 1.56, 0.64, 1)` for bouncy feel
- **Duration**: 200-400ms for micro-interactions, 600-1000ms for celebrations
- **Staggering**: Elements appear in sequence (100ms delays)
- **Physics**: Respect real-world motion (gravity, momentum, friction)
- **Accessibility**: Respect `prefers-reduced-motion` settings

## 1. Success Animations for Completed Tasks/Deals

### Deal Closure Celebration
```typescript
// Trigger: When deal status changes to "Won"
interface DealSuccessAnimation {
  trigger: 'deal_won';
  context: {
    dealValue: number;
    dealName: string;
    salesperson: string;
  };
}
```

#### Epic Win (Deals > $50k)
```tsx
export function EpicDealCelebration({ deal }: { deal: Deal }) {
  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center"
    >
      {/* Backdrop explosion */}
      <motion.div
        initial={{ scale: 0, rotate: 0 }}
        animate={{ scale: 3, rotate: 360 }}
        transition={{ duration: 1.2, ease: "easeOut" }}
        className="absolute w-32 h-32 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full opacity-20"
      />
      
      {/* Main celebration card */}
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
        className="bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-2xl max-w-md mx-4"
      >
        <div className="text-center space-y-4">
          {/* Trophy animation */}
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.5, type: "spring", stiffness: 150 }}
            className="w-20 h-20 mx-auto text-yellow-500"
          >
            <Trophy className="w-full h-full" />
          </motion.div>
          
          {/* Success message */}
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="text-2xl font-bold bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent"
          >
            🎉 EPIC WIN! 🎉
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
            className="text-lg text-gray-600 dark:text-gray-300"
          >
            {deal.salesperson} closed <strong>{deal.name}</strong> for{' '}
            <span className="text-green-600 font-bold">
              ${deal.value.toLocaleString()}!
            </span>
          </motion.p>
          
          {/* Achievement badges */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.1 }}
            className="flex justify-center space-x-2"
          >
            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
              🏆 Big Deal
            </Badge>
            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
              💎 Premium Close
            </Badge>
          </motion.div>
        </div>
      </motion.div>
      
      {/* Confetti particles */}
      {Array.from({ length: 50 }).map((_, i) => (
        <motion.div
          key={i}
          initial={{
            opacity: 0,
            scale: 0,
            x: 0,
            y: 0,
            rotate: 0
          }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1, 0.5],
            x: (Math.random() - 0.5) * 800,
            y: (Math.random() - 0.5) * 600,
            rotate: Math.random() * 720
          }}
          transition={{
            duration: 2,
            delay: Math.random() * 0.5,
            ease: "easeOut"
          }}
          className={cn(
            "absolute w-3 h-3 pointer-events-none",
            [
              "bg-yellow-400", "bg-orange-500", "bg-red-500", 
              "bg-pink-500", "bg-purple-500", "bg-blue-500"
            ][Math.floor(Math.random() * 6)]
          )}
          style={{
            borderRadius: Math.random() > 0.5 ? "50%" : "0%"
          }}
        />
      ))}
    </motion.div>
  );
}
```

#### Standard Win (Deals $1k-$50k)
```tsx
export function StandardDealCelebration({ deal }: { deal: Deal }) {
  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.8, opacity: 0 }}
      className="bg-green-50 dark:bg-green-950 border-2 border-green-200 dark:border-green-800 rounded-xl p-6 shadow-lg"
    >
      <div className="flex items-center space-x-4">
        <motion.div
          animate={{ 
            rotate: [0, -10, 10, -10, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 0.6 }}
          className="text-green-600 dark:text-green-400"
        >
          <CheckCircle className="w-8 h-8" />
        </motion.div>
        
        <div className="flex-1">
          <h3 className="font-semibold text-green-800 dark:text-green-200">
            Deal Closed! 🎯
          </h3>
          <p className="text-sm text-green-600 dark:text-green-400">
            <strong>{deal.name}</strong> - ${deal.value.toLocaleString()}
          </p>
        </div>
        
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring" }}
        >
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            ✨ Success
          </Badge>
        </motion.div>
      </div>
    </motion.div>
  );
}
```

### Task Completion Micro-Interactions
```tsx
// Enhanced checkbox with satisfying completion
export function DelightfulCheckbox({ 
  checked, 
  onChange, 
  label,
  size = 'md' 
}: DelightfulCheckboxProps) {
  const [isAnimating, setIsAnimating] = useState(false);
  
  const handleChange = (newChecked: boolean) => {
    if (newChecked) {
      setIsAnimating(true);
      setTimeout(() => setIsAnimating(false), 600);
    }
    onChange(newChecked);
  };

  return (
    <div className="flex items-center space-x-3">
      <div className="relative">
        <motion.div
          animate={checked ? { scale: [1, 1.2, 1] } : {}}
          transition={{ duration: 0.3 }}
        >
          <Checkbox
            checked={checked}
            onCheckedChange={handleChange}
            className={cn(
              "transition-all duration-200",
              checked && "bg-green-500 border-green-500"
            )}
          />
        </motion.div>
        
        {/* Success ripple */}
        <AnimatePresence>
          {isAnimating && (
            <motion.div
              initial={{ scale: 0, opacity: 1 }}
              animate={{ scale: 3, opacity: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.6 }}
              className="absolute inset-0 border-2 border-green-400 rounded-sm pointer-events-none"
            />
          )}
        </AnimatePresence>
      </div>
      
      {label && (
        <motion.label
          animate={checked ? { color: '#10b981' } : { color: 'inherit' }}
          className={cn(
            "text-sm font-medium cursor-pointer",
            checked && "line-through"
          )}
        >
          {label}
        </motion.label>
      )}
    </div>
  );
}
```

## 2. Helpful and Encouraging Empty States

### Empty Pipeline Stage
```tsx
export function EmptyPipelineState({ 
  stageName, 
  onAddCard,
  encouragementLevel = 'standard' 
}: EmptyPipelineProps) {
  const encouragements = {
    standard: [
      "Every expert was once a beginner 💪",
      "Your next big win starts here 🎯",
      "Great things take time ⏰",
      "Opportunity is knocking 🚪"
    ],
    motivational: [
      "Champions are made in empty moments like this! 🏆",
      "This space is waiting for your next victory 🌟",
      "Every closed deal started in an empty pipeline 💎",
      "Your persistence will pay off - keep going! 🚀"
    ],
    playful: [
      "This stage feels lonely... let's fix that! 👥",
      "Even ninjas need somewhere to practice 🥷",
      "Plot twist: you're about to make this interesting 📈",
      "Silence before the storm ⛈️"
    ]
  };

  const getMessage = () => {
    const messages = encouragements[encouragementLevel];
    return messages[Math.floor(Math.random() * messages.length)];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center py-12 px-4 text-center"
    >
      {/* Animated illustration */}
      <motion.div
        animate={{ 
          y: [0, -10, 0],
          rotate: [0, 5, -5, 0]
        }}
        transition={{ 
          duration: 4, 
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="w-24 h-24 mb-6 text-muted-foreground/50"
      >
        <Target className="w-full h-full" />
      </motion.div>
      
      {/* Encouraging message */}
      <motion.h3
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="text-lg font-medium text-muted-foreground mb-2"
      >
        {getMessage()}
      </motion.h3>
      
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="text-sm text-muted-foreground/75 mb-6"
      >
        Ready to add your first deal to <strong>{stageName}</strong>?
      </motion.p>
      
      {/* Action button with hover animation */}
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button onClick={onAddCard} className="group">
          <Plus className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform" />
          Add Deal
        </Button>
      </motion.div>
      
      {/* Subtle sparkles */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 3 }).map((_, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ 
              opacity: [0, 0.6, 0],
              scale: [0, 1, 0]
            }}
            transition={{ 
              duration: 3,
              delay: i * 1,
              repeat: Infinity
            }}
            className="absolute w-1 h-1 bg-primary/30 rounded-full"
            style={{
              left: `${20 + i * 30}%`,
              top: `${30 + i * 20}%`
            }}
          />
        ))}
      </div>
    </motion.div>
  );
}
```

### No Contacts Found
```tsx
export function EmptyContactsState({ onAddContact }: EmptyContactsProps) {
  const [hovering, setHovering] = useState(false);
  
  return (
    <div className="flex flex-col items-center justify-center py-16 text-center space-y-6">
      <motion.div
        animate={hovering ? { scale: 1.1 } : { scale: 1 }}
        onHoverStart={() => setHovering(true)}
        onHoverEnd={() => setHovering(false)}
        className="relative"
      >
        {/* Main illustration */}
        <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 flex items-center justify-center mb-6">
          <Users className="w-16 h-16 text-blue-500 dark:text-blue-400" />
        </div>
        
        {/* Floating elements */}
        <motion.div
          animate={{ 
            rotate: 360,
            x: [0, 10, 0, -10, 0]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center"
        >
          <Sparkles className="w-3 h-3 text-yellow-800" />
        </motion.div>
      </motion.div>
      
      <div className="space-y-2">
        <h3 className="text-xl font-semibold">Your network awaits! 🤝</h3>
        <p className="text-muted-foreground max-w-md">
          Every successful business starts with great relationships. 
          Let's add your first contact and begin building something amazing.
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3">
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button onClick={onAddContact} size="lg" className="group">
            <UserPlus className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
            Add First Contact
          </Button>
        </motion.div>
        
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button variant="outline" size="lg">
            <Upload className="w-4 h-4 mr-2" />
            Import Contacts
          </Button>
        </motion.div>
      </div>
      
      {/* Encouraging stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-muted/50 rounded-lg p-4 max-w-sm"
      >
        <p className="text-xs text-muted-foreground">
          💡 <strong>Pro tip:</strong> Sales pros with 500+ contacts close 67% more deals!
        </p>
      </motion.div>
    </div>
  );
}
```

## 3. Progress Indicators with Personality

### Deal Progress Tracker
```tsx
export function PersonalityProgressBar({ 
  progress, 
  stage, 
  personality = 'encouraging' 
}: PersonalityProgressProps) {
  const personalities = {
    encouraging: {
      messages: [
        "You've got this! 💪",
        "Making great progress! 🎯",
        "Almost there, superstar! ⭐",
        "Victory is within reach! 🏆"
      ],
      colors: "from-blue-500 to-purple-500",
      emoji: "🚀"
    },
    playful: {
      messages: [
        "Level up! 🎮",
        "Beast mode activated! 👹",
        "On fire! 🔥",
        "Legendary status! 🦄"
      ],
      colors: "from-orange-500 to-red-500",
      emoji: "🎪"
    },
    professional: {
      messages: [
        "Excellent progress",
        "On track for success",
        "Nearing completion",
        "Ready to close"
      ],
      colors: "from-green-500 to-blue-500",
      emoji: "📈"
    }
  };

  const current = personalities[personality];
  const messageIndex = Math.min(
    Math.floor(progress / 25),
    current.messages.length - 1
  );

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">
          {current.messages[messageIndex]} {current.emoji}
        </span>
        <span className="text-sm text-muted-foreground">
          {Math.round(progress)}%
        </span>
      </div>
      
      <div className="relative w-full h-3 bg-muted rounded-full overflow-hidden">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className={cn(
            "h-full bg-gradient-to-r rounded-full relative",
            current.colors
          )}
        >
          {/* Shimmer effect */}
          <motion.div
            animate={{ x: ['100%', '-100%'] }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
          />
        </motion.div>
        
        {/* Progress milestones */}
        {[25, 50, 75].map((milestone) => (
          <motion.div
            key={milestone}
            initial={{ scale: 0 }}
            animate={{ scale: progress >= milestone ? 1 : 0.5 }}
            className={cn(
              "absolute top-1/2 w-2 h-2 rounded-full -translate-y-1/2 border-2 border-white",
              progress >= milestone ? "bg-white" : "bg-muted-foreground/30"
            )}
            style={{ left: `${milestone}%`, transform: 'translateX(-50%) translateY(-50%)' }}
          />
        ))}
      </div>
      
      {/* Achievement unlocked */}
      <AnimatePresence>
        {progress === 100 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0 }}
            className="bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg p-3"
          >
            <div className="flex items-center space-x-2">
              <Trophy className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                Deal ready to close! 🎉
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
```

### Team Performance Meter
```tsx
export function TeamMoodMeter({ 
  teamPerformance, 
  teamSize, 
  period = 'this month' 
}: TeamMoodMeterProps) {
  const getMoodData = (performance: number) => {
    if (performance >= 90) return { mood: '🔥', text: 'ON FIRE!', color: 'text-red-500' };
    if (performance >= 80) return { mood: '🚀', text: 'Flying High!', color: 'text-orange-500' };
    if (performance >= 70) return { mood: '😊', text: 'Doing Great!', color: 'text-green-500' };
    if (performance >= 60) return { mood: '💪', text: 'Getting There!', color: 'text-blue-500' };
    if (performance >= 40) return { mood: '🎯', text: 'Finding Focus', color: 'text-purple-500' };
    return { mood: '🌱', text: 'Growing Strong', color: 'text-green-600' };
  };

  const moodData = getMoodData(teamPerformance);

  return (
    <Card className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50">
      <CardContent className="p-6">
        <div className="text-center space-y-4">
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-6xl"
          >
            {moodData.mood}
          </motion.div>
          
          <div>
            <h3 className={cn("text-2xl font-bold", moodData.color)}>
              {moodData.text}
            </h3>
            <p className="text-sm text-muted-foreground">
              Team of {teamSize} • {performance}% target {period}
            </p>
          </div>
          
          {/* Performance rings */}
          <div className="relative w-24 h-24 mx-auto">
            <svg className="w-full h-full transform -rotate-90">
              <circle
                cx="48"
                cy="48"
                r="40"
                fill="none"
                stroke="currentColor"
                strokeWidth="4"
                className="text-muted/20"
              />
              <motion.circle
                cx="48"
                cy="48"
                r="40"
                fill="none"
                stroke="currentColor"
                strokeWidth="4"
                strokeLinecap="round"
                className="text-primary"
                strokeDasharray={`${2 * Math.PI * 40}`}
                initial={{ strokeDashoffset: `${2 * Math.PI * 40}` }}
                animate={{ 
                  strokeDashoffset: `${2 * Math.PI * 40 * (1 - teamPerformance / 100)}` 
                }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
            </svg>
            
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-lg font-bold">{teamPerformance}%</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

## 4. Celebration Moments for Achievements

### Monthly Target Achievement
```tsx
export function MonthlyTargetCelebration({ 
  achievement, 
  targetAmount, 
  actualAmount 
}: MonthlyTargetProps) {
  const overPerformance = ((actualAmount - targetAmount) / targetAmount) * 100;
  
  return (
    <motion.div
      initial={{ scale: 0, rotate: -180 }}
      animate={{ scale: 1, rotate: 0 }}
      transition={{ type: "spring", stiffness: 150, damping: 12 }}
      className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center"
    >
      {/* Background celebration */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-orange-500/10" />
      
      {/* Main achievement card */}
      <Card className="bg-white dark:bg-gray-900 shadow-2xl max-w-lg mx-4 overflow-hidden">
        <CardContent className="p-8">
          <div className="text-center space-y-6">
            {/* Trophy with particles */}
            <div className="relative">
              <motion.div
                animate={{ 
                  y: [0, -10, 0],
                  rotateY: [0, 360]
                }}
                transition={{ 
                  y: { duration: 2, repeat: Infinity },
                  rotateY: { duration: 3, repeat: Infinity }
                }}
                className="w-20 h-20 mx-auto text-yellow-500"
              >
                <Trophy className="w-full h-full" />
              </motion.div>
              
              {/* Floating particles */}
              {Array.from({ length: 8 }).map((_, i) => (
                <motion.div
                  key={i}
                  animate={{
                    y: [0, -30, 0],
                    opacity: [0, 1, 0],
                    scale: [0, 1, 0]
                  }}
                  transition={{
                    duration: 2,
                    delay: i * 0.2,
                    repeat: Infinity
                  }}
                  className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                  style={{
                    left: `${50 + (Math.cos(i * 45 * Math.PI / 180) * 40)}%`,
                    top: `${50 + (Math.sin(i * 45 * Math.PI / 180) * 40)}%`,
                  }}
                />
              ))}
            </div>
            
            <div className="space-y-2">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                TARGET SMASHED! 🎯
              </h1>
              {overPerformance > 0 && (
                <p className="text-lg font-semibold text-green-600">
                  {overPerformance.toFixed(0)}% over target!
                </p>
              )}
            </div>
            
            {/* Achievement stats */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-4">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    ${targetAmount.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">Target</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    ${actualAmount.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">Achieved</div>
                </div>
              </div>
            </div>
            
            {/* Badges */}
            <div className="flex flex-wrap justify-center gap-2">
              <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                🏆 Goal Crusher
              </Badge>
              <Badge className="bg-purple-100 text-purple-800 border-purple-200">
                💎 Top Performer
              </Badge>
              {overPerformance > 20 && (
                <Badge className="bg-orange-100 text-orange-800 border-orange-200">
                  🔥 Overachiever
                </Badge>
              )}
            </div>
            
            {/* Call to action */}
            <div className="pt-4">
              <Button size="lg" className="group">
                Share the Victory
                <Share className="w-4 h-4 ml-2 group-hover:scale-110 transition-transform" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
```

### Streak Achievement
```tsx
export function StreakCelebration({ 
  streakType, 
  streakCount, 
  streakRecord 
}: StreakCelebrationProps) {
  const isNewRecord = streakCount > streakRecord;
  
  return (
    <motion.div
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: 300, opacity: 0 }}
      transition={{ type: "spring", stiffness: 100 }}
      className="fixed top-4 right-4 z-50 max-w-sm"
    >
      <Card className={cn(
        "border-2 shadow-lg",
        isNewRecord 
          ? "border-orange-300 bg-orange-50 dark:border-orange-700 dark:bg-orange-950" 
          : "border-blue-300 bg-blue-50 dark:border-blue-700 dark:bg-blue-950"
      )}>
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <motion.div
              animate={{ 
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ duration: 1 }}
              className={cn(
                "text-2xl",
                isNewRecord ? "text-orange-500" : "text-blue-500"
              )}
            >
              {isNewRecord ? "🔥" : "⚡"}
            </motion.div>
            
            <div className="flex-1 space-y-1">
              <h3 className={cn(
                "font-bold",
                isNewRecord ? "text-orange-800 dark:text-orange-200" : "text-blue-800 dark:text-blue-200"
              )}>
                {isNewRecord ? "NEW RECORD!" : `${streakCount}-Day Streak!`}
              </h3>
              
              <p className="text-sm text-muted-foreground">
                {streakType} for {streakCount} days straight
                {isNewRecord && ` (previous: ${streakRecord})`}
              </p>
              
              {/* Streak visualization */}
              <div className="flex space-x-1 pt-2">
                {Array.from({ length: Math.min(streakCount, 10) }).map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: i * 0.1 }}
                    className={cn(
                      "w-2 h-6 rounded-full",
                      isNewRecord ? "bg-orange-400" : "bg-blue-400"
                    )}
                  />
                ))}
                {streakCount > 10 && (
                  <div className="flex items-center text-xs text-muted-foreground">
                    +{streakCount - 10}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
```

## 5. Friendly Error Messages That Reduce Frustration

### Connection Error Recovery
```tsx
export function FriendlyConnectionError({ 
  onRetry, 
  severity = 'medium' 
}: FriendlyErrorProps) {
  const errorPersonalities = {
    low: {
      title: "Oops, tiny hiccup! 🤏",
      message: "Just a small blip in the matrix. We'll have you back online in no time!",
      suggestions: ["Maybe try refreshing?", "Check your connection"],
      mood: "optimistic"
    },
    medium: {
      title: "Houston, we have a problem... 🚀",
      message: "Don't worry! Even NASA has technical difficulties. We're on it!",
      suggestions: ["Try again in a moment", "Check your internet", "Contact support if this persists"],
      mood: "reassuring"
    },
    high: {
      title: "Well, this is awkward... 😅",
      message: "Something went really wrong, but we're like detectives on the case!",
      suggestions: ["We're investigating", "Try refreshing", "Our team has been notified"],
      mood: "apologetic"
    }
  };

  const error = errorPersonalities[severity];
  
  return (
    <motion.div
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ type: "spring", stiffness: 200 }}
      className="bg-red-50 dark:bg-red-950 border-2 border-red-200 dark:border-red-800 rounded-xl p-6 max-w-md mx-auto"
    >
      <div className="text-center space-y-4">
        {/* Animated error icon */}
        <motion.div
          animate={{ 
            rotate: [0, -10, 10, -10, 0],
            y: [0, -5, 0]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="w-16 h-16 mx-auto"
        >
          <div className="w-full h-full rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
            <WifiOff className="w-8 h-8 text-red-500" />
          </div>
        </motion.div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
            {error.title}
          </h3>
          <p className="text-sm text-red-600 dark:text-red-300">
            {error.message}
          </p>
        </div>
        
        {/* Helpful suggestions */}
        <div className="bg-red-100 dark:bg-red-900/50 rounded-lg p-3">
          <h4 className="text-xs font-medium text-red-800 dark:text-red-200 mb-2">
            What you can try:
          </h4>
          <ul className="text-xs text-red-600 dark:text-red-300 space-y-1">
            {error.suggestions.map((suggestion, i) => (
              <li key={i} className="flex items-center space-x-2">
                <div className="w-1 h-1 bg-red-400 rounded-full" />
                <span>{suggestion}</span>
              </li>
            ))}
          </ul>
        </div>
        
        {/* Action buttons */}
        <div className="flex space-x-3">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button onClick={onRetry} variant="outline" size="sm" className="group">
              <RefreshCw className="w-4 h-4 mr-2 group-hover:rotate-180 transition-transform duration-300" />
              Try Again
            </Button>
          </motion.div>
          
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button variant="ghost" size="sm">
              <MessageCircle className="w-4 h-4 mr-2" />
              Get Help
            </Button>
          </motion.div>
        </div>
        
        {/* Encouraging footer */}
        <p className="text-xs text-red-500 dark:text-red-400">
          💪 We believe in you - technical difficulties won't stop your success!
        </p>
      </div>
    </motion.div>
  );
}
```

### Form Validation with Personality
```tsx
export function PersonalityFormValidation({ 
  field, 
  error, 
  value,
  encouragementLevel = 'friendly' 
}: PersonalityValidationProps) {
  const validationPersonalities = {
    friendly: {
      required: `Hey, we need the ${field} to keep things rolling! 😊`,
      email: "That email looks a bit shy - mind double-checking it? 📧",
      minLength: `Just a few more characters and you're golden! ✨`,
      pattern: `Almost there! Just need to match the format 🎯`
    },
    encouraging: {
      required: `You're doing great! Just need the ${field} to continue 💪`,
      email: "So close! That email just needs a tiny adjustment 🚀",
      minLength: `You've got this! Just a bit more to reach the minimum 📈`,
      pattern: `Perfect effort! Let's just tweak the format slightly 🌟`
    },
    playful: {
      required: `Oops! The ${field} went on vacation - can you bring it back? 🏖️`,
      email: "That email is playing hide and seek - help us find it! 🕵️",
      minLength: `So close! Your input is almost at superhero length! 🦸`,
      pattern: `Almost cracked the code! Just need the secret format 🔐`
    }
  };

  const personality = validationPersonalities[encouragementLevel];
  const errorMessage = personality[error.type] || personality.required;

  return (
    <AnimatePresence>
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="flex items-center space-x-2 text-sm"
        >
          <motion.div
            animate={{ 
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{ duration: 0.5 }}
            className="text-amber-500"
          >
            <AlertCircle className="w-4 h-4" />
          </motion.div>
          
          <span className="text-amber-600 dark:text-amber-400">
            {errorMessage}
          </span>
          
          {/* Progress indicator for partial completion */}
          {value && error.type === 'minLength' && (
            <div className="flex-1 max-w-20">
              <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${(value.length / error.expected) * 100}%` }}
                  className="h-full bg-amber-400 rounded-full"
                />
              </div>
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}
```

## 6. Loading States That Entertain

### Personality Loading Spinner
```tsx
export function PersonalityLoadingSpinner({ 
  message, 
  personality = 'encouraging',
  duration = 'medium' 
}: PersonalityLoadingProps) {
  const personalities = {
    encouraging: {
      messages: [
        "Great things take time... ⏰",
        "Brewing something awesome... ☕",
        "Almost there, superstar! ⭐",
        "Magic is happening... ✨"
      ],
      color: "text-blue-500",
      spinner: "dots"
    },
    playful: {
      messages: [
        "Teaching hamsters to run faster... 🐹",
        "Convincing servers to cooperate... 🤖",
        "Summoning data from the cloud... ☁️",
        "Bribing the internet with cookies... 🍪"
      ],
      color: "text-purple-500",
      spinner: "bounce"
    },
    professional: {
      messages: [
        "Processing your request...",
        "Retrieving data...",
        "Finalizing results...",
        "Nearly complete..."
      ],
      color: "text-gray-500",
      spinner: "spin"
    }
  };

  const [currentMessage, setCurrentMessage] = useState(0);
  const currentPersonality = personalities[personality];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % currentPersonality.messages.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [currentPersonality.messages.length]);

  return (
    <div className="flex flex-col items-center justify-center space-y-4 py-12">
      {/* Animated spinner based on personality */}
      {currentPersonality.spinner === 'dots' && (
        <div className="flex space-x-2">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              animate={{
                y: [0, -20, 0],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: i * 0.2
              }}
              className={cn("w-3 h-3 rounded-full", currentPersonality.color)}
              style={{ backgroundColor: 'currentColor' }}
            />
          ))}
        </div>
      )}

      {currentPersonality.spinner === 'bounce' && (
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className={cn("w-8 h-8", currentPersonality.color)}
        >
          <Loader2 className="w-full h-full" />
        </motion.div>
      )}

      {currentPersonality.spinner === 'spin' && (
        <Loader2 className={cn("w-8 h-8 animate-spin", currentPersonality.color)} />
      )}

      {/* Rotating messages */}
      <AnimatePresence mode="wait">
        <motion.p
          key={currentMessage}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className="text-sm text-muted-foreground text-center max-w-xs"
        >
          {message || currentPersonality.messages[currentMessage]}
        </motion.p>
      </AnimatePresence>

      {/* Progress dots */}
      <div className="flex space-x-1">
        {currentPersonality.messages.map((_, i) => (
          <motion.div
            key={i}
            animate={{ 
              scale: currentMessage === i ? 1.2 : 1,
              opacity: currentMessage === i ? 1 : 0.3
            }}
            className={cn("w-2 h-2 rounded-full", currentPersonality.color)}
            style={{ backgroundColor: 'currentColor' }}
          />
        ))}
      </div>
    </div>
  );
}
```

### Progress Loading with Stages
```tsx
export function StageProgressLoader({ 
  stages, 
  currentStage, 
  personality = 'encouraging' 
}: StageProgressProps) {
  return (
    <div className="max-w-md mx-auto space-y-6 p-6">
      <div className="text-center space-y-2">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 mx-auto text-primary"
        >
          <Settings className="w-full h-full" />
        </motion.div>
        
        <h3 className="font-semibold">Setting things up...</h3>
        <p className="text-sm text-muted-foreground">
          This will only take a moment
        </p>
      </div>
      
      {/* Stage progress */}
      <div className="space-y-3">
        {stages.map((stage, index) => {
          const isCompleted = index < currentStage;
          const isCurrent = index === currentStage;
          const isUpcoming = index > currentStage;
          
          return (
            <motion.div
              key={stage.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                "flex items-center space-x-3 p-3 rounded-lg transition-all",
                isCurrent && "bg-primary/10 border border-primary/20",
                isCompleted && "opacity-75"
              )}
            >
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center border-2 transition-all",
                isCompleted && "bg-green-500 border-green-500 text-white",
                isCurrent && "border-primary text-primary animate-pulse",
                isUpcoming && "border-muted text-muted-foreground"
              )}>
                {isCompleted ? (
                  <Check className="w-4 h-4" />
                ) : isCurrent ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    <Loader2 className="w-4 h-4" />
                  </motion.div>
                ) : (
                  <span className="text-xs font-medium">{index + 1}</span>
                )}
              </div>
              
              <div className="flex-1">
                <h4 className={cn(
                  "font-medium transition-colors",
                  isCompleted && "text-green-600",
                  isCurrent && "text-primary",
                  isUpcoming && "text-muted-foreground"
                )}>
                  {stage.title}
                </h4>
                <p className="text-xs text-muted-foreground">
                  {stage.description}
                </p>
              </div>
              
              {isCompleted && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="text-green-500"
                >
                  <CheckCircle className="w-5 h-5" />
                </motion.div>
              )}
            </motion.div>
          );
        })}
      </div>
      
      {/* Overall progress bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Overall Progress</span>
          <span>{Math.round((currentStage / stages.length) * 100)}%</span>
        </div>
        <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${(currentStage / stages.length) * 100}%` }}
            transition={{ duration: 0.5 }}
            className="h-full bg-primary rounded-full"
          />
        </div>
      </div>
    </div>
  );
}
```

## 7. Onboarding with Personality

### Welcome Tour Guide
```tsx
export function PersonalityTourGuide({ 
  step, 
  totalSteps, 
  onNext, 
  onSkip,
  personality = 'friendly' 
}: TourGuideProps) {
  const guides = {
    friendly: {
      name: "Alex",
      avatar: "👋",
      greeting: "Hey there! I'm Alex, your friendly guide",
      tone: "casual and warm"
    },
    professional: {
      name: "Sam",
      avatar: "👔",
      greeting: "Hello! I'm Sam, here to help you get started",
      tone: "polite and efficient"
    },
    playful: {
      name: "Zoe",
      avatar: "🎮",
      greeting: "What's up! I'm Zoe, let's make this fun",
      tone: "energetic and fun"
    }
  };

  const guide = guides[personality];

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.8, opacity: 0 }}
      className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border max-w-sm p-6"
    >
      {/* Guide avatar and intro */}
      <div className="flex items-start space-x-3 mb-4">
        <motion.div
          animate={{ 
            rotate: [0, 10, -10, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-3xl"
        >
          {guide.avatar}
        </motion.div>
        
        <div className="flex-1">
          <h3 className="font-semibold text-lg">{guide.greeting}!</h3>
          <p className="text-sm text-muted-foreground">
            Let me show you around your new CRM
          </p>
        </div>
      </div>
      
      {/* Step content */}
      <div className="space-y-4">
        <div className="bg-muted/50 rounded-lg p-4">
          <h4 className="font-medium mb-2">{step.title}</h4>
          <p className="text-sm text-muted-foreground">
            {step.description}
          </p>
        </div>
        
        {/* Interactive elements */}
        {step.interactive && (
          <div className="border-2 border-dashed border-primary/30 rounded-lg p-3 text-center">
            <p className="text-sm text-primary font-medium">
              👆 Try clicking {step.interactive}
            </p>
          </div>
        )}
        
        {/* Progress indicator */}
        <div className="flex justify-between items-center text-sm">
          <span className="text-muted-foreground">
            Step {step.number} of {totalSteps}
          </span>
          <div className="flex space-x-1">
            {Array.from({ length: totalSteps }).map((_, i) => (
              <div
                key={i}
                className={cn(
                  "w-2 h-2 rounded-full",
                  i < step.number ? "bg-primary" : "bg-muted"
                )}
              />
            ))}
          </div>
        </div>
        
        {/* Action buttons */}
        <div className="flex justify-between space-x-2">
          <Button variant="ghost" size="sm" onClick={onSkip}>
            Skip Tour
          </Button>
          
          <div className="flex space-x-2">
            {step.number > 1 && (
              <Button variant="outline" size="sm">
                <ChevronLeft className="w-4 h-4 mr-1" />
                Back
              </Button>
            )}
            <Button size="sm" onClick={onNext}>
              {step.number === totalSteps ? "Finish" : "Next"}
              <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
```

## 8. Team Collaboration Features with Fun Elements

### High-Five Animation
```tsx
export function TeamHighFive({ 
  fromUser, 
  toUser, 
  achievement 
}: TeamHighFiveProps) {
  return (
    <motion.div
      initial={{ scale: 0, rotate: -180 }}
      animate={{ scale: 1, rotate: 0 }}
      exit={{ scale: 0, rotate: 180 }}
      transition={{ type: "spring", stiffness: 150 }}
      className="fixed bottom-6 right-6 z-50"
    >
      <Card className="bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0 shadow-lg max-w-xs">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <motion.div
              animate={{ 
                rotate: [0, 20, -20, 0],
                scale: [1, 1.2, 1]
              }}
              transition={{ duration: 0.6 }}
              className="text-2xl"
            >
              ✋
            </motion.div>
            
            <div className="flex-1">
              <p className="text-sm font-medium">
                High-five from {fromUser}!
              </p>
              <p className="text-xs opacity-90">
                Great work on {achievement}
              </p>
            </div>
            
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="text-2xl"
            >
              🙌
            </motion.button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
```

### Team Leaderboard with Personality
```tsx
export function PlayfulLeaderboard({ 
  teamMembers, 
  metric = 'deals_closed',
  period = 'this_month' 
}: PlayfulLeaderboardProps) {
  const personalities = {
    1: { emoji: "👑", title: "The Legend", color: "text-yellow-600" },
    2: { emoji: "🥈", title: "Rising Star", color: "text-gray-500" },
    3: { emoji: "🥉", title: "Go-Getter", color: "text-orange-500" },
    default: { emoji: "⭐", title: "Team Player", color: "text-blue-500" }
  };

  return (
    <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/50 dark:to-pink-950/50">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Trophy className="w-5 h-5 text-yellow-500" />
          <span>Team Leaderboard</span>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="text-yellow-500"
          >
            ⭐
          </motion.div>
        </CardTitle>
        <CardDescription>
          Top performers {period} • {metric.replace('_', ' ')}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {teamMembers.map((member, index) => {
          const rank = index + 1;
          const personality = personalities[rank] || personalities.default;
          
          return (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                "flex items-center space-x-3 p-3 rounded-lg transition-all",
                rank === 1 && "bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-300",
                rank === 2 && "bg-gray-100 dark:bg-gray-800/50",
                rank === 3 && "bg-orange-100 dark:bg-orange-900/20"
              )}
            >
              <motion.div
                animate={rank <= 3 ? { 
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                } : {}}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-2xl"
              >
                {personality.emoji}
              </motion.div>
              
              <Avatar className="w-10 h-10">
                <AvatarImage src={member.avatar} />
                <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium">{member.name}</h4>
                  {rank <= 3 && (
                    <Badge variant="secondary" className={personality.color}>
                      {personality.title}
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  {member[metric]} {metric.includes('deals') ? 'deals' : 'activities'}
                </p>
              </div>
              
              <div className="text-right">
                <div className="font-bold text-lg">#{rank}</div>
                {rank <= 3 && (
                  <motion.div
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="text-xs text-muted-foreground"
                  >
                    🔥 Hot streak
                  </motion.div>
                )}
              </div>
            </motion.div>
          );
        })}
        
        {/* Encouraging message for everyone */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-3 text-center mt-4"
        >
          <p className="text-sm text-muted-foreground">
            🌟 Every team member is a star! Keep up the amazing work! 🌟
          </p>
        </motion.div>
      </CardContent>
    </Card>
  );
}
```

## 9. Gamification Elements for Sales Teams

### Achievement Badge System
```tsx
export function AchievementBadgeUnlock({ 
  badge, 
  isNewUnlock = false 
}: AchievementBadgeProps) {
  const badgeTypes = {
    'first_deal': {
      icon: '🎯',
      title: 'First Blood',
      description: 'Closed your first deal!',
      rarity: 'common',
      points: 100
    },
    'deal_streak_5': {
      icon: '🔥',
      title: 'On Fire',
      description: '5 deals in a row!',
      rarity: 'uncommon',
      points: 250
    },
    'big_deal_50k': {
      icon: '💎',
      title: 'Diamond Deal',
      description: 'Closed a $50k+ deal!',
      rarity: 'rare',
      points: 500
    },
    'month_target': {
      icon: '👑',
      title: 'Monthly Champion',
      description: 'Hit monthly target!',
      rarity: 'epic',
      points: 750
    },
    'team_player': {
      icon: '🤝',
      title: 'Team Player',
      description: 'Helped 5 teammates!',
      rarity: 'uncommon',
      points: 200
    }
  };

  const badgeInfo = badgeTypes[badge.type];
  const rarityColors = {
    common: 'from-gray-400 to-gray-600',
    uncommon: 'from-green-400 to-green-600',
    rare: 'from-blue-400 to-blue-600',
    epic: 'from-purple-400 to-purple-600',
    legendary: 'from-yellow-400 to-orange-500'
  };

  return (
    <AnimatePresence>
      {isNewUnlock && (
        <motion.div
          initial={{ scale: 0, rotate: -180, opacity: 0 }}
          animate={{ scale: 1, rotate: 0, opacity: 1 }}
          exit={{ scale: 0, rotate: 180, opacity: 0 }}
          transition={{ type: "spring", stiffness: 200, damping: 15 }}
          className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center"
        >
          {/* Background overlay */}
          <div className="absolute inset-0 bg-black/20" />
          
          {/* Achievement card */}
          <Card className="bg-white dark:bg-gray-900 shadow-2xl max-w-sm mx-4 overflow-hidden">
            <CardContent className="p-0">
              {/* Header with rarity gradient */}
              <div className={cn(
                "bg-gradient-to-r text-white p-4",
                rarityColors[badgeInfo.rarity]
              )}>
                <div className="text-center space-y-2">
                  <motion.div
                    animate={{ 
                      rotate: [0, 360],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{ duration: 2 }}
                    className="text-4xl"
                  >
                    {badgeInfo.icon}
                  </motion.div>
                  <h2 className="font-bold text-lg">ACHIEVEMENT UNLOCKED!</h2>
                  <p className="text-sm opacity-90 capitalize">
                    {badgeInfo.rarity} • +{badgeInfo.points} XP
                  </p>
                </div>
              </div>
              
              {/* Badge details */}
              <div className="p-6 space-y-4">
                <div className="text-center space-y-2">
                  <h3 className="text-xl font-bold">{badgeInfo.title}</h3>
                  <p className="text-muted-foreground">{badgeInfo.description}</p>
                </div>
                
                {/* Progress to next level */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress to next badge</span>
                    <span>67%</span>
                  </div>
                  <div className="w-full h-2 bg-muted rounded-full">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: '67%' }}
                      transition={{ duration: 1, delay: 0.5 }}
                      className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                    />
                  </div>
                </div>
                
                {/* Share button */}
                <Button className="w-full" size="sm">
                  <Share className="w-4 h-4 mr-2" />
                  Share Achievement
                </Button>
              </div>
            </CardContent>
          </Card>
          
          {/* Particle effects */}
          {Array.from({ length: 20 }).map((_, i) => (
            <motion.div
              key={i}
              initial={{
                opacity: 0,
                scale: 0,
                x: 0,
                y: 0
              }}
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
                x: (Math.random() - 0.5) * 400,
                y: (Math.random() - 0.5) * 400
              }}
              transition={{
                duration: 2,
                delay: Math.random() * 0.5
              }}
              className="absolute w-2 h-2 bg-yellow-400 rounded-full pointer-events-none"
            />
          ))}
        </motion.div>
      )}
    </AnimatePresence>
  );
}
```

### Level Progress Indicator
```tsx
export function SalesLevelProgress({ 
  currentLevel, 
  currentXP, 
  xpToNextLevel,
  totalXPForNextLevel 
}: SalesLevelProgressProps) {
  const progress = (currentXP / totalXPForNextLevel) * 100;
  
  const levelTitles = {
    1: "Rookie",
    2: "Prospector", 
    3: "Closer",
    4: "Account Manager",
    5: "Sales Ninja",
    6: "Deal Master",
    7: "Revenue Hero",
    8: "Sales Legend",
    9: "Enterprise Titan",
    10: "Sales God"
  };

  return (
    <Card className="bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-950/50 dark:to-purple-950/50">
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          {/* Level badge */}
          <div className="relative">
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-500 flex items-center justify-center"
            >
              <div className="text-white font-bold text-lg">{currentLevel}</div>
            </motion.div>
            
            {/* Level up glow effect */}
            <motion.div
              animate={{ 
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{ duration: 2, repeat: Infinity }}
              className="absolute inset-0 rounded-full bg-gradient-to-br from-indigo-400 to-purple-400 -z-10"
            />
          </div>
          
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">{levelTitles[currentLevel]}</h3>
              <Badge variant="secondary">
                Level {currentLevel}
              </Badge>
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{currentXP.toLocaleString()} XP</span>
                <span>{xpToNextLevel.toLocaleString()} to level {currentLevel + 1}</span>
              </div>
              
              <div className="w-full h-3 bg-muted rounded-full overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full relative"
                >
                  {/* Shimmer effect */}
                  <motion.div
                    animate={{ x: ['-100%', '100%'] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                  />
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

## 10. Easter Eggs and Surprises

### Konami Code Easter Egg
```tsx
export function KonamiCodeEasterEgg() {
  const [sequence, setSequence] = useState<string[]>([]);
  const [activated, setActivated] = useState(false);
  const konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      setSequence(prev => {
        const newSequence = [...prev, e.code].slice(-konamiCode.length);
        
        if (newSequence.join(',') === konamiCode.join(',')) {
          setActivated(true);
          setTimeout(() => setActivated(false), 5000);
          return [];
        }
        
        return newSequence;
      });
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  return (
    <AnimatePresence>
      {activated && (
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
          className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center"
        >
          {/* Rainbow background */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 opacity-20 animate-pulse" />
          
          {/* Secret message */}
          <Card className="bg-black text-green-400 font-mono border-green-400 shadow-2xl max-w-md mx-4">
            <CardContent className="p-6 text-center space-y-4">
              <motion.div
                animate={{ 
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                }}
                transition={{ duration: 1 }}
                className="text-4xl"
              >
                🎮
              </motion.div>
              
              <div className="space-y-2">
                <h2 className="text-xl font-bold">
                  ↑↑↓↓←→←→BA
                </h2>
                <p className="text-sm">
                  ACHIEVEMENT UNLOCKED: Konami Code Master!
                </p>
                <p className="text-xs opacity-75">
                  You found the secret! Here's 1000 bonus XP! 🏆
                </p>
              </div>
              
              <div className="text-xs opacity-50">
                <p>// Secret developer mode activated</p>
                <p>// All features unlocked for 24h</p>
              </div>
            </CardContent>
          </Card>
          
          {/* Matrix rain effect */}
          <div className="absolute inset-0 overflow-hidden">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ y: -100, opacity: 0 }}
                animate={{ y: '100vh', opacity: [0, 1, 0] }}
                transition={{ 
                  duration: 3,
                  delay: i * 0.2,
                  repeat: Infinity,
                  repeatDelay: 1
                }}
                className="absolute text-green-400 text-sm font-mono"
                style={{ left: `${Math.random() * 100}%` }}
              >
                {Array.from({ length: 10 }, () => 
                  Math.random() > 0.5 ? '1' : '0'
                ).join('')}
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
```

### Secret Dashboard Theme
```tsx
export function SecretDashboardTheme({ activated }: { activated: boolean }) {
  return (
    <AnimatePresence>
      {activated && (
        <>
          {/* Floating elements */}
          <div className="fixed inset-0 pointer-events-none z-40 overflow-hidden">
            {Array.from({ length: 15 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ 
                  opacity: 0,
                  x: Math.random() * window.innerWidth,
                  y: window.innerHeight + 100
                }}
                animate={{ 
                  opacity: [0, 0.7, 0],
                  y: -100,
                  rotate: 360
                }}
                transition={{
                  duration: 8 + Math.random() * 4,
                  delay: i * 0.5,
                  repeat: Infinity,
                  repeatDelay: Math.random() * 5
                }}
                className="absolute text-2xl"
              >
                {['💰', '🎯', '📈', '⭐', '🏆', '🎉'][Math.floor(Math.random() * 6)]}
              </motion.div>
            ))}
          </div>
          
          {/* Special cursor trail */}
          <style jsx>{`
            * {
              cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><text x="0" y="24" font-size="24">💎</text></svg>'), auto !important;
            }
          `}</style>
        </>
      )}
    </AnimatePresence>
  );
}
```

## Implementation Guidelines

### Performance Considerations
```typescript
// Optimize animations for performance
const optimizedVariants = {
  // Use transform properties for better performance
  transform: {
    x: 0,
    y: 0,
    scale: 1,
    rotate: 0
  },
  // Avoid animating layout properties
  avoidLayout: {
    // Don't animate: width, height, margin, padding, border
    // Use: transform, opacity, filter, backdrop-filter
  }
};

// Reduce motion for accessibility
const useReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Conditional animations
const AnimatedComponent = ({ children }) => {
  const shouldAnimate = !useReducedMotion();
  
  return shouldAnimate ? (
    <motion.div {...animationProps}>
      {children}
    </motion.div>
  ) : (
    <div>{children}</div>
  );
};
```

### Sound Integration (Optional)
```typescript
// Subtle sound effects for key interactions
const soundEffects = {
  success: '/sounds/success-chime.mp3',
  achievement: '/sounds/achievement-fanfare.mp3',
  error: '/sounds/error-gentle.mp3',
  notification: '/sounds/notification-soft.mp3',
  celebration: '/sounds/celebration-party.mp3'
};

const playSound = (type: keyof typeof soundEffects, volume = 0.3) => {
  if (user.preferences.soundEnabled) {
    const audio = new Audio(soundEffects[type]);
    audio.volume = volume;
    audio.play().catch(() => {
      // Gracefully handle autoplay restrictions
    });
  }
};
```

### Animation Timing and Curves
```css
/* Custom easing functions for personality */
:root {
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-gentle: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-energetic: cubic-bezier(0.17, 0.67, 0.83, 0.67);
  --ease-professional: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Duration tokens for consistency */
.micro-fast { transition-duration: 150ms; }
.micro-normal { transition-duration: 300ms; }
.macro-normal { transition-duration: 500ms; }
.celebration { transition-duration: 1200ms; }
```

## Cultural Sensitivity Guidelines

### Universal Gestures and Symbols
- ✅ Use: 👏 (applause), ⭐ (star), 🎯 (target), 📈 (growth)
- ⚠️ Be careful: 👍 (varies by culture), 🙏 (religious), 💪 (might be aggressive)
- ❌ Avoid: Country flags, religious symbols, political references

### Inclusive Language Patterns
```typescript
const inclusiveMessages = {
  // Instead of "guys" or "dudes"
  teamAddress: ["team", "everyone", "folks", "squad"],
  
  // Achievement messages that work globally
  success: [
    "Outstanding work!",
    "Excellent achievement!",
    "Way to go!",
    "Fantastic result!"
  ],
  
  // Encouraging without assumptions
  motivation: [
    "You've got this!",
    "Keep up the great work!",
    "Every step forward counts!",
    "Progress is progress!"
  ]
};
```

This comprehensive micro-interactions system transforms the CRM from a functional tool into a delightful experience that reduces stress, celebrates achievements, and creates memorable moments that users will want to share. The balance between professionalism and playfulness ensures the system remains appropriate for business contexts while injecting enough personality to create emotional connections.