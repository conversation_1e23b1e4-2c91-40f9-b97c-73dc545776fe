# Backend Implementation Plan

**Date:** 2025-01-29  
**Based on:** Architecture Review Phase 1 Audit  
**Target Duration:** 8 weeks (4 sprints)  
**Priority:** Critical Security & Performance Issues  

## Executive Summary

This implementation plan addresses critical issues identified in the architecture review, focusing on security vulnerabilities, performance bottlenecks, and operational readiness. The plan is structured in 4 phases with incremental improvements that maintain system stability while enhancing security and scalability.

**Tech Stack:**
- Go 1.21+ with Fiber v2 framework
- GORM v1.25+ with PostgreSQL 15+
- Redis 7+ for caching and session management
- JWT authentication with proper secret management
- Server-Sent Events (SSE) for real-time updates
- MinIO for S3-compatible file storage

## Phase 1: Critical Security & Infrastructure (Weeks 1-2)

### 1.1 Security Hardening (CRITICAL)

#### JWT Secret Management
**Issue:** Default JWT secret "your-secret-key" in production
**Priority:** CRITICAL - P0

**Implementation:**
```go
// internal/infrastructure/config/security.go
type SecurityConfig struct {
    JWTSecret           string `mapstructure:"jwt_secret" validate:"required,min=32"`
    JWTAccessTTL        time.Duration `mapstructure:"jwt_access_ttl" default:"15m"`
    JWTRefreshTTL       time.Duration `mapstructure:"jwt_refresh_ttl" default:"7d"`
    PasswordMinLength   int    `mapstructure:"password_min_length" default:"8"`
    PasswordRequireSpecial bool `mapstructure:"password_require_special" default:"true"`
    RateLimitWindow     time.Duration `mapstructure:"rate_limit_window" default:"1m"`
    RateLimitMax        int    `mapstructure:"rate_limit_max" default:"100"`
}

// Environment variable validation
func (s *SecurityConfig) Validate() error {
    if len(s.JWTSecret) < 32 {
        return errors.New("JWT_SECRET must be at least 32 characters")
    }
    if s.JWTSecret == "your-secret-key" {
        return errors.New("JWT_SECRET cannot be default value")
    }
    return nil
}
```

**Environment Variables:**
```bash
# Required production variables
CRM_AUTH_JWT_SECRET=<32-char-random-string>
CRM_AUTH_JWT_ACCESS_TTL=15m
CRM_AUTH_JWT_REFRESH_TTL=7d
CRM_SECURITY_BCRYPT_COST=12
```

#### Token Blacklisting with Redis
**Issue:** Logout doesn't invalidate tokens server-side
**Priority:** CRITICAL - P0

**Implementation:**
```go
// internal/domain/repositories/auth_repository.go
type AuthRepository interface {
    BlacklistToken(ctx context.Context, jti string, exp time.Time) error
    IsTokenBlacklisted(ctx context.Context, jti string) (bool, error)
    InvalidateUserTokens(ctx context.Context, userID uuid.UUID) error
}

// internal/adapters/cache/auth_cache.go
type AuthCache struct {
    redis redis.Client
}

func (c *AuthCache) BlacklistToken(ctx context.Context, jti string, exp time.Time) error {
    ttl := time.Until(exp)
    if ttl <= 0 {
        return nil // Token already expired
    }
    
    key := fmt.Sprintf("blacklist:token:%s", jti)
    return c.redis.Set(ctx, key, "1", ttl).Err()
}

// JWT token structure with JTI
type CustomClaims struct {
    UserID uuid.UUID `json:"user_id"`
    Email  string    `json:"email"`
    Role   string    `json:"role"`
    JTI    string    `json:"jti"` // JWT ID for blacklisting
    jwt.StandardClaims
}
```

#### Security Headers Middleware
**Issue:** Missing CORS, HSTS, CSP, X-Frame-Options
**Priority:** HIGH - P1

**Implementation:**
```go
// internal/adapters/http/middleware/security.go
func SecurityHeaders() fiber.Handler {
    return func(c *fiber.Ctx) error {
        // HSTS - Force HTTPS
        c.Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        
        // CSP - Content Security Policy
        c.Set("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")
        
        // X-Frame-Options - Prevent clickjacking
        c.Set("X-Frame-Options", "DENY")
        
        // X-Content-Type-Options - Prevent MIME sniffing
        c.Set("X-Content-Type-Options", "nosniff")
        
        // X-XSS-Protection
        c.Set("X-XSS-Protection", "1; mode=block")
        
        // Referrer Policy
        c.Set("Referrer-Policy", "strict-origin-when-cross-origin")
        
        return c.Next()
    }
}

// CORS configuration per environment
func CORSConfig() fiber.Handler {
    config := cors.Config{
        AllowOrigins:     os.Getenv("CORS_ALLOWED_ORIGINS"), // "https://yourdomain.com"
        AllowMethods:     "GET,POST,HEAD,PUT,DELETE,PATCH,OPTIONS",
        AllowHeaders:     "Origin, Content-Type, Accept, Authorization, X-Request-ID",
        AllowCredentials: true,
        MaxAge:           86400, // 24 hours
    }
    return cors.New(config)
}
```

#### Input Validation & Sanitization
**Issue:** No XSS protection, basic validation only
**Priority:** HIGH - P1

**Implementation:**
```go
// internal/adapters/http/middleware/validation.go
func InputSanitization() fiber.Handler {
    return func(c *fiber.Ctx) error {
        // Sanitize query parameters
        for key, values := range c.Context().QueryArgs().VisitAll {
            for i, value := range values {
                values[i] = html.EscapeString(string(value))
            }
        }
        
        // Sanitize JSON body (if applicable)
        if c.Get("Content-Type") == "application/json" {
            body := c.Body()
            if len(body) > 0 {
                var data interface{}
                if err := json.Unmarshal(body, &data); err == nil {
                    sanitized := sanitizeJSONRecursive(data)
                    if newBody, err := json.Marshal(sanitized); err == nil {
                        c.Context().SetBody(newBody)
                    }
                }
            }
        }
        
        return c.Next()
    }
}

// Comprehensive validation rules
type CreateCardRequest struct {
    Title       string                 `json:"title" validate:"required,min=1,max=255,no_script"`
    Description string                 `json:"description" validate:"max=2000,no_script"`
    CustomFields map[string]interface{} `json:"custom_fields" validate:"dive,no_script"`
    AssignedToID *uuid.UUID            `json:"assigned_to_id" validate:"omitempty,uuid"`
    PipelineID   uuid.UUID             `json:"pipeline_id" validate:"required,uuid"`
    StageID      uuid.UUID             `json:"stage_id" validate:"required,uuid"`
}
```

### 1.2 Redis Implementation (CRITICAL)

#### Redis Client Setup
**Issue:** Redis configured but not implemented
**Priority:** CRITICAL - P0

**Implementation:**
```go
// internal/infrastructure/cache/redis.go
type RedisClient struct {
    client redis.Client
    config RedisConfig
}

type RedisConfig struct {
    Host     string `mapstructure:"host" default:"localhost"`
    Port     string `mapstructure:"port" default:"6379"`
    Password string `mapstructure:"password"`
    DB       int    `mapstructure:"db" default:"0"`
    
    // Connection pool settings
    MaxRetries      int           `mapstructure:"max_retries" default:"3"`
    PoolSize        int           `mapstructure:"pool_size" default:"10"`
    MinIdleConns    int           `mapstructure:"min_idle_conns" default:"5"`
    PoolTimeout     time.Duration `mapstructure:"pool_timeout" default:"30s"`
    IdleTimeout     time.Duration `mapstructure:"idle_timeout" default:"5m"`
    IdleCheckFreq   time.Duration `mapstructure:"idle_check_freq" default:"1m"`
}

func NewRedisClient(config RedisConfig) (*RedisClient, error) {
    rdb := redis.NewClient(&redis.Options{
        Addr:         fmt.Sprintf("%s:%s", config.Host, config.Port),
        Password:     config.Password,
        DB:           config.DB,
        MaxRetries:   config.MaxRetries,
        PoolSize:     config.PoolSize,
        MinIdleConns: config.MinIdleConns,
        PoolTimeout:  config.PoolTimeout,
        IdleTimeout:  config.IdleTimeout,
    })
    
    // Test connection
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    if err := rdb.Ping(ctx).Err(); err != nil {
        return nil, fmt.Errorf("failed to connect to Redis: %w", err)
    }
    
    return &RedisClient{
        client: *rdb,
        config: config,
    }, nil
}
```

#### Cache Interface & Implementation
**Implementation:**
```go
// internal/domain/cache/cache.go
type Cache interface {
    Get(ctx context.Context, key string) (string, error)
    Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
    Delete(ctx context.Context, key string) error
    DeletePattern(ctx context.Context, pattern string) error
    Exists(ctx context.Context, key string) (bool, error)
    
    // Advanced operations
    SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error)
    Increment(ctx context.Context, key string) (int64, error)
    GetSet(ctx context.Context, key string, value interface{}) (string, error)
}

// Cache key generators
type CacheKeyGenerator struct{}

func (c *CacheKeyGenerator) UserKey(userID uuid.UUID) string {
    return fmt.Sprintf("user:%s", userID)
}

func (c *CacheKeyGenerator) CardKey(cardID uuid.UUID) string {
    return fmt.Sprintf("card:%s", cardID)
}

func (c *CacheKeyGenerator) PipelineCardsKey(pipelineID uuid.UUID) string {
    return fmt.Sprintf("pipeline:%s:cards", pipelineID)
}

func (c *CacheKeyGenerator) UserPermissionsKey(userID uuid.UUID) string {
    return fmt.Sprintf("user:%s:permissions", userID)
}
```

### 1.3 Structured Logging Implementation

#### Logging Infrastructure
**Issue:** Basic logging with no structure or levels
**Priority:** HIGH - P1

**Implementation:**
```go
// internal/infrastructure/logging/logger.go
type Logger interface {
    Debug(msg string, fields ...Field)
    Info(msg string, fields ...Field)
    Warn(msg string, fields ...Field)
    Error(msg string, fields ...Field)
    Fatal(msg string, fields ...Field)
    
    With(fields ...Field) Logger
    WithContext(ctx context.Context) Logger
}

type ZapLogger struct {
    logger *zap.Logger
    config LogConfig
}

type LogConfig struct {
    Level      string `mapstructure:"level" default:"info"`
    Format     string `mapstructure:"format" default:"json"`
    Output     string `mapstructure:"output" default:"stdout"`
    MaxSize    int    `mapstructure:"max_size" default:"100"`  // MB
    MaxBackups int    `mapstructure:"max_backups" default:"3"`
    MaxAge     int    `mapstructure:"max_age" default:"28"`    // days
}

// Request logging middleware
func RequestLogger(logger Logger) fiber.Handler {
    return fiber.New(fiber.Config{
        DisableStartupMessage: true,
    }).Use(func(c *fiber.Ctx) error {
        start := time.Now()
        
        // Generate request ID
        requestID := c.Get("X-Request-ID")
        if requestID == "" {
            requestID = uuid.New().String()
            c.Set("X-Request-ID", requestID)
        }
        
        // Add request ID to context
        c.SetUserContext(context.WithValue(c.UserContext(), "request_id", requestID))
        
        err := c.Next()
        
        // Log request
        fields := []Field{
            String("request_id", requestID),
            String("method", c.Method()),
            String("path", c.Path()),
            Int("status", c.Response().StatusCode()),
            Duration("duration", time.Since(start)),
            String("ip", c.IP()),
            String("user_agent", c.Get("User-Agent")),
        }
        
        if err != nil {
            fields = append(fields, String("error", err.Error()))
            logger.Error("Request failed", fields...)
        } else {
            logger.Info("Request completed", fields...)
        }
        
        return err
    })
}
```

## Phase 2: Performance Optimization & Caching (Weeks 3-4)

### 2.1 Query Result Caching

#### Repository Caching Layer
**Issue:** All queries hit database directly
**Priority:** HIGH - P1

**Implementation:**
```go
// internal/adapters/database/repositories/cached_card_repository.go
type CachedCardRepository struct {
    repo  repositories.CardRepository
    cache cache.Cache
    keyGen CacheKeyGenerator
}

func NewCachedCardRepository(repo repositories.CardRepository, cache cache.Cache) repositories.CardRepository {
    return &CachedCardRepository{
        repo:   repo,
        cache:  cache,
        keyGen: CacheKeyGenerator{},
    }
}

func (r *CachedCardRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Card, error) {
    key := r.keyGen.CardKey(id)
    
    // Try cache first
    if cached, err := r.cache.Get(ctx, key); err == nil {
        var card entities.Card
        if err := json.Unmarshal([]byte(cached), &card); err == nil {
            return &card, nil
        }
    }
    
    // Cache miss - get from database
    card, err := r.repo.GetByID(ctx, id)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    if cardJSON, err := json.Marshal(card); err == nil {
        _ = r.cache.Set(ctx, key, string(cardJSON), 15*time.Minute)
    }
    
    return card, nil
}

func (r *CachedCardRepository) Update(ctx context.Context, card *entities.Card) error {
    err := r.repo.Update(ctx, card)
    if err != nil {
        return err
    }
    
    // Invalidate related caches
    r.invalidateCardCaches(ctx, card.ID, card.PipelineID)
    
    return nil
}

func (r *CachedCardRepository) invalidateCardCaches(ctx context.Context, cardID, pipelineID uuid.UUID) {
    keys := []string{
        r.keyGen.CardKey(cardID),
        r.keyGen.PipelineCardsKey(pipelineID),
        fmt.Sprintf("dashboard:*"), // Invalidate dashboard caches
    }
    
    for _, key := range keys {
        if strings.Contains(key, "*") {
            _ = r.cache.DeletePattern(ctx, key)
        } else {
            _ = r.cache.Delete(ctx, key)
        }
    }
}
```

### 2.2 Session & User Caching

#### User Session Management
**Issue:** User data fetched on every request
**Priority:** HIGH - P1

**Implementation:**
```go
// internal/usecases/cached_auth_usecase.go
type CachedAuthUsecase struct {
    authUsecase usecases.AuthUsecase
    cache       cache.Cache
    keyGen      CacheKeyGenerator
}

func (uc *CachedAuthUsecase) ValidateToken(tokenString string) (*entities.User, error) {
    // Parse JWT to get user ID
    claims, err := uc.parseTokenClaims(tokenString)
    if err != nil {
        return nil, err
    }
    
    // Check if token is blacklisted
    if blacklisted, _ := uc.cache.Exists(context.Background(), 
        fmt.Sprintf("blacklist:token:%s", claims.JTI)); blacklisted {
        return nil, errors.New("token is blacklisted")
    }
    
    // Try to get user from cache
    userKey := uc.keyGen.UserKey(claims.UserID)
    if cached, err := uc.cache.Get(context.Background(), userKey); err == nil {
        var user entities.User
        if err := json.Unmarshal([]byte(cached), &user); err == nil {
            return &user, nil
        }
    }
    
    // Cache miss - validate normally and cache result
    user, err := uc.authUsecase.ValidateToken(tokenString)
    if err != nil {
        return nil, err
    }
    
    // Cache user data for 5 minutes
    if userJSON, err := json.Marshal(user); err == nil {
        _ = uc.cache.Set(context.Background(), userKey, string(userJSON), 5*time.Minute)
    }
    
    return user, nil
}
```

### 2.3 Dashboard Statistics Caching

#### Dashboard Performance
**Issue:** Dashboard statistics calculated on every request
**Priority:** MEDIUM - P2

**Implementation:**
```go
// internal/usecases/cached_dashboard_usecase.go
type DashboardStats struct {
    TotalCards      int64     `json:"total_cards"`
    CompletedCards  int64     `json:"completed_cards"`
    PendingCards    int64     `json:"pending_cards"`
    TotalRevenue    float64   `json:"total_revenue"`
    LastUpdated     time.Time `json:"last_updated"`
}

func (uc *CachedDashboardUsecase) GetStats(ctx context.Context, userID uuid.UUID) (*DashboardStats, error) {
    cacheKey := fmt.Sprintf("dashboard:stats:%s", userID)
    
    // Try cache first
    if cached, err := uc.cache.Get(ctx, cacheKey); err == nil {
        var stats DashboardStats
        if err := json.Unmarshal([]byte(cached), &stats); err == nil {
            return &stats, nil
        }
    }
    
    // Cache miss - calculate stats
    stats, err := uc.calculateStats(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    // Cache for 10 minutes
    if statsJSON, err := json.Marshal(stats); err == nil {
        _ = uc.cache.Set(ctx, cacheKey, string(statsJSON), 10*time.Minute)
    }
    
    return stats, nil
}
```

### 2.4 Database Optimization

#### Connection Pool Optimization
**Issue:** Default GORM connection settings
**Priority:** MEDIUM - P2

**Implementation:**
```go
// internal/infrastructure/database/connection.go
type DatabaseConfig struct {
    Host     string `mapstructure:"host" default:"localhost"`
    Port     string `mapstructure:"port" default:"5432"`
    Username string `mapstructure:"username" default:"postgres"`
    Password string `mapstructure:"password"`
    Database string `mapstructure:"database" default:"crm"`
    SSLMode  string `mapstructure:"ssl_mode" default:"disable"`
    
    // Performance settings
    MaxIdleConns    int           `mapstructure:"max_idle_conns" default:"25"`
    MaxOpenConns    int           `mapstructure:"max_open_conns" default:"100"`
    ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime" default:"1h"`
    ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time" default:"10m"`
    
    // GORM settings
    SlowThreshold             time.Duration `mapstructure:"slow_threshold" default:"200ms"`
    LogLevel                  int           `mapstructure:"log_level" default:"1"`
    IgnoreRecordNotFoundError bool          `mapstructure:"ignore_not_found" default:"true"`
}

func (config *DatabaseConfig) OptimizeConnection(sqlDB *sql.DB) error {
    sqlDB.SetMaxIdleConns(config.MaxIdleConns)
    sqlDB.SetMaxOpenConns(config.MaxOpenConns)
    sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)
    sqlDB.SetConnMaxIdleTime(config.ConnMaxIdleTime)
    
    return nil
}
```

#### Critical Index Implementation
**Issue:** Missing composite indexes for common queries
**Priority:** MEDIUM - P2

**SQL Implementation:**
```sql
-- migrations/007_add_performance_indexes.sql
-- Critical indexes identified in architecture review

-- Cards by pipeline and stage (most common query)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_pipeline_stage_active 
ON cards(pipeline_id, stage_id) WHERE deleted_at IS NULL;

-- Cards assigned to user with date ordering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_assigned_user_active 
ON cards(assigned_to_id, created_at DESC) WHERE deleted_at IS NULL;

-- Activities by entity for audit trails
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_entity_recent 
ON activities(entity_type, entity_id, created_at DESC);

-- Users by email for authentication (if not exists)
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active 
ON users(email) WHERE deleted_at IS NULL;

-- Custom fields GIN index optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_custom_fields_gin 
ON cards USING GIN (custom_fields) WHERE deleted_at IS NULL;

-- Pipeline stages ordered
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stages_pipeline_order 
ON stages(pipeline_id, sort_order) WHERE deleted_at IS NULL;

-- Comments by entity for quick retrieval
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_entity_recent 
ON comments(entity_type, entity_id, created_at DESC) WHERE deleted_at IS NULL;

-- Analyze tables after index creation
ANALYZE cards;
ANALYZE activities;
ANALYZE stages;
ANALYZE comments;
```

## Phase 3: API Improvements & Monitoring (Weeks 5-6)

### 3.1 API Endpoint Enhancements

#### Bulk Operations
**Issue:** No bulk operations for efficiency
**Priority:** MEDIUM - P2

**Implementation:**
```go
// internal/adapters/http/handlers/bulk_handler.go
type BulkHandler struct {
    cardUsecase     usecases.CardUsecase
    companyUsecase  usecases.CompanyUsecase
    contactUsecase  usecases.ContactUsecase
    logger          logging.Logger
}

type BulkCardUpdateRequest struct {
    CardIDs   []uuid.UUID            `json:"card_ids" validate:"required,min=1,max=100"`
    Updates   map[string]interface{} `json:"updates" validate:"required"`
    StageID   *uuid.UUID             `json:"stage_id,omitempty"`
    AssigneeID *uuid.UUID            `json:"assignee_id,omitempty"`
}

func (h *BulkHandler) BulkUpdateCards(c *fiber.Ctx) error {
    ctx := c.UserContext()
    
    var req BulkCardUpdateRequest
    if err := c.BodyParser(&req); err != nil {
        return c.Status(400).JSON(handlers.ErrorResponse{
            Error: "Invalid request body",
            Code:  "INVALID_REQUEST",
        })
    }
    
    // Validate request
    if err := h.validator.Struct(&req); err != nil {
        return c.Status(400).JSON(handlers.ErrorResponse{
            Error: err.Error(),
            Code:  "VALIDATION_ERROR",
        })
    }
    
    // Process bulk update
    results, err := h.cardUsecase.BulkUpdate(ctx, &req)
    if err != nil {
        h.logger.Error("Bulk update failed", 
            logging.String("error", err.Error()),
            logging.Int("card_count", len(req.CardIDs)),
        )
        return c.Status(500).JSON(handlers.ErrorResponse{
            Error: "Bulk update failed",
            Code:  "BULK_UPDATE_ERROR",
        })
    }
    
    return c.JSON(handlers.SuccessResponse{
        Message: "Bulk update completed",
        Data:    results,
    })
}

// Bulk delete with soft deletion
func (h *BulkHandler) BulkDeleteCards(c *fiber.Ctx) error {
    ctx := c.UserContext()
    
    var req struct {
        CardIDs []uuid.UUID `json:"card_ids" validate:"required,min=1,max=50"`
    }
    
    if err := c.BodyParser(&req); err != nil {
        return c.Status(400).JSON(handlers.ErrorResponse{
            Error: "Invalid request body",
        })
    }
    
    results, err := h.cardUsecase.BulkDelete(ctx, req.CardIDs)
    if err != nil {
        return c.Status(500).JSON(handlers.ErrorResponse{
            Error: "Bulk delete failed",
        })
    }
    
    return c.JSON(handlers.SuccessResponse{
        Message: fmt.Sprintf("Deleted %d cards", results.SuccessCount),
        Data:    results,
    })
}
```

#### Field Selection & Sparse Fieldsets
**Issue:** Always returns all fields
**Priority:** MEDIUM - P2

**Implementation:**
```go
// internal/adapters/http/middleware/field_selector.go
func FieldSelector() fiber.Handler {
    return func(c *fiber.Ctx) error {
        fields := c.Query("fields")
        if fields != "" {
            selectedFields := strings.Split(fields, ",")
            c.SetUserContext(context.WithValue(c.UserContext(), "selected_fields", selectedFields))
        }
        return c.Next()
    }
}

// internal/usecases/card_usecase.go - Modified to support field selection
func (uc *CardUsecase) List(ctx context.Context, opts ListOptions) (*ListResult[entities.Card], error) {
    // Get selected fields from context
    if selectedFields, ok := ctx.Value("selected_fields").([]string); ok {
        opts.Fields = selectedFields
    }
    
    cards, total, err := uc.repo.ListWithFields(ctx, opts)
    if err != nil {
        return nil, err
    }
    
    return &ListResult[entities.Card]{
        Items:      cards,
        Total:      total,
        Page:       opts.Page,
        PerPage:    opts.PerPage,
        TotalPages: int(math.Ceil(float64(total) / float64(opts.PerPage))),
    }, nil
}
```

#### Enhanced Pagination
**Issue:** Missing pagination metadata
**Priority:** LOW - P3

**Implementation:**
```go
// internal/domain/common/pagination.go
type PaginationRequest struct {
    Page    int    `query:"page" validate:"min=1" default:"1"`
    PerPage int    `query:"per_page" validate:"min=1,max=100" default:"20"`
    Sort    string `query:"sort" validate:"oneof=created_at updated_at title" default:"created_at"`
    Order   string `query:"order" validate:"oneof=asc desc" default:"desc"`
    Search  string `query:"search" validate:"max=255"`
}

type PaginatedResponse[T any] struct {
    Data       []T              `json:"data"`
    Pagination PaginationMeta   `json:"pagination"`
    Links      PaginationLinks  `json:"links"`
}

type PaginationMeta struct {
    CurrentPage int   `json:"current_page"`
    PerPage     int   `json:"per_page"`
    Total       int64 `json:"total"`
    TotalPages  int   `json:"total_pages"`
    HasNext     bool  `json:"has_next"`
    HasPrev     bool  `json:"has_prev"`
}

type PaginationLinks struct {
    First string  `json:"first"`
    Last  string  `json:"last"`
    Next  *string `json:"next"`
    Prev  *string `json:"prev"`
}
```

### 3.2 Rate Limiting Enhancements

#### Per-Endpoint Rate Limiting
**Issue:** Only global rate limiting
**Priority:** MEDIUM - P2

**Implementation:**
```go
// internal/adapters/http/middleware/rate_limiter.go
type RateLimitConfig struct {
    WindowSize time.Duration
    MaxRequests int
    KeyGenerator func(c *fiber.Ctx) string
}

var EndpointLimits = map[string]RateLimitConfig{
    "POST:/api/v1/auth/login": {
        WindowSize:   time.Minute,
        MaxRequests:  5,  // Strict limit for login attempts
        KeyGenerator: func(c *fiber.Ctx) string { return c.IP() },
    },
    "POST:/api/v1/auth/register": {
        WindowSize:   time.Hour,
        MaxRequests:  3,  // Very strict for registration
        KeyGenerator: func(c *fiber.Ctx) string { return c.IP() },
    },
    "GET:/api/v1/cards": {
        WindowSize:   time.Minute,
        MaxRequests:  100,
        KeyGenerator: func(c *fiber.Ctx) string { 
            userID := c.Locals("user_id")
            return fmt.Sprintf("user:%v", userID)
        },
    },
    "POST:/api/v1/cards": {
        WindowSize:   time.Minute,
        MaxRequests:  20,
        KeyGenerator: func(c *fiber.Ctx) string { 
            userID := c.Locals("user_id")
            return fmt.Sprintf("user:%v", userID)
        },
    },
}

func PerEndpointRateLimit(cache cache.Cache) fiber.Handler {
    return func(c *fiber.Ctx) error {
        endpoint := fmt.Sprintf("%s:%s", c.Method(), c.Path())
        
        config, exists := EndpointLimits[endpoint]
        if !exists {
            return c.Next() // No specific limit, allow through
        }
        
        key := fmt.Sprintf("ratelimit:%s:%s", endpoint, config.KeyGenerator(c))
        
        // Implement sliding window rate limiting
        count, err := cache.Increment(c.UserContext(), key)
        if err != nil {
            // If cache fails, allow request but log error
            log.Error("Rate limit cache error", err)
            return c.Next()
        }
        
        if count == 1 {
            // Set expiration on first request
            cache.Set(c.UserContext(), key, 1, config.WindowSize)
        }
        
        if count > int64(config.MaxRequests) {
            return c.Status(429).JSON(fiber.Map{
                "error": "Rate limit exceeded",
                "retry_after": config.WindowSize.Seconds(),
            })
        }
        
        return c.Next()
    }
}
```

### 3.3 Monitoring & Observability

#### Health Check Endpoints
**Issue:** Basic health check only
**Priority:** HIGH - P1

**Implementation:**
```go
// internal/adapters/http/handlers/health_handler.go
type HealthHandler struct {
    db     *gorm.DB
    redis  cache.Cache
    minio  *minio.Client
    logger logging.Logger
}

type HealthStatus struct {
    Status      string                    `json:"status"`
    Version     string                    `json:"version"`
    Timestamp   time.Time                 `json:"timestamp"`
    Environment string                    `json:"environment"`
    Checks      map[string]ComponentHealth `json:"checks"`
    Uptime      time.Duration             `json:"uptime"`
}

type ComponentHealth struct {
    Status    string        `json:"status"`
    Message   string        `json:"message,omitempty"`
    Duration  time.Duration `json:"response_time"`
    Timestamp time.Time     `json:"timestamp"`
}

var startTime = time.Now()

func (h *HealthHandler) Health(c *fiber.Ctx) error {
    ctx := c.UserContext()
    
    checks := make(map[string]ComponentHealth)
    overallStatus := "healthy"
    
    // Database health check
    dbStart := time.Now()
    if err := h.db.WithContext(ctx).Raw("SELECT 1").Error; err != nil {
        checks["database"] = ComponentHealth{
            Status:    "unhealthy",
            Message:   err.Error(),
            Duration:  time.Since(dbStart),
            Timestamp: time.Now(),
        }
        overallStatus = "unhealthy"
    } else {
        checks["database"] = ComponentHealth{
            Status:    "healthy",
            Duration:  time.Since(dbStart),
            Timestamp: time.Now(),
        }
    }
    
    // Redis health check
    redisStart := time.Now()
    if err := h.redis.Get(ctx, "health_check"); err != nil && !errors.Is(err, redis.Nil) {
        checks["redis"] = ComponentHealth{
            Status:    "unhealthy",
            Message:   err.Error(),
            Duration:  time.Since(redisStart),
            Timestamp: time.Now(),
        }
        overallStatus = "degraded" // Redis failure is not critical
    } else {
        checks["redis"] = ComponentHealth{
            Status:    "healthy",
            Duration:  time.Since(redisStart),
            Timestamp: time.Now(),
        }
    }
    
    // MinIO health check
    minioStart := time.Now()
    if _, err := h.minio.BucketExists(ctx, os.Getenv("MINIO_BUCKET")); err != nil {
        checks["storage"] = ComponentHealth{
            Status:    "unhealthy",
            Message:   err.Error(),
            Duration:  time.Since(minioStart),
            Timestamp: time.Now(),
        }
        overallStatus = "degraded"
    } else {
        checks["storage"] = ComponentHealth{
            Status:    "healthy",
            Duration:  time.Since(minioStart),
            Timestamp: time.Now(),
        }
    }
    
    health := HealthStatus{
        Status:      overallStatus,
        Version:     os.Getenv("APP_VERSION"),
        Timestamp:   time.Now(),
        Environment: os.Getenv("APP_ENV"),
        Checks:      checks,
        Uptime:      time.Since(startTime),
    }
    
    statusCode := 200
    if overallStatus == "unhealthy" {
        statusCode = 503
    } else if overallStatus == "degraded" {
        statusCode = 200 // Still operational
    }
    
    return c.Status(statusCode).JSON(health)
}

// Readiness check for Kubernetes
func (h *HealthHandler) Ready(c *fiber.Ctx) error {
    ctx := c.UserContext()
    
    // Check critical dependencies only
    if err := h.db.WithContext(ctx).Raw("SELECT 1").Error; err != nil {
        return c.Status(503).JSON(fiber.Map{
            "status": "not ready",
            "reason": "database unavailable",
        })
    }
    
    return c.JSON(fiber.Map{
        "status": "ready",
    })
}

// Liveness check for Kubernetes
func (h *HealthHandler) Live(c *fiber.Ctx) error {
    return c.JSON(fiber.Map{
        "status": "alive",
        "timestamp": time.Now(),
    })
}
```

#### Metrics Collection
**Issue:** No metrics or performance monitoring
**Priority:** MEDIUM - P2

**Implementation:**
```go
// internal/infrastructure/metrics/prometheus.go
var (
    httpRequestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    httpRequestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
            Buckets: prometheus.DefBuckets,
        },
        []string{"method", "endpoint"},
    )
    
    databaseConnections = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "database_connections",
            Help: "Current database connections",
        },
        []string{"state"},
    )
    
    cacheHitRate = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cache_requests_total",
            Help: "Total cache requests",
        },
        []string{"type", "result"},
    )
)

func init() {
    prometheus.MustRegister(
        httpRequestsTotal,
        httpRequestDuration,
        databaseConnections,
        cacheHitRate,
    )
}

// Middleware for metrics collection
func PrometheusMetrics() fiber.Handler {
    return func(c *fiber.Ctx) error {
        start := time.Now()
        
        err := c.Next()
        
        duration := time.Since(start).Seconds()
        status := strconv.Itoa(c.Response().StatusCode())
        
        httpRequestsTotal.WithLabelValues(
            c.Method(),
            c.Path(),
            status,
        ).Inc()
        
        httpRequestDuration.WithLabelValues(
            c.Method(),
            c.Path(),
        ).Observe(duration)
        
        return err
    }
}
```

## Phase 4: Advanced Features & Microservices Prep (Weeks 7-8)

### 4.1 Transaction Management

#### Transaction Scope Implementation
**Issue:** No distributed transaction support
**Priority:** MEDIUM - P2

**Implementation:**
```go
// internal/infrastructure/database/transaction.go
type TransactionManager interface {
    WithTransaction(ctx context.Context, fn func(tx *gorm.DB) error) error
    BeginTransaction(ctx context.Context) (*gorm.DB, error)
    CommitTransaction(tx *gorm.DB) error
    RollbackTransaction(tx *gorm.DB) error
}

type GORMTransactionManager struct {
    db *gorm.DB
}

func (tm *GORMTransactionManager) WithTransaction(ctx context.Context, fn func(tx *gorm.DB) error) error {
    tx := tm.db.WithContext(ctx).Begin()
    if tx.Error != nil {
        return tx.Error
    }
    
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
            panic(r)
        }
    }()
    
    if err := fn(tx); err != nil {
        tx.Rollback()
        return err
    }
    
    return tx.Commit().Error
}

// Use case with transaction support
func (uc *CardUsecase) CreateWithActivity(ctx context.Context, req *CreateCardRequest) (*entities.Card, error) {
    var card *entities.Card
    
    err := uc.txManager.WithTransaction(ctx, func(tx *gorm.DB) error {
        // Create repository with transaction
        txRepo := repositories.NewCardRepository(tx)
        
        // Create card
        newCard := &entities.Card{
            Title:        req.Title,
            Description:  req.Description,
            PipelineID:   req.PipelineID,
            StageID:      req.StageID,
            CustomFields: req.CustomFields,
        }
        
        if err := txRepo.Create(ctx, newCard); err != nil {
            return fmt.Errorf("failed to create card: %w", err)
        }
        
        // Create activity log
        activity := &entities.Activity{
            EntityType: "card",
            EntityID:   newCard.ID,
            Action:     "created",
            UserID:     getUserID(ctx),
            Details:    map[string]interface{}{"title": newCard.Title},
        }
        
        activityRepo := repositories.NewActivityRepository(tx)
        if err := activityRepo.Create(ctx, activity); err != nil {
            return fmt.Errorf("failed to create activity: %w", err)
        }
        
        card = newCard
        return nil
    })
    
    if err != nil {
        uc.logger.Error("Transaction failed", 
            logging.String("operation", "CreateCardWithActivity"),
            logging.String("error", err.Error()),
        )
        return nil, err
    }
    
    // Invalidate caches outside transaction
    go func() {
        uc.cache.DeletePattern(context.Background(), 
            fmt.Sprintf("pipeline:%s:*", card.PipelineID))
    }()
    
    return card, nil
}
```

#### Optimistic Locking
**Issue:** No concurrent update protection
**Priority:** LOW - P3

**Implementation:**
```go
// internal/domain/entities/base.go - Add version field
type BaseEntity struct {
    ID        uuid.UUID      `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
    CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
    UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
    DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
    Version   int64          `json:"version" gorm:"default:0"` // For optimistic locking
}

// Repository with version checking
func (r *cardRepository) Update(ctx context.Context, card *entities.Card) error {
    result := r.db.WithContext(ctx).
        Model(card).
        Where("id = ? AND version = ?", card.ID, card.Version).
        Updates(map[string]interface{}{
            "title":         card.Title,
            "description":   card.Description,
            "custom_fields": card.CustomFields,
            "version":       gorm.Expr("version + 1"),
        })
    
    if result.Error != nil {
        return result.Error
    }
    
    if result.RowsAffected == 0 {
        return errors.New("record has been modified by another user")
    }
    
    card.Version++
    return nil
}
```

### 4.2 Background Job Processing

#### Job Queue Implementation
**Issue:** No background processing for heavy operations
**Priority:** MEDIUM - P2

**Implementation:**
```go
// internal/infrastructure/queue/job_queue.go
type JobQueue interface {
    Enqueue(ctx context.Context, job Job) error
    Process(ctx context.Context, jobType string, handler JobHandler) error
    StartWorkers(ctx context.Context, concurrency int) error
    Stop() error
}

type Job struct {
    ID       string                 `json:"id"`
    Type     string                 `json:"type"`
    Payload  map[string]interface{} `json:"payload"`
    Priority int                    `json:"priority"`
    Retry    int                    `json:"retry"`
    MaxRetry int                    `json:"max_retry"`
}

type JobHandler func(ctx context.Context, job Job) error

// Redis-based job queue implementation
type RedisJobQueue struct {
    redis    redis.Client
    handlers map[string]JobHandler
    done     chan struct{}
    wg       sync.WaitGroup
}

func (q *RedisJobQueue) Enqueue(ctx context.Context, job Job) error {
    if job.ID == "" {
        job.ID = uuid.New().String()
    }
    
    jobJSON, err := json.Marshal(job)
    if err != nil {
        return err
    }
    
    queueKey := fmt.Sprintf("queue:%s", job.Type)
    return q.redis.LPush(ctx, queueKey, jobJSON).Err()
}

// Background job processors
func (uc *EmailUsecase) SendWelcomeEmailAsync(ctx context.Context, userID uuid.UUID) error {
    job := queue.Job{
        Type: "send_welcome_email",
        Payload: map[string]interface{}{
            "user_id": userID.String(),
        },
        MaxRetry: 3,
    }
    
    return uc.jobQueue.Enqueue(ctx, job)
}

func (uc *EmailUsecase) HandleSendWelcomeEmail(ctx context.Context, job queue.Job) error {
    userIDStr, ok := job.Payload["user_id"].(string)
    if !ok {
        return errors.New("invalid user_id in job payload")
    }
    
    userID, err := uuid.Parse(userIDStr)
    if err != nil {
        return fmt.Errorf("invalid user_id format: %w", err)
    }
    
    return uc.SendWelcomeEmail(ctx, userID)
}
```

### 4.3 Error Handling Improvements

#### Error Classification System
**Issue:** All errors treated the same
**Priority:** HIGH - P1

**Implementation:**
```go
// internal/domain/errors/errors.go
type ErrorCode string

const (
    ErrCodeValidation     ErrorCode = "VALIDATION_ERROR"
    ErrCodeNotFound       ErrorCode = "NOT_FOUND"
    ErrCodeUnauthorized   ErrorCode = "UNAUTHORIZED"
    ErrCodeForbidden      ErrorCode = "FORBIDDEN"
    ErrCodeConflict       ErrorCode = "CONFLICT"
    ErrCodeInternal       ErrorCode = "INTERNAL_ERROR"
    ErrCodeExternalAPI    ErrorCode = "EXTERNAL_API_ERROR"
    ErrCodeRateLimit      ErrorCode = "RATE_LIMIT_EXCEEDED"
)

type AppError struct {
    Code       ErrorCode              `json:"code"`
    Message    string                 `json:"message"`
    Details    map[string]interface{} `json:"details,omitempty"`
    Cause      error                  `json:"-"`
    Timestamp  time.Time              `json:"timestamp"`
    RequestID  string                 `json:"request_id"`
    StackTrace string                 `json:"stack_trace,omitempty"`
}

func (e *AppError) Error() string {
    return e.Message
}

func NewValidationError(message string, details map[string]interface{}) *AppError {
    return &AppError{
        Code:      ErrCodeValidation,
        Message:   message,
        Details:   details,
        Timestamp: time.Now(),
    }
}

// Global error handler with classification
func ErrorHandler(logger logging.Logger) fiber.ErrorHandler {
    return func(c *fiber.Ctx, err error) error {
        var appErr *AppError
        
        if errors.As(err, &appErr) {
            // Handle application errors
            statusCode := getStatusCodeForError(appErr.Code)
            
            // Add request ID if available
            if requestID := c.Get("X-Request-ID"); requestID != "" {
                appErr.RequestID = requestID
            }
            
            // Log error with context
            logger.Error("Request failed",
                logging.String("error_code", string(appErr.Code)),
                logging.String("message", appErr.Message),
                logging.String("request_id", appErr.RequestID),
                logging.String("path", c.Path()),
                logging.String("method", c.Method()),
            )
            
            return c.Status(statusCode).JSON(appErr)
        }
        
        // Handle Fiber errors
        var fiberErr *fiber.Error
        if errors.As(err, &fiberErr) {
            appErr = &AppError{
                Code:      getErrorCodeForStatus(fiberErr.Code),
                Message:   fiberErr.Message,
                Timestamp: time.Now(),
                RequestID: c.Get("X-Request-ID"),
            }
            
            return c.Status(fiberErr.Code).JSON(appErr)
        }
        
        // Handle unknown errors
        requestID := c.Get("X-Request-ID")
        logger.Error("Unhandled error",
            logging.String("error", err.Error()),
            logging.String("request_id", requestID),
            logging.String("path", c.Path()),
        )
        
        appErr = &AppError{
            Code:      ErrCodeInternal,
            Message:   "Internal server error",
            Timestamp: time.Now(),
            RequestID: requestID,
        }
        
        return c.Status(500).JSON(appErr)
    }
}
```

### 4.4 Microservices Preparation

#### Service Extraction Planning
**Issue:** Monolithic structure not ready for microservices
**Priority:** LOW - P3

**Implementation Strategy:**
```go
// Phase 1: Extract User Management Service
// internal/services/user_service/
├── cmd/
│   └── server/
│       └── main.go
├── internal/
│   ├── domain/
│   │   ├── entities/
│   │   │   ├── user.go
│   │   │   ├── role.go
│   │   │   └── permission.go
│   │   └── repositories/
│   ├── usecases/
│   ├── adapters/
│   │   ├── http/
│   │   ├── database/
│   │   └── cache/
│   └── infrastructure/
└── configs/

// Service communication interface
type UserServiceClient interface {
    ValidateToken(ctx context.Context, token string) (*User, error)
    GetUserPermissions(ctx context.Context, userID uuid.UUID) ([]Permission, error)
    CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
}

// gRPC service definition
service UserService {
    rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
    rpc GetUserPermissions(GetUserPermissionsRequest) returns (GetUserPermissionsResponse);
    rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
}

// HTTP client implementation for backward compatibility
type HTTPUserServiceClient struct {
    baseURL string
    client  *http.Client
    logger  logging.Logger
}

func (c *HTTPUserServiceClient) ValidateToken(ctx context.Context, token string) (*User, error) {
    req, err := http.NewRequestWithContext(ctx, "POST", 
        c.baseURL+"/internal/validate-token", 
        bytes.NewBuffer([]byte(fmt.Sprintf(`{"token":"%s"}`, token))))
    
    if err != nil {
        return nil, err
    }
    
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := c.client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != 200 {
        return nil, fmt.Errorf("user service returned status %d", resp.StatusCode)
    }
    
    var result struct {
        User *User `json:"user"`
    }
    
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return nil, err
    }
    
    return result.User, nil
}
```

#### API Gateway Configuration
**Issue:** No centralized API routing
**Priority:** LOW - P3

**Implementation:**
```yaml
# api-gateway.yml - Kong configuration
version: "3.1"

services:
  - name: user-service
    url: http://user-service:8081
    routes:
      - name: auth-routes
        paths:
          - /api/v1/auth
        strip_path: false
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          policy: local

  - name: crm-core-service
    url: http://crm-core:8082
    routes:
      - name: cards-routes
        paths:
          - /api/v1/cards
          - /api/v1/pipelines
        strip_path: false
    plugins:
      - name: jwt
        config:
          claims_to_verify:
            - exp
            - nbf
          key_claim_name: iss
          secret_is_base64: false

  - name: communication-service
    url: http://communication:8083
    routes:
      - name: comments-routes
        paths:
          - /api/v1/comments
          - /api/v1/notifications
```

## Implementation Timeline & Deliverables

### Sprint 1 (Weeks 1-2): Critical Security & Infrastructure
**Deliverables:**
- ✅ JWT secret management with environment validation
- ✅ Redis client implementation with connection pooling
- ✅ Token blacklisting system
- ✅ Security headers middleware (HSTS, CSP, CORS)
- ✅ Input validation and XSS protection
- ✅ Structured logging with Zap
- ✅ Basic caching layer for user sessions

**Success Metrics:**
- All security vulnerabilities from audit addressed
- Redis successfully integrated and functional
- JWT tokens properly invalidated on logout
- Structured logs with request IDs implemented

### Sprint 2 (Weeks 3-4): Performance & Database Optimization
**Deliverables:**
- ✅ Query result caching implementation
- ✅ Database connection pool optimization
- ✅ Critical composite indexes deployment
- ✅ Dashboard statistics caching
- ✅ Cache invalidation strategy
- ✅ Connection monitoring

**Success Metrics:**
- 50%+ reduction in database query load
- Sub-100ms response times for cached queries
- Dashboard loads in <500ms
- Database connection utilization optimized

### Sprint 3 (Weeks 5-6): API Enhancement & Monitoring
**Deliverables:**
- ✅ Bulk operations for cards/contacts/companies
- ✅ Field selection and sparse fieldsets
- ✅ Enhanced pagination with metadata
- ✅ Per-endpoint rate limiting
- ✅ Comprehensive health checks
- ✅ Prometheus metrics collection
- ✅ Error classification system

**Success Metrics:**
- API documentation coverage 100%
- Health check response <100ms
- Metrics collection for all endpoints
- Rate limiting properly configured per endpoint

### Sprint 4 (Weeks 7-8): Advanced Features & Architecture
**Deliverables:**
- ✅ Transaction management with rollback support
- ✅ Background job processing system
- ✅ Optimistic locking implementation
- ✅ Microservices preparation (user service extraction plan)
- ✅ API Gateway configuration
- ✅ Comprehensive error tracking

**Success Metrics:**
- Complex operations properly transacted
- Background jobs processing reliably
- Concurrent update conflicts handled gracefully
- User service extraction plan documented

## Risk Assessment & Mitigation

### High-Risk Items
1. **Redis Migration Risk**
   - **Risk:** Cache failures causing system instability
   - **Mitigation:** Graceful degradation, circuit breaker pattern
   - **Fallback:** Bypass cache on Redis failure

2. **Database Index Creation**
   - **Risk:** Long-running index creation blocking production
   - **Mitigation:** Use `CREATE INDEX CONCURRENTLY` for zero-downtime
   - **Timeline:** Off-peak hours deployment

3. **Token Blacklisting Impact**
   - **Risk:** Performance impact on every request
   - **Mitigation:** Efficient Redis lookups, connection pooling
   - **Monitoring:** Token validation latency metrics

### Medium-Risk Items
1. **Cache Invalidation Complexity**
   - **Risk:** Stale data from improper cache invalidation
   - **Mitigation:** Conservative TTL, pattern-based invalidation
   - **Testing:** Comprehensive cache invalidation testing

2. **Bulk Operations Performance**
   - **Risk:** Large bulk operations causing timeouts
   - **Mitigation:** Batch size limits, background processing
   - **Limits:** Maximum 100 items per bulk operation

## Testing Strategy

### Unit Testing Requirements
```go
// Example test structure for critical components
func TestCachedCardRepository_GetByID(t *testing.T) {
    tests := []struct {
        name          string
        cardID        uuid.UUID
        cacheHit      bool
        cacheData     string
        dbResult      *entities.Card
        dbError       error
        expectedCard  *entities.Card
        expectedError error
    }{
        {
            name:     "cache hit returns cached data",
            cardID:   uuid.New(),
            cacheHit: true,
            cacheData: `{"id":"123","title":"Test Card"}`,
            expectedCard: &entities.Card{Title: "Test Card"},
        },
        // More test cases...
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

### Integration Testing Focus Areas
1. **Redis Integration**
   - Cache hit/miss scenarios
   - Connection failure handling
   - Data serialization/deserialization

2. **Database Transactions**
   - Multi-entity operations
   - Rollback scenarios
   - Concurrent access patterns

3. **Authentication Flow**
   - Token validation with cache
   - Token blacklisting
   - Session management

### Performance Testing Requirements
1. **Load Testing Targets**
   - 1000 concurrent users
   - 95th percentile response time <500ms
   - Error rate <0.1%

2. **Cache Performance**
   - Cache hit ratio >80%
   - Cache response time <10ms
   - Cache failover testing

## Monitoring & Alerting Setup

### Key Performance Indicators (KPIs)
```yaml
# Prometheus alerts configuration
groups:
  - name: crm-backend
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.01
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          
      - alert: CacheHighMissRate
        expr: rate(cache_requests_total{result="miss"}[5m]) / rate(cache_requests_total[5m]) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Cache miss rate is high"
          
      - alert: DatabaseConnectionsHigh
        expr: database_connections{state="active"} > 80
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Database connections approaching limit"
```

### Dashboard Metrics
1. **Application Metrics**
   - Request rate and latency
   - Error rate by endpoint
   - Cache hit/miss ratios
   - Active user sessions

2. **Infrastructure Metrics**
   - Database connection pool usage
   - Redis memory usage
   - CPU and memory utilization
   - Disk I/O patterns

## Security Compliance Checklist

### OWASP Top 10 Compliance
- ✅ **A01 - Broken Access Control**: JWT validation, RBAC implementation
- ✅ **A02 - Cryptographic Failures**: Proper JWT secret management, bcrypt
- ✅ **A03 - Injection**: GORM ORM usage, input validation
- ✅ **A04 - Insecure Design**: Clean architecture, security by design
- ✅ **A05 - Security Misconfiguration**: Security headers, CORS configuration
- ✅ **A06 - Vulnerable Components**: Dependency updates, security scanning
- ✅ **A07 - Authentication Failures**: Token blacklisting, rate limiting
- ✅ **A08 - Software Integrity Failures**: Input validation, sanitization
- ✅ **A09 - Logging Failures**: Structured logging, error tracking
- ✅ **A10 - SSRF**: Input validation, network segmentation

### Production Readiness Checklist
- ✅ Environment-specific configuration management
- ✅ Secrets management (JWT secret, database passwords)
- ✅ TLS/HTTPS enforcement
- ✅ Rate limiting and DDoS protection
- ✅ Input validation and sanitization
- ✅ Error handling without information disclosure
- ✅ Audit logging for security events
- ✅ Health checks and monitoring
- ✅ Backup and recovery procedures
- ✅ Incident response procedures

## Conclusion

This implementation plan addresses all critical issues identified in the architecture review while maintaining system stability and performance. The phased approach ensures that security vulnerabilities are resolved first, followed by performance improvements and advanced features.

**Key Success Factors:**
1. **Security First**: All critical security issues addressed in Phase 1
2. **Performance Focus**: Caching implementation provides immediate performance gains
3. **Operational Excellence**: Comprehensive monitoring and error handling
4. **Future-Ready**: Architecture prepared for microservices migration

**Next Steps:**
1. Review and approve this implementation plan
2. Set up development environment with Redis
3. Begin Phase 1 implementation with security hardening
4. Establish testing and monitoring infrastructure
5. Plan production deployment strategy

The implementation will result in a production-ready, secure, and scalable CRM backend system capable of handling enterprise workloads while maintaining code quality and operational excellence.