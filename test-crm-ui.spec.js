// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('CRM Application Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000');
  });

  test('Login functionality', async ({ page }) => {
    // Check if login page loads
    await expect(page).toHaveURL(/.*localhost:3000.*/);
    
    // Fill login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    
    // Take screenshot before login
    await page.screenshot({ path: 'test-screenshots/01-login-form.png' });
    
    // Click login button
    await page.click('button:has-text("Sign in")');
    
    // Wait for navigation
    await page.waitForLoadState('networkidle');
    
    // Check if redirected to dashboard
    const dashboardElement = page.locator('text=/Dashboard|Pipeline/i').first();
    await expect(dashboardElement).toBeVisible({ timeout: 10000 });
    
    // Take screenshot after login
    await page.screenshot({ path: 'test-screenshots/02-dashboard.png', fullPage: true });
  });

  test('Pipeline view', async ({ page }) => {
    // Login first
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button:has-text("Sign in")');
    await page.waitForLoadState('networkidle');
    
    // Navigate to pipeline view
    const pipelineLink = page.locator('a:has-text("Pipeline"), button:has-text("Pipeline")').first();
    if (await pipelineLink.isVisible()) {
      await pipelineLink.click();
      await page.waitForLoadState('networkidle');
    }
    
    // Check for pipeline stages
    const stages = page.locator('[class*="stage"], [data-testid*="stage"]');
    const stageCount = await stages.count();
    console.log(`Found ${stageCount} pipeline stages`);
    expect(stageCount).toBeGreaterThan(0);
    
    // Take screenshot of pipeline
    await page.screenshot({ path: 'test-screenshots/03-pipeline.png', fullPage: true });
  });

  test('Card creation', async ({ page }) => {
    // Login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button:has-text("Sign in")');
    await page.waitForLoadState('networkidle');
    
    // Look for add card button
    const addButton = page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("+")').first();
    
    if (await addButton.isVisible()) {
      await addButton.click();
      
      // Wait for modal
      await page.waitForTimeout(1000);
      
      // Fill card form
      const titleInput = page.locator('input[name="title"], input[placeholder*="Title"]').first();
      if (await titleInput.isVisible()) {
        await titleInput.fill('Test Card from Playwright');
        
        const valueInput = page.locator('input[name="value"], input[placeholder*="Value"]').first();
        if (await valueInput.isVisible()) {
          await valueInput.fill('100000');
        }
        
        // Take screenshot of form
        await page.screenshot({ path: 'test-screenshots/04-new-card-form.png' });
        
        // Submit form
        const saveButton = page.locator('button:has-text("Save"), button:has-text("Create")').first();
        if (await saveButton.isVisible()) {
          await saveButton.click();
          await page.waitForLoadState('networkidle');
        }
      }
    }
    
    // Final screenshot
    await page.screenshot({ path: 'test-screenshots/05-final-state.png', fullPage: true });
  });

  test('Real-time SSE connection', async ({ page }) => {
    // Login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button:has-text("Sign in")');
    await page.waitForLoadState('networkidle');
    
    // Check for SSE connection
    const sseConnected = await page.evaluate(() => {
      return new Promise((resolve) => {
        const token = localStorage.getItem('token');
        if (!token) {
          resolve(false);
          return;
        }
        
        const eventSource = new EventSource(`http://localhost:8080/api/v1/realtime/events`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        eventSource.onopen = () => {
          resolve(true);
          eventSource.close();
        };
        
        eventSource.onerror = () => {
          resolve(false);
          eventSource.close();
        };
        
        setTimeout(() => {
          resolve(false);
          eventSource.close();
        }, 5000);
      });
    });
    
    console.log(`SSE Connection: ${sseConnected ? 'Connected' : 'Failed'}`);
  });
});