# CRM Frontend

A modern, responsive CRM system frontend built with Next.js 15, TypeScript, and Tailwind CSS.

## Features

- **Authentication System**: Login, registration, and JWT-based authentication
- **Dashboard**: Overview with key metrics and recent activities
- **Pipeline Management**: Drag-and-drop kanban board for sales pipeline
- **Contact Management**: CRUD operations for contacts with search and filtering
- **Company Management**: Organization and account management
- **Real-time Updates**: Server-sent events (SSE) for live data updates
- **Responsive Design**: Mobile-first design with shadcn/ui components
- **State Management**: Zustand for global state management
- **API Integration**: React Query for efficient data fetching and caching

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui (Radix UI primitives)
- **State Management**: Zustand
- **Data Fetching**: TanStack React Query
- **Drag & Drop**: @dnd-kit
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install --legacy-peer-deps
```

2. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your backend API URL:
```env
NEXT_PUBLIC_API_URL=http://localhost:8080
```

3. Start the development server:
```bash
npm run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard and main app pages
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── dashboard/        # Dashboard-specific components
│   └── pipeline/         # Pipeline-specific components
├── lib/                  # Utilities and configurations
│   ├── api/             # API client and endpoints
│   ├── react-query/     # React Query configuration
│   ├── sse/             # Server-sent events client
│   └── utils.ts         # Utility functions
├── providers/           # React context providers
├── stores/             # Zustand state stores
└── types/              # TypeScript type definitions
```

## Key Components

### Authentication
- Login/Register pages with form validation
- JWT token management with automatic refresh
- Protected routes and authentication guards

### Dashboard
- Key metrics overview
- Pipeline statistics
- Recent activities feed
- Responsive layout with collapsible sidebar

### Pipeline Board
- Drag-and-drop kanban interface
- Real-time card updates via SSE
- Optimistic UI updates
- Card details with custom fields

### Contact & Company Management
- Search and filtering capabilities
- Card-based layouts
- Tag management
- Relationship tracking

## API Integration

The frontend integrates with a Go backend API providing:

- RESTful API endpoints
- JWT authentication
- Real-time updates via Server-Sent Events
- File upload support
- Comprehensive error handling

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build production bundle
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler check

### Code Quality

- TypeScript strict mode enabled
- ESLint configuration for Next.js
- Consistent code formatting
- Component-based architecture
- Custom hooks for business logic

## Deployment

1. Build the application:
```bash
npm run build
```

2. Start the production server:
```bash
npm run start
```

For deployment to platforms like Vercel, Netlify, or Docker, follow the respective platform guidelines.

## Configuration

### Environment Variables

- `NEXT_PUBLIC_API_URL` - Backend API URL
- `NEXT_PUBLIC_APP_NAME` - Application name
- `NEXT_PUBLIC_APP_VERSION` - Application version
- `NODE_ENV` - Environment (development/production)

### Customization

- **Styling**: Modify `tailwind.config.ts` and CSS variables in `globals.css`
- **Components**: Customize shadcn/ui components in `components/ui/`
- **API**: Extend API client in `lib/api/`
- **State**: Add new stores in `stores/`

## Contributing

1. Follow the existing code style and conventions
2. Use TypeScript strictly - avoid `any` types
3. Write meaningful component and function names
4. Add proper error handling
5. Test components in isolation
6. Ensure responsive design

## License

This project is part of a comprehensive CRM system built for demonstration purposes.