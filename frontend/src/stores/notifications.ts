import { create } from "zustand";

export interface Notification {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message?: string;
  duration?: number; // milliseconds, 0 for persistent
  timestamp: number;
  isRead: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
}

interface NotificationActions {
  addNotification: (notification: Omit<Notification, "id" | "timestamp" | "isRead">) => void;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  clearExpired: () => void;
}

type NotificationStore = NotificationState & NotificationActions;

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  // State
  notifications: [],
  unreadCount: 0,

  // Actions
  addNotification: (notificationData) => {
    const notification: Notification = {
      id: Math.random().toString(36).substr(2, 9),
      timestamp: Date.now(),
      isRead: false,
      duration: 5000, // Default 5 seconds
      ...notificationData,
    };

    set((state) => ({
      notifications: [notification, ...state.notifications],
      unreadCount: state.unreadCount + 1,
    }));

    // Auto-remove notification after duration (if not persistent)
    if (notification.duration && notification.duration > 0) {
      setTimeout(() => {
        get().removeNotification(notification.id);
      }, notification.duration);
    }
  },

  removeNotification: (id) => {
    set((state) => {
      const notification = state.notifications.find((n) => n.id === id);
      const wasUnread = notification && !notification.isRead;
      
      return {
        notifications: state.notifications.filter((n) => n.id !== id),
        unreadCount: wasUnread ? state.unreadCount - 1 : state.unreadCount,
      };
    });
  },

  markAsRead: (id) => {
    set((state) => {
      const notification = state.notifications.find((n) => n.id === id);
      const wasUnread = notification && !notification.isRead;
      
      return {
        notifications: state.notifications.map((n) =>
          n.id === id ? { ...n, isRead: true } : n
        ),
        unreadCount: wasUnread ? state.unreadCount - 1 : state.unreadCount,
      };
    });
  },

  markAllAsRead: () => {
    set((state) => ({
      notifications: state.notifications.map((n) => ({ ...n, isRead: true })),
      unreadCount: 0,
    }));
  },

  clearAll: () => {
    set({
      notifications: [],
      unreadCount: 0,
    });
  },

  clearExpired: () => {
    const now = Date.now();
    set((state) => {
      const validNotifications = state.notifications.filter((n) => {
        if (n.duration === 0) return true; // Persistent
        return (now - n.timestamp) < (n.duration || 5000);
      });

      const removedUnread = state.notifications.filter((n) => {
        const isExpired = n.duration !== 0 && (now - n.timestamp) >= (n.duration || 5000);
        return isExpired && !n.isRead;
      }).length;

      return {
        notifications: validNotifications,
        unreadCount: Math.max(0, state.unreadCount - removedUnread),
      };
    });
  },
}));

// Helper functions to create specific types of notifications
export const useNotifications = () => {
  const addNotification = useNotificationStore((state) => state.addNotification);

  return {
    success: (title: string, message?: string, duration?: number) => {
      addNotification({ type: "success", title, message, duration });
    },

    error: (title: string, message?: string, duration?: number) => {
      addNotification({ type: "error", title, message, duration: duration || 0 }); // Errors are persistent by default
    },

    warning: (title: string, message?: string, duration?: number) => {
      addNotification({ type: "warning", title, message, duration });
    },

    info: (title: string, message?: string, duration?: number) => {
      addNotification({ type: "info", title, message, duration });
    },
  };
};