import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { User, LoginRequest, LoginResponse } from "@/types";
import { authApi } from "@/lib/api";
import { apiClient } from "@/lib/api/client";

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  _hasHydrated: boolean;
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  register: (userData: any) => Promise<void>;
  getCurrentUser: () => Promise<void>;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  clearError: () => void;
  reset: () => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      _hasHydrated: false,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authApi.login(credentials);
          
          // Check if response has data property or is the data directly
          const responseData = response.data || response;
          
          if (responseData && responseData.token) {
            const { token, user } = responseData;
            
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });

            // Set token in API client (persist middleware handles storage)
            if (typeof window !== "undefined") {
              apiClient.setAuthToken(token);
              
              // Also set cookie for SSR middleware
              const authData = {
                state: {
                  user,
                  token,
                  isAuthenticated: true,
                  isLoading: false,
                  error: null,
                  _hasHydrated: true
                },
                version: 0
              };
              document.cookie = `auth-storage=${encodeURIComponent(JSON.stringify(authData))}; path=/; max-age=604800`; // 7 days
            }
          } else {
            set({
              isLoading: false,
              error: response.message || "Login failed",
            });
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.message || "Login failed",
          });
        }
      },

      logout: async () => {
        set({ isLoading: true });
        
        try {
          await authApi.logout();
        } catch (error) {
          // Continue with logout even if API call fails
          console.warn("Logout API call failed:", error);
        } finally {
          // Clear all auth data
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          // Clear API client token (persist middleware handles storage cleanup)
          if (typeof window !== "undefined") {
            apiClient.removeAuthToken();
            // Clear cookie
            document.cookie = "auth-storage=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC";
          }
        }
      },

      register: async (userData: any) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authApi.register(userData);
          
          if (response.success) {
            set({
              isLoading: false,
              error: null,
            });
            // After successful registration, user should login
          } else {
            set({
              isLoading: false,
              error: response.message || "Registration failed",
            });
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.message || "Registration failed",
          });
        }
      },

      getCurrentUser: async () => {
        const { token } = get();
        
        if (!token) {
          set({ isAuthenticated: false });
          return;
        }

        set({ isLoading: true });
        
        try {
          const response = await authApi.getCurrentUser();
          
          if (response.success && response.data) {
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } else {
            // Token is invalid
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
            
            if (typeof window !== "undefined") {
              apiClient.removeAuthToken();
            }
          }
        } catch (error: any) {
          // Token is invalid or API error
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          
          if (typeof window !== "undefined") {
            apiClient.removeAuthToken();
          }
        }
      },

      setUser: (user: User) => {
        set({ user });
      },

      setToken: (token: string) => {
        set({ token, isAuthenticated: true });
        if (typeof window !== "undefined") {
          // Set token in API client (persist middleware handles storage)
          apiClient.setAuthToken(token);
        }
      },

      clearError: () => {
        set({ error: null });
      },

      reset: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
        
        if (typeof window !== "undefined") {
          apiClient.removeAuthToken();
        }
      },
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => {
        // Custom storage that works with both localStorage and cookies
        return {
          getItem: (name: string) => {
            if (typeof window === "undefined") {
              return null;
            }
            // First try localStorage
            const value = localStorage.getItem(name);
            if (value) return value;
            
            // Fallback to cookies
            const cookies = document.cookie.split('; ');
            const cookie = cookies.find(c => c.startsWith(`${name}=`));
            return cookie ? decodeURIComponent(cookie.split('=')[1]) : null;
          },
          setItem: (name: string, value: string) => {
            if (typeof window === "undefined") return;
            
            // Save to localStorage
            localStorage.setItem(name, value);
            
            // Also save to cookie for middleware access
            const maxAge = 60 * 60 * 24 * 7; // 7 days
            document.cookie = `${name}=${encodeURIComponent(value)}; path=/; max-age=${maxAge}; SameSite=Lax`;
          },
          removeItem: (name: string) => {
            if (typeof window === "undefined") return;
            
            // Remove from localStorage
            localStorage.removeItem(name);
            
            // Remove cookie
            document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT`;
          },
        };
      }),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // Called when hydration is complete
        if (state) {
          state._hasHydrated = true;
          // Restore token in API client if user is authenticated
          if (state.token && state.isAuthenticated && typeof window !== "undefined") {
            apiClient.setAuthToken(state.token);
          }
        }
      },
    }
  )
);