import { create } from "zustand";
import { Pipeline, Stage, Card, MoveCardRequest } from "@/types";
import { cardsApi, pipelinesApi } from "@/lib/api";

interface PipelineState {
  pipelines: Pipeline[];
  currentPipeline: Pipeline | null;
  stages: Stage[];
  cards: Card[];
  isLoading: boolean;
  error: string | null;
  draggedCard: Card | null;
}

interface PipelineActions {
  setPipelines: (pipelines: Pipeline[]) => void;
  setCurrentPipeline: (pipeline: Pipeline) => void;
  setStages: (stages: Stage[]) => void;
  setCards: (cards: Card[]) => void;
  addCard: (card: Card) => void;
  updateCard: (card: Card) => void;
  removeCard: (cardId: string) => void;
  moveCard: (cardId: string, targetStageId: string, position: number) => Promise<void>;
  moveCardOptimistic: (cardId: string, targetStageId: string, position: number) => void;
  revertCardMove: () => void;
  setDraggedCard: (card: Card | null) => void;
  loadPipelineData: (pipelineId: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
}

type PipelineStore = PipelineState & PipelineActions;

export const usePipelineStore = create<PipelineStore>((set, get) => ({
  // State
  pipelines: [],
  currentPipeline: null,
  stages: [],
  cards: [],
  isLoading: false,
  error: null,
  draggedCard: null,

  // Actions
  setPipelines: (pipelines: Pipeline[]) => {
    set({ pipelines });
  },

  setCurrentPipeline: (pipeline: Pipeline) => {
    set({ currentPipeline: pipeline });
  },

  setStages: (stages: Stage[]) => {
    set({ stages });
  },

  setCards: (cards: Card[]) => {
    set({ cards });
  },

  addCard: (card: Card) => {
    set((state) => ({
      cards: [...state.cards, card],
    }));
  },

  updateCard: (updatedCard: Card) => {
    set((state) => ({
      cards: state.cards.map((card) =>
        card.id === updatedCard.id ? updatedCard : card
      ),
    }));
  },

  removeCard: (cardId: string) => {
    set((state) => ({
      cards: state.cards.filter((card) => card.id !== cardId),
    }));
  },

  moveCardOptimistic: (cardId: string, targetStageId: string, position: number) => {
    set((state) => {
      const cards = [...state.cards];
      const cardIndex = cards.findIndex((c) => c.id === cardId);
      
      if (cardIndex === -1) return state;

      const card = { ...cards[cardIndex] };
      card.stage_id = targetStageId;
      card.position = position;

      // Remove card from current position
      cards.splice(cardIndex, 1);

      // Find insertion point based on position
      const targetStageCards = cards
        .filter((c) => c.stage_id === targetStageId)
        .sort((a, b) => a.position - b.position);

      let insertIndex = 0;
      for (let i = 0; i < targetStageCards.length; i++) {
        if (targetStageCards[i].position < position) {
          insertIndex = cards.indexOf(targetStageCards[i]) + 1;
        } else {
          break;
        }
      }

      // Insert card at new position
      cards.splice(insertIndex, 0, card);

      return { cards };
    });
  },

  moveCard: async (cardId: string, targetStageId: string, position: number) => {
    const { moveCardOptimistic } = get();
    
    // Store original state for potential revert
    const originalCards = get().cards;
    
    // Optimistically update UI
    moveCardOptimistic(cardId, targetStageId, position);
    
    try {
      const moveData: MoveCardRequest = {
        card_id: cardId,
        stage_id: targetStageId,
        position,
      };
      
      const response = await cardsApi.moveCard(moveData);
      
      if (response.success && response.data) {
        // Update with server response
        get().updateCard(response.data);
      } else {
        // Revert on failure
        set({ cards: originalCards });
        set({ error: response.message || "Failed to move card" });
      }
    } catch (error: any) {
      // Revert on error
      set({ cards: originalCards });
      set({ error: error.response?.data?.message || "Failed to move card" });
    }
  },

  revertCardMove: () => {
    // This would be used if we stored the previous state
    // For now, we'll reload the data
    const { currentPipeline } = get();
    if (currentPipeline) {
      get().loadPipelineData(currentPipeline.id);
    }
  },

  setDraggedCard: (card: Card | null) => {
    set({ draggedCard: card });
  },

  loadPipelineData: async (pipelineId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      // Load pipeline details
      const pipelineResponse = await pipelinesApi.getPipeline(pipelineId);
      
      // Check if response has data (backend doesn't return success field)
      if (pipelineResponse && pipelineResponse.data) {
        set({ currentPipeline: pipelineResponse.data });
        
        // Pipeline data should include stages
        if (pipelineResponse.data.stages) {
          set({ stages: pipelineResponse.data.stages });
        }
      } else if (pipelineResponse) {
        // Direct response without wrapper
        set({ currentPipeline: pipelineResponse });
        
        // Pipeline data should include stages
        if (pipelineResponse.stages) {
          set({ stages: pipelineResponse.stages });
        }
      }

      // Load cards for this pipeline
      const cardsResponse = await cardsApi.getCards({ pipeline_id: pipelineId });
      
      // Check if response has data (backend doesn't return success field)
      if (cardsResponse && cardsResponse.data) {
        set({ cards: cardsResponse.data });
      } else if (cardsResponse) {
        // Direct response without wrapper
        set({ cards: cardsResponse });
      }

      set({ isLoading: false });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.message || "Failed to load pipeline data",
      });
    }
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set({
      pipelines: [],
      currentPipeline: null,
      stages: [],
      cards: [],
      isLoading: false,
      error: null,
      draggedCard: null,
    });
  },
}));

// Selectors for computed values
export const usePipelineSelectors = () => {
  const store = usePipelineStore();
  
  return {
    ...store,
    getCardsByStage: (stageId: string): Card[] => {
      return store.cards
        .filter((card) => card.stage_id === stageId)
        .sort((a, b) => a.position - b.position);
    },
    
    getTotalCardsValue: (): number => {
      return store.cards.reduce((total, card) => total + (card.value || 0), 0);
    },
    
    getStageCardsCount: (stageId: string): number => {
      return store.cards.filter((card) => card.stage_id === stageId).length;
    },
    
    getStageValue: (stageId: string): number => {
      return store.cards
        .filter((card) => card.stage_id === stageId)
        .reduce((total, card) => total + (card.value || 0), 0);
    },
  };
};