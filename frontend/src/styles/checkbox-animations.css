/* Optimized checkbox animations for better responsiveness */

/* Will-change optimization for checkboxes */
.optimize-checkbox {
  will-change: transform, background-color, border-color;
}

/* GPU acceleration for checkbox transitions */
.checkbox-gpu {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .optimize-checkbox {
    transition-duration: 0.001ms !important;
  }
}

/* Fast checkbox transitions */
.checkbox-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover state optimization */
.checkbox-hover:hover {
  transition-duration: 0.1s;
}

/* Active state feedback */
.checkbox-active:active {
  transform: scale(0.95);
  transition-duration: 0.05s;
}

/* Batch checkbox updates - prevents layout thrashing */
.checkbox-batch-container {
  contain: layout style paint;
  will-change: contents;
}

/* Optimized table row hover states */
.permission-table-row {
  transition: background-color 0.15s ease;
}

.permission-table-row:hover {
  background-color: var(--hover-bg, rgba(0, 0, 0, 0.02));
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .permission-table-row:hover {
    background-color: var(--hover-bg-dark, rgba(255, 255, 255, 0.02));
  }
}

/* Optimized focus states */
.checkbox-focus:focus-visible {
  outline: 2px solid var(--focus-color, #3b82f6);
  outline-offset: 2px;
  transition: outline-offset 0.1s ease;
}

/* Smooth state changes for permission matrix */
.permission-matrix {
  contain: layout;
}

.permission-cell {
  contain: paint;
}