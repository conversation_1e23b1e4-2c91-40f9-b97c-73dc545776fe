/* Enhanced Drag & Drop Styles */

/* GPU acceleration for smooth animations */
.drag-item {
  will-change: transform, opacity, box-shadow;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Smooth drag transitions */
.drag-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced drag overlay styles */
.drag-overlay {
  pointer-events: none;
  z-index: 9999;
  transform-style: preserve-3d;
}

/* Glassmorphism effect for drag overlay */
.drag-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.dark .drag-glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* Drop zone animations */
.drop-zone {
  position: relative;
  overflow: hidden;
}

.drop-zone::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    transparent, 
    rgba(var(--primary), 0.1), 
    transparent, 
    rgba(var(--primary), 0.1), 
    transparent
  );
  background-size: 20px 20px;
  animation: shimmer 2s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  z-index: -1;
}

.drop-zone.active::before {
  opacity: 1;
}

@keyframes shimmer {
  0% { background-position: -20px 0; }
  100% { background-position: 20px 0; }
}

/* Magnetic effect for cards near drop zones */
.card-magnetic {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-magnetic.near-drop {
  transform: scale(1.02) translateY(-2px);
  box-shadow: 0 10px 25px rgba(var(--primary), 0.15);
}

/* Pulse animation for active drop zones */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--primary), 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(var(--primary), 0);
  }
}

.drop-zone-pulse {
  animation: pulse-glow 2s infinite;
}

/* Ripple effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(var(--primary), 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple.active::after {
  width: 300px;
  height: 300px;
}

/* Floating animation for drag handles */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-2px) rotate(1deg);
  }
  66% {
    transform: translateY(2px) rotate(-1deg);
  }
}

.drag-handle:hover {
  animation: float 2s ease-in-out infinite;
}

/* Enhanced focus states for accessibility */
.drag-item:focus-visible {
  outline: 2px solid rgba(var(--primary), 0.8);
  outline-offset: 4px;
  box-shadow: 
    0 0 0 4px rgba(var(--primary), 0.2),
    0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Smooth color transitions */
.color-transition {
  transition: 
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease,
    box-shadow 0.3s ease;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(var(--primary), 0.1);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, 
    rgba(var(--primary), 1) 0%, 
    rgba(var(--primary), 0.8) 50%, 
    rgba(var(--primary), 1) 100%
  );
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Particle effect container */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(var(--primary), 0.6);
  border-radius: 50%;
  animation: particle-float 3s linear infinite;
}

@keyframes particle-float {
  0% {
    opacity: 0;
    transform: translateY(100px) scale(0);
  }
  10% {
    opacity: 1;
    transform: translateY(80px) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateY(-80px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-100px) scale(0);
  }
}

/* Success animation */
@keyframes success-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.success-animation {
  animation: success-bounce 1s ease-in-out;
}

/* Error shake animation */
@keyframes error-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

.error-animation {
  animation: error-shake 0.5s ease-in-out;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .drag-transition,
  .card-hover,
  .color-transition {
    transition-duration: 0.01ms !important;
  }
  
  .drag-handle:hover,
  .drop-zone::before,
  .gradient-text,
  .particle,
  .success-animation,
  .error-animation {
    animation: none !important;
  }
  
  .drop-zone-pulse {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .drag-glass {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(0, 0, 0, 0.8);
  }
  
  .dark .drag-glass {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
  
  .drop-zone.active {
    border-color: rgba(var(--primary), 1) !important;
    background: rgba(var(--primary), 0.2) !important;
  }
}
