"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Link from "next/link";
import { <PERSON>, <PERSON>Off, <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useAuthStore } from "@/stores/auth";

const loginSchema = z.object({
  email: z.string().email("Пожалуйста, введите корректный email адрес"),
  password: z.string().min(6, "Пароль должен содержать минимум 6 символов"),
});

type LoginFormData = z.infer<typeof loginSchema>;

// Test accounts for quick login
const testAccounts = [
  { email: "<EMAIL>", password: "admin123", label: "Админ", role: "Полный доступ" },
  { email: "<EMAIL>", password: "manager123", label: "Менеджер", role: "Роль менеджера" },
  { email: "<EMAIL>", password: "sales123", label: "Продажи", role: "Отдел продаж" },
  { email: "<EMAIL>", password: "support123", label: "Поддержка", role: "Команда поддержки" },
  { email: "<EMAIL>", password: "test123", label: "Тест", role: "Обычный пользователь" },
];

export default function LoginPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, error, clearError } = useAuthStore();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    clearError();
    try {
      await login(data);
      // After successful login, the auth store will set isAuthenticated
      // and the login function returns when complete
      router.push("/dashboard");
    } catch (error) {
      // Error is handled by the auth store
      console.error("Login error:", error);
    }
  };

  // Quick fill test account credentials
  const fillTestAccount = (email: string, password: string) => {
    form.setValue("email", email);
    form.setValue("password", password);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Добро пожаловать</h1>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Войдите в свою учетную запись для продолжения
          </p>
        </div>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Вход</CardTitle>
            <CardDescription>
              Введите ваши данные для доступа к учетной записи
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {error && (
                  <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3">
                    <p className="text-sm text-destructive">{error}</p>
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 dark:text-gray-300">Email адрес</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Введите ваш email"
                          autoComplete="email"
                          className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 dark:text-gray-300">Пароль</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showPassword ? "text" : "password"}
                            placeholder="Введите ваш пароль"
                            autoComplete="current-password"
                            className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent text-gray-600 dark:text-gray-400"
                            onClick={() => setShowPassword(!showPassword)}
                            tabIndex={-1}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                            <span className="sr-only">
                              {showPassword ? "Скрыть пароль" : "Показать пароль"}
                            </span>
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex items-center justify-between">
                  <Link
                    href="/auth/forgot-password"
                    className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    Забыли пароль?
                  </Link>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Вход...
                    </>
                  ) : (
                    "Войти"
                  )}
                </Button>
              </form>
            </Form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Нет учетной записи?{" "}
                <Link href="/auth/register" className="font-medium text-blue-600 dark:text-blue-400 hover:underline">
                  Зарегистрироваться
                </Link>
              </p>
            </div>

            {/* Test Account Quick Login Buttons */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="text-center mb-3">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center justify-center gap-2">
                  <Users className="h-4 w-4" />
                  Быстрый вход (Тестовые аккаунты)
                </h3>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {testAccounts.map((account) => (
                  <Button
                    key={account.email}
                    type="button"
                    variant="outline"
                    size="sm"
                    className="text-xs hover:bg-blue-50 dark:hover:bg-blue-900/20 border-gray-300 dark:border-gray-600"
                    onClick={() => fillTestAccount(account.email, account.password)}
                    disabled={isLoading}
                  >
                    <UserCheck className="h-3 w-3 mr-1" />
                    {account.label}
                  </Button>
                ))}
              </div>
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-center">
                Нажмите любую кнопку для автозаполнения
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}