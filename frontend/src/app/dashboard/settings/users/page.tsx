"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Users,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  MoreVertical,
  UserPlus,
  Key,
  Shield,
  Mail,
  Phone,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  Upload,
  Loader2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { OptimizedCheckbox } from "@/components/ui/optimized-checkbox";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useUsers } from "@/hooks/useUsers";
import { CreateUserData, UpdateUserData } from "@/lib/api/users";
import { format } from "date-fns";
import { ru } from "date-fns/locale";

const departments = [
  "Продажи",
  "Маркетинг", 
  "Разработка",
  "Поддержка",
  "Бухгалтерия",
  "HR",
  "Администрация",
];

export default function UsersSettingsPage() {
  const router = useRouter();
  const {
    users,
    meta,
    roles,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    createUser,
    updateUser,
    deleteUser,
    toggleStatus,
    assignRole,
    updatePassword,
    bulkOperations,
    searchUsers,
    filterByRole,
    filterByStatus,
    filterByDepartment,
    goToPage,
    currentFilters,
  } = useUsers();

  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [isEditUserOpen, setIsEditUserOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [passwordUserId, setPasswordUserId] = useState<string>("");
  const [newPassword, setNewPassword] = useState("");
  
  const [newUserData, setNewUserData] = useState<CreateUserData>({
    email: "",
    first_name: "",
    last_name: "",
    password: "",
    phone: "",
    department: "",
    position: "",
    role_id: undefined,
  });

  const [editUserData, setEditUserData] = useState<UpdateUserData>({
    email: "",
    first_name: "",
    last_name: "",
    phone: "",
    department: "",
    position: "",
    role_id: undefined,
  });

  const handleAddUser = () => {
    createUser(newUserData, {
      onSuccess: () => {
        setIsAddUserOpen(false);
        setNewUserData({
          email: "",
          first_name: "",
          last_name: "",
          password: "",
          phone: "",
          department: "",
          position: "",
          role_id: undefined,
        });
      },
    });
  };

  const handleEditUser = (user: any) => {
    setEditingUser(user);
    setEditUserData({
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      phone: user.phone || "",
      department: user.department || "",
      position: user.position || "",
      role_id: user.role?.id || undefined,
    });
    setIsEditUserOpen(true);
  };

  const handleUpdateUser = () => {
    if (editingUser) {
      updateUser({ id: editingUser.id, data: editUserData }, {
        onSuccess: () => {
          setIsEditUserOpen(false);
          setEditingUser(null);
        },
      });
    }
  };

  const handleDeleteUser = (userId: string) => {
    if (confirm("Вы уверены, что хотите удалить этого пользователя?")) {
      deleteUser(userId);
    }
  };

  const handleToggleStatus = (userId: string) => {
    toggleStatus(userId);
  };

  const handleResetPassword = (userId: string) => {
    setPasswordUserId(userId);
    setNewPassword("");
    setIsPasswordDialogOpen(true);
  };

  const handleUpdatePassword = () => {
    if (passwordUserId && newPassword) {
      updatePassword({ id: passwordUserId, password: newPassword }, {
        onSuccess: () => {
          setIsPasswordDialogOpen(false);
          setPasswordUserId("");
          setNewPassword("");
        },
      });
    }
  };

  const handleBulkDelete = () => {
    if (selectedUsers.length > 0 && confirm(`Удалить ${selectedUsers.length} пользователей?`)) {
      bulkOperations({ user_ids: selectedUsers, action: 'delete' }, {
        onSuccess: () => {
          setSelectedUsers([]);
        },
      });
    }
  };

  const handleBulkActivate = () => {
    if (selectedUsers.length > 0) {
      bulkOperations({ user_ids: selectedUsers, action: 'activate' }, {
        onSuccess: () => {
          setSelectedUsers([]);
        },
      });
    }
  };

  const handleBulkDeactivate = () => {
    if (selectedUsers.length > 0) {
      bulkOperations({ user_ids: selectedUsers, action: 'deactivate' }, {
        onSuccess: () => {
          setSelectedUsers([]);
        },
      });
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="success">Активен</Badge>
    ) : (
      <Badge variant="secondary">Неактивен</Badge>
    );
  };

  const getRoleBadge = (role: any) => {
    if (!role) return <Badge variant="outline">Нет роли</Badge>;
    
    const colorMap: Record<string, string> = {
      "Administrator": "destructive",
      "Администратор": "destructive",
      "Manager": "blue",
      "Менеджер": "blue",
      "Sales": "green",
      "Продажи": "green",
      "Support": "purple",
      "Поддержка": "purple",
    };
    
    return (
      <Badge 
        variant={colorMap[role.name] as any || "default"}
        style={{ backgroundColor: role.color }}
      >
        {role.name}
      </Badge>
    );
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  const formatDate = (date: string | null) => {
    if (!date) return "-";
    try {
      return format(new Date(date), "dd.MM.yyyy HH:mm", { locale: ru });
    } catch {
      return date;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/dashboard/settings")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Users className="h-8 w-8" />
              Управление пользователями
            </h1>
            <p className="text-gray-500 mt-1">Добавление и управление учетными записями</p>
          </div>
        </div>
        <div className="flex gap-2">
          {selectedUsers.length > 0 && (
            <>
              <Button variant="outline" onClick={handleBulkActivate}>
                Активировать ({selectedUsers.length})
              </Button>
              <Button variant="outline" onClick={handleBulkDeactivate}>
                Деактивировать ({selectedUsers.length})
              </Button>
              <Button variant="destructive" onClick={handleBulkDelete}>
                Удалить ({selectedUsers.length})
              </Button>
            </>
          )}
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Импорт
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Экспорт
          </Button>
          <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                Добавить пользователя
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Добавить нового пользователя</DialogTitle>
                <DialogDescription>
                  Введите данные нового пользователя системы
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">Имя</Label>
                    <Input
                      id="firstName"
                      value={newUserData.first_name}
                      onChange={(e) => setNewUserData({ ...newUserData, first_name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Фамилия</Label>
                    <Input
                      id="lastName"
                      value={newUserData.last_name}
                      onChange={(e) => setNewUserData({ ...newUserData, last_name: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUserData.email}
                      onChange={(e) => setNewUserData({ ...newUserData, email: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Телефон</Label>
                    <Input
                      id="phone"
                      value={newUserData.phone}
                      onChange={(e) => setNewUserData({ ...newUserData, phone: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="role">Роль</Label>
                    <Select
                      value={newUserData.role_id}
                      onValueChange={(value) => setNewUserData({ ...newUserData, role_id: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Выберите роль" />
                      </SelectTrigger>
                      <SelectContent>
                        {roles.map(role => (
                          <SelectItem key={role.id} value={role.id}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="department">Отдел</Label>
                    <Select
                      value={newUserData.department}
                      onValueChange={(value) => setNewUserData({ ...newUserData, department: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Выберите отдел" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map(dept => (
                          <SelectItem key={dept} value={dept}>
                            {dept}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="position">Должность</Label>
                    <Input
                      id="position"
                      value={newUserData.position}
                      onChange={(e) => setNewUserData({ ...newUserData, position: e.target.value })}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="password">Временный пароль</Label>
                  <Input
                    id="password"
                    type="password"
                    value={newUserData.password}
                    onChange={(e) => setNewUserData({ ...newUserData, password: e.target.value })}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Пользователь должен будет сменить пароль при первом входе
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddUserOpen(false)}>
                  Отмена
                </Button>
                <Button onClick={handleAddUser} disabled={isCreating}>
                  {isCreating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Создание...
                    </>
                  ) : (
                    "Добавить пользователя"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Редактировать пользователя</DialogTitle>
            <DialogDescription>
              Измените данные пользователя
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-firstName">Имя</Label>
                <Input
                  id="edit-firstName"
                  value={editUserData.first_name}
                  onChange={(e) => setEditUserData({ ...editUserData, first_name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-lastName">Фамилия</Label>
                <Input
                  id="edit-lastName"
                  value={editUserData.last_name}
                  onChange={(e) => setEditUserData({ ...editUserData, last_name: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-email">Email</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={editUserData.email}
                  onChange={(e) => setEditUserData({ ...editUserData, email: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-phone">Телефон</Label>
                <Input
                  id="edit-phone"
                  value={editUserData.phone}
                  onChange={(e) => setEditUserData({ ...editUserData, phone: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="edit-role">Роль</Label>
                <Select
                  value={editUserData.role_id || ""}
                  onValueChange={(value) => setEditUserData({ ...editUserData, role_id: value || null })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Выберите роль" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map(role => (
                      <SelectItem key={role.id} value={role.id}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-department">Отдел</Label>
                <Select
                  value={editUserData.department}
                  onValueChange={(value) => setEditUserData({ ...editUserData, department: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Выберите отдел" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map(dept => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-position">Должность</Label>
                <Input
                  id="edit-position"
                  value={editUserData.position}
                  onChange={(e) => setEditUserData({ ...editUserData, position: e.target.value })}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditUserOpen(false)}>
              Отмена
            </Button>
            <Button onClick={handleUpdateUser} disabled={isUpdating}>
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Сохранение...
                </>
              ) : (
                "Сохранить изменения"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Password Reset Dialog */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Сброс пароля</DialogTitle>
            <DialogDescription>
              Введите новый пароль для пользователя
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div>
              <Label htmlFor="new-password">Новый пароль</Label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Минимум 8 символов"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPasswordDialogOpen(false)}>
              Отмена
            </Button>
            <Button onClick={handleUpdatePassword}>
              Обновить пароль
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Пользователи системы</CardTitle>
              <CardDescription>Всего: {meta.total} пользователей</CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Поиск пользователей..."
                  onChange={(e) => searchUsers(e.target.value)}
                  className="w-64"
                />
              </div>
              <Select onValueChange={(value) => filterByRole(value === "all" ? undefined : value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Все роли" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все роли</SelectItem>
                  {roles.map(role => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select onValueChange={(value) => {
                if (value === "all") filterByStatus(undefined);
                else if (value === "active") filterByStatus(true);
                else if (value === "inactive") filterByStatus(false);
              }}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Все статусы" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все статусы</SelectItem>
                  <SelectItem value="active">Активные</SelectItem>
                  <SelectItem value="inactive">Неактивные</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <OptimizedCheckbox 
                    checked={selectedUsers.length === users.length && users.length > 0}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedUsers(users.map(u => u.id));
                      } else {
                        setSelectedUsers([]);
                      }
                    }}
                  />
                </TableHead>
                <TableHead>Пользователь</TableHead>
                <TableHead>Роль</TableHead>
                <TableHead>Отдел</TableHead>
                <TableHead>Статус</TableHead>
                <TableHead>Безопасность</TableHead>
                <TableHead>Последний вход</TableHead>
                <TableHead className="text-right">Действия</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user: any) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <OptimizedCheckbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedUsers([...selectedUsers, user.id]);
                        } else {
                          setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={user.avatar} />
                        <AvatarFallback>
                          {getInitials(user.first_name, user.last_name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">
                          {user.first_name} {user.last_name}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center gap-2">
                          <Mail className="h-3 w-3" />
                          {user.email}
                        </div>
                        {user.phone && (
                          <div className="text-sm text-gray-500 flex items-center gap-2">
                            <Phone className="h-3 w-3" />
                            {user.phone}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getRoleBadge(user.role)}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.department || "-"}</div>
                      <div className="text-sm text-gray-500">{user.position || "-"}</div>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(user.is_active)}</TableCell>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-1">
                        {user.email_verified ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="text-sm">Email</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {user.two_factor_enabled ? (
                          <Shield className="h-4 w-4 text-green-500" />
                        ) : (
                          <Shield className="h-4 w-4 text-gray-300" />
                        )}
                        <span className="text-sm">2FA</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <Clock className="h-3 w-3" />
                      {formatDate(user.last_login_at)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Действия</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleEditUser(user)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Редактировать
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleResetPassword(user.id)}>
                          <Key className="mr-2 h-4 w-4" />
                          Сбросить пароль
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleStatus(user.id)}>
                          <Shield className="mr-2 h-4 w-4" />
                          {user.is_active ? "Деактивировать" : "Активировать"}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Удалить
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {/* Pagination */}
          {meta.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Показано {users.length} из {meta.total}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(meta.page - 1)}
                  disabled={meta.page <= 1}
                >
                  Назад
                </Button>
                {[...Array(meta.totalPages)].map((_, i) => (
                  <Button
                    key={i}
                    variant={meta.page === i + 1 ? "default" : "outline"}
                    size="sm"
                    onClick={() => goToPage(i + 1)}
                  >
                    {i + 1}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(meta.page + 1)}
                  disabled={meta.page >= meta.totalPages}
                >
                  Вперед
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}