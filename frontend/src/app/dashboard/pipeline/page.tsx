"use client";

import { useEffect, useState, useMemo } from "react";
import { PipelineBoard } from "@/components/pipeline/pipeline-board";
import { usePipelines } from "@/lib/react-query/hooks";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { RefreshCw } from "lucide-react";

export default function PipelinePage() {
  const { data: pipelines, isLoading, error, refetch } = usePipelines();
  const [selectedPipelineId, setSelectedPipelineId] = useState<string>("");

  // Select first pipeline by default
  useEffect(() => {
    if (pipelines && pipelines.length > 0 && !selectedPipelineId) {
      setSelectedPipelineId(pipelines[0].id);
    }
  }, [pipelines, selectedPipelineId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">Воронка</h1>
        <div className="bg-destructive/15 border border-destructive/20 rounded-md p-4">
          <p className="text-destructive mb-4">Не удалось загрузить воронки</p>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Повторить
          </Button>
        </div>
      </div>
    );
  }

  if (!pipelines || pipelines.length === 0) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">Воронка</h1>
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">Воронки не найдены</p>
          <p className="text-gray-400 mt-2">
            Создайте воронку для управления процессом продаж
          </p>
          <Button className="mt-4">Создать воронку</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Pipeline Selector */}
      {pipelines.length > 1 && (
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium">Воронка:</label>
          <Select value={selectedPipelineId} onValueChange={setSelectedPipelineId}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Выберите воронку" />
            </SelectTrigger>
            <SelectContent>
              {pipelines.map((pipeline) => (
                <SelectItem key={pipeline.id} value={pipeline.id}>
                  {pipeline.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Pipeline Board */}
      {selectedPipelineId && (
        <PipelineBoard pipelineId={selectedPipelineId} />
      )}
    </div>
  );
}