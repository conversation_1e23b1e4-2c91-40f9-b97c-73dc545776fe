"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { MobileSidebar } from "@/components/dashboard/mobile-sidebar";
import { useAuth } from "@/providers/auth-provider";
import { ThemeToggle } from "@/components/theme-toggle";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { isAuthenticated, isLoading, hasHydrated } = useAuth();

  useEffect(() => {
    // Only redirect after hydration is complete
    if (hasHydrated && !isLoading && !isAuthenticated) {
      router.push("/auth/login");
    }
  }, [hasHydrated, isAuthenticated, isLoading, router]);

  // Show loading state while hydrating or checking authentication
  if (!hasHydrated || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Don't render dashboard if not authenticated (after hydration)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex h-screen bg-background">
      <MobileSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Desktop Header */}
        <header className="hidden md:flex border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-6 py-4">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center space-x-4">
              {/* You can add breadcrumbs or page title here */}
            </div>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
            </div>
          </div>
        </header>
        {/* Main content with responsive padding */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-background p-4 md:p-6 pt-16 md:pt-6 pb-20 md:pb-6">
          {children}
        </main>
      </div>
    </div>
  );
}