"use client";

import { useState, useEffect } from "react";
import { useDashboardStats } from "@/lib/react-query/hooks";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { OnboardingWizard } from "@/components/onboarding/onboarding-wizard";
import {
  DollarSign,
  TrendingUp,
  Users,
  Building2,
  Activity,
  Target,
  Clock,
  CheckCircle,
  ArrowUpRight,
  BarChart3,
  AlertTriangle,
  Plus,
  UserPlus,
  Calendar
} from "lucide-react";

export default function DashboardPage() {
  const { data: stats, isLoading, error } = useDashboardStats();
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    // Check if onboarding has been completed
    const onboardingCompleted = localStorage.getItem("onboardingCompleted");
    if (!onboardingCompleted) {
      setShowOnboarding(true);
    }
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-5 w-96" />
          </div>
        </div>
        
        {/* Bento Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-6 auto-rows-[140px]">
          {/* Main metric - large card */}
          <div className="col-span-2 row-span-2">
            <Skeleton className="w-full h-full rounded-2xl" />
          </div>
          
          {/* Other metrics */}
          {[...Array(6)].map((_, i) => (
            <div key={i} className="col-span-1">
              <Skeleton className="w-full h-full rounded-xl" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-8"
      >
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">Главная</h1>
          <p className="text-muted-foreground mt-2">Добро пожаловать! Вот что происходит с вашими продажами.</p>
        </div>
        <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6">
          <p className="text-destructive font-medium">Не удалось загрузить данные</p>
          <p className="text-destructive/70 text-sm mt-1">Пожалуйста, обновите страницу</p>
        </div>
      </motion.div>
    );
  }

  const mainMetric = {
    title: "Общая выручка",
    value: formatCurrency(stats?.total_revenue || 0),
    icon: DollarSign,
    description: "Закрытые сделки",
    change: "+12.5%",
    trend: "up" as const
  };

  const metrics = [
    {
      title: "Активные сделки",
      value: stats?.active_cards?.toString() || "0",
      icon: Target,
      description: "В работе",
      change: "+5.2%",
      trend: "up" as const
    },
    {
      title: "Выигранные сделки",
      value: stats?.closed_won_cards?.toString() || "0",
      icon: CheckCircle,
      description: formatCurrency(stats?.total_revenue || 0),
      change: "+8.1%",
      trend: "up" as const
    },
    {
      title: "Конверсия",
      value: `${stats?.conversion_rate?.toFixed(1) || "0"}%`,
      icon: TrendingUp,
      description: "Процент успеха",
      change: "+2.3%",
      trend: "up" as const
    },
    {
      title: "Средняя сделка",
      value: formatCurrency(stats?.avg_deal_size || 0),
      icon: BarChart3,
      description: "Размер сделки",
      change: "-1.2%",
      trend: "down" as const
    },
    {
      title: "Ожидаемая выручка",
      value: formatCurrency(stats?.expected_revenue || 0),
      icon: Clock,
      description: "Прогноз по активным",
      change: "+7.8%",
      trend: "up" as const
    },
    {
      title: "Просроченные",
      value: stats?.overdue_cards?.toString() || "0",
      icon: AlertTriangle,
      description: "Требуют внимания",
      change: "-2.1%",
      trend: stats?.overdue_cards && stats.overdue_cards > 0 ? "down" as const : "up" as const
    }
  ];

  return (
    <>
      {showOnboarding && (
        <OnboardingWizard onComplete={() => setShowOnboarding(false)} />
      )}
      
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="space-y-8"
      >
      {/* Header */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">
            Welcome back! Here's what's happening with your sales.
          </p>
        </div>
      </motion.div>

      {/* Bento Grid Layout */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-6 auto-rows-[140px]">
        {/* Main Pipeline Value Card - Large */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="col-span-2 row-span-2"
        >
          <Card className="h-full bg-gradient-to-br from-blue-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] group overflow-hidden relative">
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <CardHeader className="pb-2 relative z-10">
              <div className="flex items-center justify-between gap-2">
                <CardTitle className="text-lg font-semibold text-white/90 truncate min-w-0">
                  {mainMetric.title}
                </CardTitle>
                <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm flex-shrink-0">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 space-y-3">
              <div className="text-4xl font-bold text-white truncate">{mainMetric.value}</div>
              <div className="flex items-center gap-2 flex-wrap">
                <div className="flex items-center gap-1 bg-white/20 px-2 py-1 rounded-full backdrop-blur-sm flex-shrink-0">
                  <ArrowUpRight className="h-3 w-3" />
                  <span className="text-xs font-medium">{mainMetric.change}</span>
                </div>
                <p className="text-sm text-white/80 truncate min-w-0">{mainMetric.description}</p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Other Metrics Cards */}
        {metrics.map((metric, index) => {
          const Icon = metric.icon;
          const isNegative = metric.trend === 'down';
          
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              className="col-span-1"
            >
              <Card className="h-full bg-card/50 backdrop-blur-sm border border-border/50 hover:bg-card/80 transition-all duration-300 hover:scale-105 hover:shadow-md group">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between gap-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors truncate min-w-0">
                      {metric.title}
                    </CardTitle>
                    <div className="p-2 bg-muted/50 group-hover:bg-primary/10 rounded-lg transition-colors flex-shrink-0">
                      <Icon className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="text-2xl font-bold group-hover:text-primary transition-colors truncate">{metric.value}</div>
                  <div className="flex items-center gap-2">
                    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
                      isNegative
                        ? 'bg-red-100 text-red-600 dark:bg-red-950 dark:text-red-400'
                        : 'bg-green-100 text-green-600 dark:bg-green-950 dark:text-green-400'
                    }`}>
                      <ArrowUpRight className={`h-3 w-3 ${
                        isNegative ? 'rotate-180' : ''
                      }`} />
                      <span>{metric.change}</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground truncate">
                    {metric.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Pipeline Stages Overview */}
      {stats?.cards_by_stage && stats.cards_by_stage.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card className="bg-card/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-white" />
                </div>
                Обзор воронки
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.cards_by_stage.map((stage, index) => (
                  <motion.div
                    key={stage.stage_id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.9 + index * 0.1 }}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors group"
                  >
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                      <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 group-hover:scale-110 transition-transform flex-shrink-0"></div>
                      <span className="font-medium group-hover:text-primary transition-colors truncate">{stage.stage_name}</span>
                    </div>
                    <div className="text-right flex-shrink-0">
                      <div className="font-semibold text-lg">{stage.card_count}</div>
                      <div className="text-sm text-muted-foreground truncate">
                        {formatCurrency(stage.total_value)}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Recent Activities Summary */}
      {stats?.recent_activities && stats.recent_activities.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <Card className="bg-card/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg">
                  <Activity className="h-5 w-5 text-white" />
                </div>
                Активность за 30 дней
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recent_activities.slice(0, 5).map((activity, index) => (
                  <motion.div
                    key={activity.type}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.1 + index * 0.1 }}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors group"
                  >
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                      <div className="w-3 h-3 rounded-full bg-gradient-to-r from-green-500 to-blue-600 group-hover:scale-110 transition-transform flex-shrink-0"></div>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-sm group-hover:text-primary transition-colors capitalize truncate">
                          {activity.type.replace('_', ' ')}
                        </p>
                        <p className="text-xs text-muted-foreground truncate">
                          Последнее: {new Date(activity.last_updated).toLocaleDateString('ru-RU')}
                        </p>
                      </div>
                    </div>
                    <div className="text-right flex-shrink-0">
                      <div className="font-semibold text-lg">{activity.count}</div>
                      <div className="text-xs text-muted-foreground">действий</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Top Performers */}
      {stats?.top_performers && stats.top_performers.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
        >
          <Card className="bg-card/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-lg">
                  <Users className="h-5 w-5 text-white" />
                </div>
                Топ исполнители (3 месяца)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.top_performers.slice(0, 5).map((performer, index) => (
                  <motion.div
                    key={performer.user_id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.3 + index * 0.1 }}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors group"
                  >
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white flex-shrink-0 ${
                        index === 0 ? 'bg-yellow-500' :
                        index === 1 ? 'bg-gray-400' :
                        index === 2 ? 'bg-amber-600' :
                        'bg-blue-500'
                      }`}>
                        {index + 1}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-sm group-hover:text-primary transition-colors truncate">
                          {performer.user_name}
                        </p>
                        <p className="text-xs text-muted-foreground truncate">
                          {performer.cards_count} сделок • {performer.win_rate.toFixed(1)}% конверсия
                        </p>
                      </div>
                    </div>
                    <div className="text-right flex-shrink-0">
                      <div className="font-semibold text-lg truncate">{formatCurrency(performer.total_revenue)}</div>
                      <div className="text-xs text-muted-foreground">выручка</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.4 }}
      >
        <Card className="bg-card/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
                <Plus className="h-5 w-5 text-white" />
              </div>
              Быстрые действия
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center gap-3 p-4 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors group"
                onClick={() => window.location.href = '/cards/new'}
              >
                <div className="p-2 bg-blue-100 dark:bg-blue-950 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-900 transition-colors">
                  <Target className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-left min-w-0 flex-1">
                  <p className="font-medium text-sm truncate">Новая сделка</p>
                  <p className="text-xs text-muted-foreground truncate">Создать возможность</p>
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center gap-3 p-4 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors group"
                onClick={() => window.location.href = '/contacts/new'}
              >
                <div className="p-2 bg-green-100 dark:bg-green-950 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-900 transition-colors">
                  <UserPlus className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <div className="text-left min-w-0 flex-1">
                  <p className="font-medium text-sm truncate">Новый контакт</p>
                  <p className="text-xs text-muted-foreground truncate">Добавить в базу</p>
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center gap-3 p-4 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors group"
                onClick={() => window.location.href = '/activities/new'}
              >
                <div className="p-2 bg-purple-100 dark:bg-purple-950 rounded-lg group-hover:bg-purple-200 dark:group-hover:bg-purple-900 transition-colors">
                  <Calendar className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="text-left min-w-0 flex-1">
                  <p className="font-medium text-sm truncate">Запланировать</p>
                  <p className="text-xs text-muted-foreground truncate">Встреча или звонок</p>
                </div>
              </motion.button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
    </>
  );
}