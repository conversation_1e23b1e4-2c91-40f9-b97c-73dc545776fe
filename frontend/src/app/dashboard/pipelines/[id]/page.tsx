"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON><PERSON>, Edit2, Trash2, <PERSON><PERSON><PERSON>, Users, Package, Clock, Bell } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuthStore } from "@/stores/auth";
import { apiClient } from "@/lib/api";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/hooks/use-toast";

interface Pipeline {
  id: string;
  name: string;
  description: string;
  type: "sales" | "hr" | "projects" | "support" | "custom";
  color: string;
  icon?: string;
  is_default: boolean;
  is_active: boolean;
  stages?: Stage[];
  settings?: {
    allowed_users?: string[];
    allowed_roles?: string[];
    automation_enabled?: boolean;
    sla_enabled?: boolean;
  };
  created_at?: string;
  updated_at?: string;
}

interface Stage {
  id: string;
  name: string;
  description?: string;
  color: string;
  sort_order: number;
  is_final?: boolean;
  is_closed_won?: boolean;
  is_closed_lost?: boolean;
  cards?: any[];
}

const pipelineTypeLabels = {
  sales: "Sales",
  hr: "HR & Recruiting",
  projects: "Projects",
  support: "Support",
  custom: "Custom",
};

export default function ViewPipelinePage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuthStore();
  const [pipeline, setPipeline] = useState<Pipeline | null>(null);
  const [loading, setLoading] = useState(true);

  const canManagePipeline = user?.role === "admin" || user?.role === "manager";

  useEffect(() => {
    fetchPipeline();
  }, [params.id]);

  const fetchPipeline = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/api/v1/pipelines/${params.id}?include_stages=true`);
      const pipelineData = response.data?.data || response.data;
      setPipeline(pipelineData);
    } catch (error) {
      console.error("Failed to fetch pipeline:", error);
      toast({
        title: "Error",
        description: "Failed to load pipeline details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this pipeline? This action cannot be undone.")) {
      return;
    }

    try {
      await apiClient.delete(`/api/v1/pipelines/${params.id}`);
      toast({
        title: "Success",
        description: "Pipeline deleted successfully",
      });
      router.push("/dashboard/pipelines");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete pipeline",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-48" />
        </div>
        <Skeleton className="h-96" />
      </div>
    );
  }

  if (!pipeline) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Package className="h-12 w-12 text-gray-400 mb-4" />
        <h2 className="text-xl font-semibold mb-2">Pipeline not found</h2>
        <p className="text-gray-500 mb-4">The pipeline you're looking for doesn't exist or has been deleted.</p>
        <Button onClick={() => router.push("/dashboard/pipelines")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Pipelines
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/dashboard/pipelines")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              {pipeline.name}
              {pipeline.is_default && (
                <Badge variant="outline">Default</Badge>
              )}
            </h1>
            <p className="text-gray-500 mt-1">{pipeline.description || "No description"}</p>
          </div>
        </div>
        {canManagePipeline && (
          <div className="flex gap-2">
            <Button
              onClick={() => router.push(`/dashboard/pipelines/${params.id}/edit`)}
            >
              <Edit2 className="mr-2 h-4 w-4" />
              Edit Pipeline
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Badge>{pipelineTypeLabels[pipeline.type]}</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={pipeline.is_active ? "default" : "secondary"}>
              {pipeline.is_active ? "Active" : "Inactive"}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Stages</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{pipeline.stages?.length || 0}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              {pipeline.settings?.automation_enabled && (
                <Badge variant="secondary">Automation</Badge>
              )}
              {pipeline.settings?.sla_enabled && (
                <Badge variant="secondary">SLA</Badge>
              )}
              {!pipeline.settings?.automation_enabled && !pipeline.settings?.sla_enabled && (
                <span className="text-sm text-gray-500">None</span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="stages">
        <TabsList>
          <TabsTrigger value="stages">Stages</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
        </TabsList>

        <TabsContent value="stages" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Pipeline Stages</CardTitle>
              <CardDescription>
                The stages that cards move through in this pipeline
              </CardDescription>
            </CardHeader>
            <CardContent>
              {pipeline.stages && pipeline.stages.length > 0 ? (
                <div className="space-y-4">
                  {pipeline.stages
                    .sort((a, b) => a.sort_order - b.sort_order)
                    .map((stage, index) => (
                      <div
                        key={stage.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center gap-4">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: stage.color }}
                          />
                          <div>
                            <p className="font-medium">
                              {index + 1}. {stage.name}
                            </p>
                            {stage.description && (
                              <p className="text-sm text-gray-500">{stage.description}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          {stage.is_closed_won && (
                            <Badge variant="success">Won</Badge>
                          )}
                          {stage.is_closed_lost && (
                            <Badge variant="destructive">Lost</Badge>
                          )}
                          {stage.is_final && (
                            <Badge variant="outline">Final</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-gray-500">No stages configured for this pipeline.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Pipeline Settings</CardTitle>
              <CardDescription>
                Configuration and automation settings for this pipeline
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-gray-500" />
                  <span>Automation</span>
                </div>
                <Badge variant={pipeline.settings?.automation_enabled ? "default" : "secondary"}>
                  {pipeline.settings?.automation_enabled ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>SLA Tracking</span>
                </div>
                <Badge variant={pipeline.settings?.sla_enabled ? "default" : "secondary"}>
                  {pipeline.settings?.sla_enabled ? "Enabled" : "Disabled"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Access Permissions</CardTitle>
              <CardDescription>
                Control who can view and manage this pipeline
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Allowed Roles
                </h4>
                <div className="flex flex-wrap gap-2">
                  {pipeline.settings?.allowed_roles?.length ? (
                    pipeline.settings.allowed_roles.map((role) => (
                      <Badge key={role} variant="outline">
                        {role}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-gray-500">All roles have access</span>
                  )}
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  Notifications
                </h4>
                <p className="text-sm text-gray-500">
                  Email notifications for stage changes are currently disabled
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}