"use client";

import { useState, useEffect } from "react";
import { useR<PERSON>er, use<PERSON>ara<PERSON> } from "next/navigation";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { 
  ArrowLeft, Plus, Save, Settings2, Trash2, GripVertical,
  ChevronDown, ChevronUp, AlertCircle, Check, X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { apiClient } from "@/lib/api";
import { toast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";

interface Stage {
  id: string;
  name: string;
  description?: string;
  color: string;
  sort_order: number;
  is_closed_won?: boolean;
  is_closed_lost?: boolean;
  is_final?: boolean;
  probability?: number;
  auto_move_after_days?: number;
  settings?: {
    required_fields?: string[];
    time_limit?: number;
  };
}

interface Pipeline {
  id: string;
  name: string;
  description?: string;
  type: string;
  color: string;
  is_active: boolean;
  is_default: boolean;
  settings?: {
    automation_enabled?: boolean;
    sla_enabled?: boolean;
    allowed_roles?: string[];
    required_fields?: string[];
  };
  stages?: Stage[];
}

const colorOptions = [
  "#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", 
  "#EC4899", "#14B8A6", "#F97316", "#6366F1", "#84CC16"
];

function SortableStage({ stage, onUpdate, onDelete }: {
  stage: Stage;
  onUpdate: (id: string, updates: Partial<Stage>) => void;
  onDelete: (id: string) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: stage.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`border rounded-lg bg-white dark:bg-gray-800 ${isDragging ? 'shadow-lg' : ''}`}
    >
      <div className="p-4">
        <div className="flex items-start gap-4">
          <button
            className="mt-1 cursor-grab hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"
            {...attributes}
            {...listeners}
          >
            <GripVertical className="h-5 w-5 text-gray-400" />
          </button>

          <div className="flex-1 space-y-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-3">
                <Input
                  value={stage.name}
                  onChange={(e) => onUpdate(stage.id, { name: e.target.value })}
                  placeholder="Stage name"
                  className="font-medium"
                />
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    {colorOptions.slice(0, 5).map((color) => (
                      <button
                        key={color}
                        onClick={() => onUpdate(stage.id, { color })}
                        className={`
                          w-6 h-6 rounded-full border-2
                          ${stage.color === color ? "border-gray-900 dark:border-white" : "border-gray-300"}
                        `}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                  <div className="flex gap-2 ml-2">
                    {stage.is_closed_won && (
                      <Badge className="bg-green-100 text-green-800">Won</Badge>
                    )}
                    {stage.is_closed_lost && (
                      <Badge className="bg-red-100 text-red-800">Lost</Badge>
                    )}
                    {stage.is_final && (
                      <Badge variant="secondary">Final</Badge>
                    )}
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete(stage.id)}
                className="ml-2"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="settings" className="border-0">
                <AccordionTrigger className="py-2 text-sm">
                  Advanced Settings
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 pt-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`probability-${stage.id}`}>
                          Win Probability (%)
                        </Label>
                        <Input
                          id={`probability-${stage.id}`}
                          type="number"
                          min="0"
                          max="100"
                          value={stage.probability || 0}
                          onChange={(e) => onUpdate(stage.id, { probability: parseFloat(e.target.value) })}
                        />
                      </div>
                      <div>
                        <Label htmlFor={`auto-move-${stage.id}`}>
                          Auto-move after (days)
                        </Label>
                        <Input
                          id={`auto-move-${stage.id}`}
                          type="number"
                          min="0"
                          value={stage.auto_move_after_days || ""}
                          onChange={(e) => onUpdate(stage.id, { 
                            auto_move_after_days: e.target.value ? parseInt(e.target.value) : undefined 
                          })}
                          placeholder="No auto-move"
                        />
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Switch
                          id={`closed-won-${stage.id}`}
                          checked={stage.is_closed_won || false}
                          onCheckedChange={(checked) => onUpdate(stage.id, { 
                            is_closed_won: checked,
                            is_closed_lost: checked ? false : stage.is_closed_lost,
                            is_final: checked ? true : stage.is_final
                          })}
                        />
                        <Label htmlFor={`closed-won-${stage.id}`}>Won stage</Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          id={`closed-lost-${stage.id}`}
                          checked={stage.is_closed_lost || false}
                          onCheckedChange={(checked) => onUpdate(stage.id, { 
                            is_closed_lost: checked,
                            is_closed_won: checked ? false : stage.is_closed_won,
                            is_final: checked ? true : stage.is_final
                          })}
                        />
                        <Label htmlFor={`closed-lost-${stage.id}`}>Lost stage</Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          id={`final-${stage.id}`}
                          checked={stage.is_final || false}
                          onCheckedChange={(checked) => onUpdate(stage.id, { is_final: checked })}
                        />
                        <Label htmlFor={`final-${stage.id}`}>Final stage</Label>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function EditPipelinePage() {
  const router = useRouter();
  const params = useParams();
  const pipelineId = params.id as string;
  
  const [pipeline, setPipeline] = useState<Pipeline | null>(null);
  const [stages, setStages] = useState<Stage[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    fetchPipeline();
  }, [pipelineId]);

  const fetchPipeline = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/api/v1/pipelines/${pipelineId}?include_stages=true`);
      const pipelineData = response.data?.data || response.data;
      setPipeline(pipelineData);
      setStages(pipelineData.stages || []);
    } catch (error) {
      console.error("Failed to fetch pipeline:", error);
      toast({
        title: "Error",
        description: "Failed to load pipeline",
        variant: "destructive",
      });
      router.push("/dashboard/pipelines");
    } finally {
      setLoading(false);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setStages((items) => {
        const oldIndex = items.findIndex(i => i.id === active.id);
        const newIndex = items.findIndex(i => i.id === over.id);
        return arrayMove(items, oldIndex, newIndex).map((stage, index) => ({
          ...stage,
          sort_order: index
        }));
      });
      setHasChanges(true);
    }
  };

  const updateStage = (id: string, updates: Partial<Stage>) => {
    setStages(stages.map(stage => 
      stage.id === id ? { ...stage, ...updates } : stage
    ));
    setHasChanges(true);
  };

  const deleteStage = async (id: string) => {
    if (!confirm("Are you sure you want to delete this stage?")) return;
    
    try {
      await apiClient.delete(`/api/v1/stages/${id}`);
      setStages(stages.filter(s => s.id !== id));
      toast({
        title: "Success",
        description: "Stage deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete stage",
        variant: "destructive",
      });
    }
  };

  const addStage = async () => {
    const newStage = {
      name: `New Stage ${stages.length + 1}`,
      color: colorOptions[stages.length % colorOptions.length],
      sort_order: stages.length,
    };

    try {
      const response = await apiClient.post(`/api/v1/pipelines/${pipelineId}/stages`, newStage);
      setStages([...stages, response.data.data]);
      toast({
        title: "Success",
        description: "Stage added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add stage",
        variant: "destructive",
      });
    }
  };

  const savePipelineChanges = async () => {
    if (!pipeline) return;
    
    try {
      setSaving(true);
      
      // Update pipeline details
      await apiClient.put(`/api/v1/pipelines/${pipelineId}`, {
        name: pipeline.name,
        description: pipeline.description,
        color: pipeline.color,
        is_active: pipeline.is_active,
        is_default: pipeline.is_default,
      });

      // Update pipeline settings
      if (pipeline.settings) {
        await apiClient.patch(`/api/v1/pipelines/${pipelineId}/settings`, pipeline.settings);
      }

      // Update stages
      for (const stage of stages) {
        await apiClient.put(`/api/v1/stages/${stage.id}`, {
          name: stage.name,
          color: stage.color,
          sort_order: stage.sort_order,
          is_closed_won: stage.is_closed_won,
          is_closed_lost: stage.is_closed_lost,
          is_final: stage.is_final,
          probability: stage.probability,
          auto_move_after_days: stage.auto_move_after_days,
        });
      }

      // Reorder stages if needed
      await apiClient.patch(`/api/v1/pipelines/${pipelineId}/stages/reorder`, {
        stage_orders: stages.map((stage, index) => ({
          id: stage.id,
          order: index,
        })),
      });

      toast({
        title: "Success",
        description: "Pipeline updated successfully",
      });
      setHasChanges(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save changes",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <Skeleton className="h-12 w-64" />
        <Skeleton className="h-48" />
        <Skeleton className="h-96" />
      </div>
    );
  }

  if (!pipeline) {
    return null;
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.push("/dashboard/pipelines")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Edit Pipeline</h1>
            <p className="text-gray-500 mt-1">Customize your pipeline workflow</p>
          </div>
        </div>
        <div className="flex gap-2">
          {hasChanges && (
            <Badge variant="outline" className="gap-1">
              <AlertCircle className="h-3 w-3" />
              Unsaved changes
            </Badge>
          )}
          <Button 
            onClick={savePipelineChanges} 
            disabled={saving || !hasChanges}
          >
            <Save className="mr-2 h-4 w-4" />
            {saving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">General Settings</TabsTrigger>
          <TabsTrigger value="stages">Stages</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Pipeline Name</Label>
                  <Input
                    id="name"
                    value={pipeline.name}
                    onChange={(e) => {
                      setPipeline({ ...pipeline, name: e.target.value });
                      setHasChanges(true);
                    }}
                  />
                </div>
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Input
                    id="type"
                    value={pipeline.type}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={pipeline.description || ""}
                  onChange={(e) => {
                    setPipeline({ ...pipeline, description: e.target.value });
                    setHasChanges(true);
                  }}
                  placeholder="Describe the purpose of this pipeline"
                />
              </div>
              <div>
                <Label>Pipeline Color</Label>
                <div className="flex gap-2 mt-2">
                  {colorOptions.map((color) => (
                    <button
                      key={color}
                      onClick={() => {
                        setPipeline({ ...pipeline, color });
                        setHasChanges(true);
                      }}
                      className={`
                        w-8 h-8 rounded-full border-2 transition-all
                        ${pipeline.color === color 
                          ? "border-gray-900 dark:border-white scale-110" 
                          : "border-gray-300 hover:border-gray-500"}
                      `}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="active">Active Status</Label>
                  <p className="text-sm text-gray-500">
                    Inactive pipelines won't be available for new cards
                  </p>
                </div>
                <Switch
                  id="active"
                  checked={pipeline.is_active}
                  onCheckedChange={(checked) => {
                    setPipeline({ ...pipeline, is_active: checked });
                    setHasChanges(true);
                  }}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="default">Default Pipeline</Label>
                  <p className="text-sm text-gray-500">
                    Default pipeline is used when no pipeline is specified
                  </p>
                </div>
                <Switch
                  id="default"
                  checked={pipeline.is_default}
                  onCheckedChange={(checked) => {
                    setPipeline({ ...pipeline, is_default: checked });
                    setHasChanges(true);
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stages" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Pipeline Stages</CardTitle>
                  <CardDescription>
                    Drag to reorder stages. Cards will flow through these stages.
                  </CardDescription>
                </div>
                <Button onClick={addStage}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Stage
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {stages.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <p>No stages configured</p>
                  <Button onClick={addStage} className="mt-4">
                    Add First Stage
                  </Button>
                </div>
              ) : (
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={stages.map(s => s.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-4">
                      {stages.map((stage) => (
                        <SortableStage
                          key={stage.id}
                          stage={stage}
                          onUpdate={updateStage}
                          onDelete={deleteStage}
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Automation & SLA</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="automation">Enable Automation</Label>
                  <p className="text-sm text-gray-500">
                    Automate actions when cards move between stages
                  </p>
                </div>
                <Switch
                  id="automation"
                  checked={pipeline.settings?.automation_enabled || false}
                  onCheckedChange={(checked) => {
                    setPipeline({
                      ...pipeline,
                      settings: {
                        ...pipeline.settings,
                        automation_enabled: checked,
                      },
                    });
                    setHasChanges(true);
                  }}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="sla">Enable SLA</Label>
                  <p className="text-sm text-gray-500">
                    Track service level agreements for each stage
                  </p>
                </div>
                <Switch
                  id="sla"
                  checked={pipeline.settings?.sla_enabled || false}
                  onCheckedChange={(checked) => {
                    setPipeline({
                      ...pipeline,
                      settings: {
                        ...pipeline.settings,
                        sla_enabled: checked,
                      },
                    });
                    setHasChanges(true);
                  }}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Access Control</CardTitle>
              <CardDescription>
                Configure who can view and manage this pipeline
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>Allowed Roles</Label>
                  <p className="text-sm text-gray-500 mb-3">
                    Select which roles can access this pipeline
                  </p>
                  <div className="space-y-2">
                    {["admin", "manager", "user"].map((role) => {
                      const isAllowed = pipeline.settings?.allowed_roles?.includes(role) ?? true;
                      return (
                        <div key={role} className="flex items-center space-x-2">
                          <Switch
                            id={`role-${role}`}
                            checked={isAllowed}
                            onCheckedChange={(checked) => {
                              const currentRoles = pipeline.settings?.allowed_roles || ["admin", "manager", "user"];
                              const newRoles = checked
                                ? [...currentRoles.filter(r => r !== role), role]
                                : currentRoles.filter(r => r !== role);
                              setPipeline({
                                ...pipeline,
                                settings: {
                                  ...pipeline.settings,
                                  allowed_roles: newRoles,
                                },
                              });
                              setHasChanges(true);
                            }}
                          />
                          <Label htmlFor={`role-${role}`} className="capitalize cursor-pointer">
                            {role}
                          </Label>
                        </div>
                      );
                    })}
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <Label>Required Fields</Label>
                  <p className="text-sm text-gray-500 mb-3">
                    Fields that must be filled when creating cards in this pipeline
                  </p>
                  <div className="space-y-2">
                    {["title", "description", "value", "contact", "company"].map((field) => {
                      const isRequired = pipeline.settings?.required_fields?.includes(field) ?? false;
                      return (
                        <div key={field} className="flex items-center space-x-2">
                          <Switch
                            id={`field-${field}`}
                            checked={isRequired}
                            onCheckedChange={(checked) => {
                              const currentFields = pipeline.settings?.required_fields || [];
                              const newFields = checked
                                ? [...currentFields.filter(f => f !== field), field]
                                : currentFields.filter(f => f !== field);
                              setPipeline({
                                ...pipeline,
                                settings: {
                                  ...pipeline.settings,
                                  required_fields: newFields,
                                },
                              });
                              setHasChanges(true);
                            }}
                          />
                          <Label htmlFor={`field-${field}`} className="capitalize cursor-pointer">
                            {field}
                          </Label>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}