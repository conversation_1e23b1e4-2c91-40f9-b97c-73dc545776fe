"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { 
  ArrowLeft, Plus, X, Briefcase, Users, Layers, 
  HeadphonesIcon, Package, Trash2, GripVertical,
  Save, Palette
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { apiClient } from "@/lib/api";
import { toast } from "@/hooks/use-toast";

const pipelineSchema = z.object({
  name: z.string().min(1, "Pipeline name is required"),
  description: z.string().optional(),
  type: z.enum(["sales", "hr", "projects", "support", "custom"]),
  color: z.string().default("#3B82F6"),
  automation_enabled: z.boolean().default(false),
  sla_enabled: z.boolean().default(false),
});

type PipelineFormData = z.infer<typeof pipelineSchema>;

interface StageData {
  id: string;
  name: string;
  color: string;
  description?: string;
  is_closed_won?: boolean;
  is_closed_lost?: boolean;
  is_final?: boolean;
}

const defaultStageTemplates: Record<string, StageData[]> = {
  sales: [
    { id: "1", name: "Lead", color: "#94A3B8", description: "New potential customers" },
    { id: "2", name: "Qualified", color: "#60A5FA", description: "Qualified leads" },
    { id: "3", name: "Proposal", color: "#A78BFA", description: "Proposal sent" },
    { id: "4", name: "Negotiation", color: "#FCD34D", description: "In negotiation" },
    { id: "5", name: "Won", color: "#34D399", description: "Deal closed", is_closed_won: true, is_final: true },
    { id: "6", name: "Lost", color: "#F87171", description: "Deal lost", is_closed_lost: true, is_final: true },
  ],
  hr: [
    { id: "1", name: "Application", color: "#94A3B8", description: "New applications" },
    { id: "2", name: "Screening", color: "#60A5FA", description: "Initial screening" },
    { id: "3", name: "Interview", color: "#A78BFA", description: "Interview process" },
    { id: "4", name: "Assessment", color: "#FCD34D", description: "Technical assessment" },
    { id: "5", name: "Offer", color: "#FB923C", description: "Offer extended" },
    { id: "6", name: "Hired", color: "#34D399", description: "Candidate hired", is_closed_won: true, is_final: true },
    { id: "7", name: "Rejected", color: "#F87171", description: "Not selected", is_closed_lost: true, is_final: true },
  ],
  projects: [
    { id: "1", name: "Backlog", color: "#94A3B8", description: "Project backlog" },
    { id: "2", name: "Planning", color: "#60A5FA", description: "In planning" },
    { id: "3", name: "In Progress", color: "#A78BFA", description: "Active development" },
    { id: "4", name: "Review", color: "#FCD34D", description: "Under review" },
    { id: "5", name: "Completed", color: "#34D399", description: "Project completed", is_closed_won: true, is_final: true },
  ],
  support: [
    { id: "1", name: "New", color: "#94A3B8", description: "New tickets" },
    { id: "2", name: "Open", color: "#60A5FA", description: "Being worked on" },
    { id: "3", name: "Pending", color: "#FCD34D", description: "Awaiting response" },
    { id: "4", name: "Resolved", color: "#34D399", description: "Issue resolved", is_closed_won: true, is_final: true },
    { id: "5", name: "Closed", color: "#6B7280", description: "Ticket closed", is_final: true },
  ],
  custom: [
    { id: "1", name: "Stage 1", color: "#94A3B8", description: "" },
    { id: "2", name: "Stage 2", color: "#60A5FA", description: "" },
    { id: "3", name: "Stage 3", color: "#A78BFA", description: "" },
    { id: "4", name: "Final", color: "#34D399", description: "", is_final: true },
  ],
};

const colorOptions = [
  "#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", 
  "#EC4899", "#14B8A6", "#F97316", "#6366F1", "#84CC16"
];

export default function NewPipelinePage() {
  const router = useRouter();
  const [stages, setStages] = useState<StageData[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState("");

  const form = useForm<PipelineFormData>({
    resolver: zodResolver(pipelineSchema),
    defaultValues: {
      name: "",
      description: "",
      type: "sales",
      color: "#3B82F6",
      automation_enabled: false,
      sla_enabled: false,
    },
  });

  const onSubmit = async (data: PipelineFormData) => {
    if (stages.length === 0) {
      toast({
        title: "Error",
        description: "Please add at least one stage",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      const pipelineData = {
        ...data,
        stages: stages.map((stage, index) => ({
          name: stage.name,
          description: stage.description,
          color: stage.color,
          is_closed_won: stage.is_closed_won || false,
          is_closed_lost: stage.is_closed_lost || false,
          is_final: stage.is_final || false,
          sort_order: index,
        })),
        settings: {
          automation_enabled: data.automation_enabled,
          sla_enabled: data.sla_enabled,
        },
      };

      const response = await apiClient.post("/api/v1/pipelines", pipelineData);
      
      toast({
        title: "Success",
        description: "Pipeline created successfully",
      });
      
      router.push(`/dashboard/pipelines/${response.data.data.id}/edit`);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Failed to create pipeline",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (type: string) => {
    setSelectedTemplate(type);
    setStages(defaultStageTemplates[type].map(s => ({ ...s, id: Date.now().toString() + Math.random() })));
    form.setValue("type", type as any);
  };

  const addStage = () => {
    const newStage: StageData = {
      id: Date.now().toString(),
      name: `Stage ${stages.length + 1}`,
      color: colorOptions[stages.length % colorOptions.length],
      description: "",
    };
    setStages([...stages, newStage]);
  };

  const updateStage = (id: string, field: keyof StageData, value: any) => {
    setStages(stages.map(stage => 
      stage.id === id ? { ...stage, [field]: value } : stage
    ));
  };

  const deleteStage = (id: string) => {
    setStages(stages.filter(stage => stage.id !== id));
  };

  const moveStage = (index: number, direction: "up" | "down") => {
    const newStages = [...stages];
    const targetIndex = direction === "up" ? index - 1 : index + 1;
    
    if (targetIndex >= 0 && targetIndex < stages.length) {
      [newStages[index], newStages[targetIndex]] = [newStages[targetIndex], newStages[index]];
      setStages(newStages);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Create New Pipeline</h1>
            <p className="text-gray-500 mt-1">Set up a new workflow for your team</p>
          </div>
        </div>
      </div>

      {/* Template Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Choose a Template</CardTitle>
          <CardDescription>
            Start with a pre-configured template or create a custom pipeline
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {Object.entries({
              sales: { icon: <Briefcase />, label: "Sales" },
              hr: { icon: <Users />, label: "HR" },
              projects: { icon: <Layers />, label: "Projects" },
              support: { icon: <HeadphonesIcon />, label: "Support" },
              custom: { icon: <Package />, label: "Custom" },
            }).map(([key, { icon, label }]) => (
              <button
                key={key}
                onClick={() => handleTemplateSelect(key)}
                className={`
                  p-4 rounded-lg border-2 transition-all
                  ${selectedTemplate === key 
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-950" 
                    : "border-gray-200 hover:border-gray-300 dark:border-gray-700"}
                `}
              >
                <div className="flex flex-col items-center space-y-2">
                  <div className="text-gray-600 dark:text-gray-400">{icon}</div>
                  <span className="text-sm font-medium">{label}</span>
                </div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Pipeline Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Pipeline Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pipeline Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Enterprise Sales" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="color"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pipeline Color</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          {colorOptions.map((color) => (
                            <button
                              key={color}
                              type="button"
                              onClick={() => field.onChange(color)}
                              className={`
                                w-8 h-8 rounded-full border-2
                                ${field.value === color ? "border-gray-900 dark:border-gray-100" : "border-gray-300"}
                              `}
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe the purpose of this pipeline..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="automation_enabled"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between">
                      <div>
                        <FormLabel>Enable Automation</FormLabel>
                        <FormDescription>
                          Automate actions when cards move between stages
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sla_enabled"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between">
                      <div>
                        <FormLabel>Enable SLA</FormLabel>
                        <FormDescription>
                          Track time limits for each stage
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Stage Configuration */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Pipeline Stages</CardTitle>
              <CardDescription>
                Configure the stages for your pipeline workflow
              </CardDescription>
            </div>
            <Button onClick={addStage} size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Stage
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {stages.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p>No stages added yet</p>
              <p className="text-sm mt-2">
                Choose a template above or add stages manually
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {stages.map((stage, index) => (
                <div 
                  key={stage.id}
                  className="flex items-start gap-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-900"
                >
                  <div className="flex flex-col gap-1">
                    <button
                      type="button"
                      onClick={() => moveStage(index, "up")}
                      disabled={index === 0}
                      className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded disabled:opacity-50"
                    >
                      <GripVertical className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="flex-1 space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <Input
                        placeholder="Stage name"
                        value={stage.name}
                        onChange={(e) => updateStage(stage.id, "name", e.target.value)}
                      />
                      <div className="flex gap-2">
                        <div className="flex gap-1">
                          {colorOptions.slice(0, 5).map((color) => (
                            <button
                              key={color}
                              type="button"
                              onClick={() => updateStage(stage.id, "color", color)}
                              className={`
                                w-6 h-6 rounded-full border
                                ${stage.color === color ? "border-gray-900" : "border-gray-300"}
                              `}
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                        <div className="flex gap-2">
                          {stage.is_closed_won && (
                            <Badge variant="success" className="text-xs">Won</Badge>
                          )}
                          {stage.is_closed_lost && (
                            <Badge variant="destructive" className="text-xs">Lost</Badge>
                          )}
                          {stage.is_final && (
                            <Badge variant="secondary" className="text-xs">Final</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <Input
                      placeholder="Stage description (optional)"
                      value={stage.description}
                      onChange={(e) => updateStage(stage.id, "description", e.target.value)}
                    />
                  </div>

                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => deleteStage(stage.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button 
          onClick={form.handleSubmit(onSubmit)} 
          disabled={loading || stages.length === 0}
        >
          <Save className="mr-2 h-4 w-4" />
          {loading ? "Creating..." : "Create Pipeline"}
        </Button>
      </div>
    </div>
  );
}