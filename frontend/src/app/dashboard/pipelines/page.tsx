"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Plus, Settings, MoreVertical, Users, Layers, Briefcase, HeadphonesIcon, Package, Edit, Trash2, Copy } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuthStore } from "@/stores/auth";
import { apiClient } from "@/lib/api";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/hooks/use-toast";

interface Pipeline {
  id: string;
  name: string;
  description: string;
  type: "sales" | "hr" | "projects" | "support" | "custom";
  color: string;
  icon?: string;
  is_default: boolean;
  is_active: boolean;
  stages?: Stage[];
  settings?: {
    allowed_users?: string[];
    allowed_roles?: string[];
    automation_enabled?: boolean;
    sla_enabled?: boolean;
  };
  cards?: any[];
}

interface Stage {
  id: string;
  name: string;
  color: string;
  sort_order: number;
  cards?: any[];
}

const pipelineTypeIcons = {
  sales: <Briefcase className="h-5 w-5" />,
  hr: <Users className="h-5 w-5" />,
  projects: <Layers className="h-5 w-5" />,
  support: <HeadphonesIcon className="h-5 w-5" />,
  custom: <Package className="h-5 w-5" />,
};

const pipelineTypeLabels = {
  sales: "Sales",
  hr: "HR & Recruiting",
  projects: "Projects",
  support: "Support",
  custom: "Custom",
};

export default function PipelinesPage() {
  const router = useRouter();
  const { user } = useAuthStore();
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");

  useEffect(() => {
    fetchPipelines();
  }, []);

  const fetchPipelines = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get("/api/v1/pipelines?include_stages=true");
      // Проверяем разные возможные структуры ответа
      const pipelinesData = response.data?.data?.pipelines || 
                           response.data?.pipelines || 
                           response.data?.data || 
                           response.data || 
                           [];
      // Если это объект с массивом pipelines
      if (pipelinesData && typeof pipelinesData === 'object' && !Array.isArray(pipelinesData)) {
        setPipelines(pipelinesData.pipelines || []);
      } else if (Array.isArray(pipelinesData)) {
        setPipelines(pipelinesData);
      } else {
        setPipelines([]);
      }
    } catch (error) {
      console.error("Failed to fetch pipelines:", error);
      toast({
        title: "Error",
        description: "Failed to load pipelines",
        variant: "destructive",
      });
      setPipelines([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePipeline = async (id: string) => {
    if (!confirm("Are you sure you want to delete this pipeline?")) return;
    
    try {
      await apiClient.delete(`/api/v1/pipelines/${id}`);
      toast({
        title: "Success",
        description: "Pipeline deleted successfully",
      });
      fetchPipelines();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete pipeline",
        variant: "destructive",
      });
    }
  };

  const handleDuplicatePipeline = async (pipeline: Pipeline) => {
    try {
      const newPipeline = {
        name: `${pipeline.name} (Copy)`,
        description: pipeline.description,
        type: pipeline.type,
        color: pipeline.color,
        stages: pipeline.stages?.map(s => ({
          name: s.name,
          color: s.color,
        })),
      };
      
      await apiClient.post("/api/v1/pipelines", newPipeline);
      toast({
        title: "Success",
        description: "Pipeline duplicated successfully",
      });
      fetchPipelines();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate pipeline",
        variant: "destructive",
      });
    }
  };

  const filteredPipelines = activeTab === "all" 
    ? pipelines 
    : pipelines.filter(p => p.type === activeTab);

  const canManagePipelines = user?.legacy_role === "admin" || user?.legacy_role === "manager" || user?.role?.name === "Administrator" || user?.role?.name === "Manager";

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-48" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Pipeline Management</h1>
          <p className="text-gray-500 mt-1">Manage your sales processes and workflows</p>
        </div>
        {canManagePipelines && (
          <Button onClick={() => router.push("/dashboard/pipelines/new")}>
            <Plus className="mr-2 h-4 w-4" />
            New Pipeline
          </Button>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Pipelines</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="hr">HR</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="support">Support</TabsTrigger>
          <TabsTrigger value="custom">Custom</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {filteredPipelines.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Package className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">No pipelines found</h3>
                <p className="text-gray-500 mb-4">
                  {activeTab === "all" 
                    ? "Create your first pipeline to get started"
                    : `No ${pipelineTypeLabels[activeTab as keyof typeof pipelineTypeLabels]} pipelines yet`}
                </p>
                {canManagePipelines && (
                  <Button onClick={() => router.push("/dashboard/pipelines/new")}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Pipeline
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filteredPipelines.map((pipeline) => (
                <Card key={pipeline.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="flex items-start space-x-3">
                        <div 
                          className="p-2 rounded-lg"
                          style={{ backgroundColor: `${pipeline.color}20` }}
                        >
                          {pipelineTypeIcons[pipeline.type]}
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-lg">{pipeline.name}</CardTitle>
                          <CardDescription className="mt-1">
                            {pipeline.description || "No description"}
                          </CardDescription>
                        </div>
                      </div>
                      {canManagePipelines && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => router.push(`/dashboard/pipelines/${pipeline.id}/edit`)}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Pipeline
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => router.push(`/dashboard/pipelines/${pipeline.id}/stages`)}
                            >
                              <Layers className="mr-2 h-4 w-4" />
                              Manage Stages
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDuplicatePipeline(pipeline)}
                            >
                              <Copy className="mr-2 h-4 w-4" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeletePipeline(pipeline.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Type</span>
                        <Badge variant="outline">
                          {pipelineTypeLabels[pipeline.type]}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Stages</span>
                        <span className="font-medium">{pipeline.stages?.length || 0}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Status</span>
                        <Badge variant={pipeline.is_active ? "default" : "secondary"}>
                          {pipeline.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </div>

                      {pipeline.is_default && (
                        <Badge className="w-full justify-center" variant="outline">
                          Default Pipeline
                        </Badge>
                      )}

                      <div className="flex gap-2">
                        {pipeline.settings?.automation_enabled && (
                          <Badge variant="secondary" className="text-xs">
                            Automation
                          </Badge>
                        )}
                        {pipeline.settings?.sla_enabled && (
                          <Badge variant="secondary" className="text-xs">
                            SLA
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t">
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={() => router.push(`/dashboard/pipelines/${pipeline.id}`)}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        View Pipeline
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}