"use client";

import { useState, useCallback, useMemo } from "react";
import { useContacts } from "@/lib/react-query/hooks";
import { useDebounce } from "@/lib/hooks/use-debounce";
import { getContactStatus, getContactCategories, filterByText, sortItems } from "@/lib/utils/filter-utils";
import { Card, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { SearchFilterBar } from "@/components/ui/search-filter-bar";
import { 
  Plus, User, Users, Building, Briefcase 
} from "lucide-react";
import { AddContactDialog } from "@/components/contacts/add-contact-dialog";
import { EditContactDialog } from "@/components/contacts/edit-contact-dialog";
import { DeleteContactDialog } from "@/components/contacts/delete-contact-dialog";
import { ContactCard } from "@/components/contacts/contact-card";
import { Contact } from "@/types";

export default function ContactsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<any>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [addContactOpen, setAddContactOpen] = useState(false);
  const [editContactOpen, setEditContactOpen] = useState(false);
  const [deleteContactOpen, setDeleteContactOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

  // Load all contacts without any filters for client-side filtering
  const queryParams = useMemo(() => ({
    page: currentPage,
    limit: 200, // Load more data for client-side filtering
    // Remove all filters - we'll do client-side filtering
  }), [currentPage]); // Only depend on page for server requests

  const { data: allContacts, isLoading, error } = useContacts(queryParams);

  // Client-side filtering and sorting
  const { filteredContacts, totalCount } = useMemo(() => {
    if (!allContacts) {
      return { filteredContacts: [], totalCount: 0 };
    }

    let filtered = [...allContacts];

    // Apply search filter using utility
    filtered = filterByText(filtered, searchQuery, (contact) => [
      contact.first_name || '',
      contact.last_name || '',
      contact.email || '',
      contact.phone || '',
      contact.company?.name || '',
      contact.job_title || ''
    ]);

    // Apply category filters using smart categorization
    if (filters.categories?.length > 0) {
      filtered = filtered.filter(contact => {
        const contactCategories = getContactCategories(contact);
        return filters.categories.some((category: string) =>
          contactCategories.includes(category.toLowerCase())
        );
      });
    }

    // Apply status filters using smart status detection
    if (filters.statuses?.length > 0) {
      filtered = filtered.filter(contact => {
        const status = getContactStatus(contact);
        return filters.statuses.includes(status);
      });
    }

    // Apply date range filter
    if (filters.dateRange?.from || filters.dateRange?.to) {
      filtered = filtered.filter(contact => {
        const contactDate = new Date(contact.created_at);
        const fromDate = filters.dateRange?.from;
        const toDate = filters.dateRange?.to;

        if (fromDate && contactDate < fromDate) return false;
        if (toDate && contactDate > toDate) return false;
        return true;
      });
    }

    // Apply sorting using utility
    filtered = sortItems(filtered, filters.sortBy, (contact, field) => {
      switch (field) {
        case 'name':
          return contact.first_name || '';
        case 'created':
          return new Date(contact.created_at);
        case 'updated':
          return new Date(contact.updated_at);
        default:
          return '';
      }
    });

    return {
      filteredContacts: filtered,
      totalCount: filtered.length
    };
  }, [allContacts, searchQuery, filters]);

  // For display purposes, use filteredContacts
  const contacts = filteredContacts;

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  }, []);

  const handleFiltersChange = useCallback((newFilters: any) => {
    setFilters(newFilters);
    setCurrentPage(1);
  }, []);

  const handleEditContact = (contact: Contact) => {
    setSelectedContact(contact);
    setEditContactOpen(true);
  };

  const handleDeleteContact = (contact: Contact) => {
    setSelectedContact(contact);
    setDeleteContactOpen(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Contacts</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">Contacts</h1>
        <div className="bg-destructive/15 border border-destructive/20 rounded-md p-4">
          <p className="text-destructive">Failed to load contacts</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Контакты</h1>
          <p className="text-gray-600 mt-1">
            Управление контактами и взаимоотношениями
          </p>
        </div>
        <Button onClick={() => setAddContactOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Добавить контакт
        </Button>
      </div>

      {/* Search and Filters */}
      <SearchFilterBar
        placeholder="Поиск контактов по имени, email, телефону..."
        searchValue={searchQuery}
        onSearch={handleSearch}
        onFiltersChange={handleFiltersChange}
        onExport={() => console.log("Export contacts")}
        onImport={() => console.log("Import contacts")}
        totalCount={allContacts?.length}
        filteredCount={totalCount}
        sortOptions={[
          { value: "name_asc", label: "Имя (А-Я)" },
          { value: "name_desc", label: "Имя (Я-А)" },
          { value: "created_asc", label: "Сначала старые" },
          { value: "created_desc", label: "Сначала новые" },
          { value: "updated_desc", label: "Недавно обновленные" },
        ]}
        categoryOptions={[
          { value: "vip", label: "VIP", icon: <User className="h-4 w-4 mr-2" /> },
          { value: "lead", label: "Лид", icon: <Users className="h-4 w-4 mr-2" /> },
          { value: "customer", label: "Клиент", icon: <Building className="h-4 w-4 mr-2" /> },
          { value: "partner", label: "Партнер", icon: <Briefcase className="h-4 w-4 mr-2" /> },
        ]}
        statusOptions={[
          { value: "active", label: "Активный" },
          { value: "inactive", label: "Неактивный" },
          { value: "pending", label: "Ожидает" },
        ]}
      />

      {/* Contacts Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="h-48 animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      ) : contacts && contacts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {contacts.map((contact) => (
            <ContactCard
              key={contact.id}
              contact={contact}
              onEdit={handleEditContact}
              onDelete={handleDeleteContact}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">
            {error ? "Ошибка загрузки контактов" : "Контакты не найдены"}
          </p>
          <p className="text-gray-400 mt-2">
            {error
              ? "Попробуйте обновить страницу"
              : searchQuery
                ? "Попробуйте изменить параметры поиска"
                : "Создайте первый контакт, чтобы начать"
            }
          </p>
          <Button className="mt-4" onClick={() => setAddContactOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Добавить контакт
          </Button>
        </div>
      )}

      {/* Pagination would go here */}

      {/* Dialogs */}
      <AddContactDialog
        open={addContactOpen}
        onOpenChange={setAddContactOpen}
      />

      <EditContactDialog
        contact={selectedContact}
        open={editContactOpen}
        onOpenChange={setEditContactOpen}
      />

      <DeleteContactDialog
        contact={selectedContact}
        open={deleteContactOpen}
        onOpenChange={setDeleteContactOpen}
      />
    </div>
  );
}