import { Contact, Company } from "@/types";

// Contact status determination
export function getContactStatus(contact: Contact): string {
  // Check if contact has complete information
  const hasEmail = contact.email && contact.email.trim() !== '';
  const hasPhone = contact.phone && contact.phone.trim() !== '';
  const hasCompany = contact.company && contact.company.name;
  
  // Determine status based on completeness and activity
  if (hasEmail && hasPhone && hasCompany) {
    return 'active';
  } else if (hasEmail || hasPhone) {
    return 'pending';
  } else {
    return 'inactive';
  }
}

// Company status determination
export function getCompanyStatus(company: Company): string {
  const hasWebsite = company.website && company.website.trim() !== '';
  const hasEmail = company.email && company.email.trim() !== '';
  const hasPhone = company.phone && company.phone.trim() !== '';
  const hasAddress = company.address && company.address.trim() !== '';
  
  // Determine status based on completeness
  if (hasWebsite && hasEmail && (hasPhone || hasAddress)) {
    return 'active';
  } else if (hasEmail || hasPhone) {
    return 'pending';
  } else {
    return 'inactive';
  }
}

// Contact category determination
export function getContactCategories(contact: Contact): string[] {
  const categories: string[] = [];
  
  // Check existing tags
  if (contact.tags && contact.tags.length > 0) {
    categories.push(...contact.tags.map(tag => tag.name.toLowerCase()));
  }
  
  // Infer categories from data
  if (contact.job_title) {
    const title = contact.job_title.toLowerCase();
    if (title.includes('director') || title.includes('ceo') || title.includes('president')) {
      categories.push('vip');
    }
    if (title.includes('partner')) {
      categories.push('partner');
    }
    if (title.includes('manager') || title.includes('lead')) {
      categories.push('lead');
    }
  }
  
  // If has company, likely a customer
  if (contact.company && contact.company.name) {
    categories.push('customer');
  }
  
  // If no categories found, default to lead
  if (categories.length === 0) {
    categories.push('lead');
  }
  
  return [...new Set(categories)]; // Remove duplicates
}

// Company category determination
export function getCompanyCategories(company: Company): string[] {
  const categories: string[] = [];
  
  if (company.industry) {
    const industry = company.industry.toLowerCase();
    
    // Map industries to categories
    if (industry.includes('technology') || industry.includes('software') || industry.includes('tech')) {
      categories.push('tech');
    }
    if (industry.includes('financial') || industry.includes('bank') || industry.includes('finance')) {
      categories.push('finance');
    }
    if (industry.includes('health') || industry.includes('medical') || industry.includes('pharma')) {
      categories.push('healthcare');
    }
    if (industry.includes('retail') || industry.includes('commerce') || industry.includes('shop')) {
      categories.push('retail');
    }
    if (industry.includes('manufacturing') || industry.includes('production')) {
      categories.push('manufacturing');
    }
    if (industry.includes('service') || industry.includes('consulting')) {
      categories.push('services');
    }
  }
  
  // If no specific category found, use general business
  if (categories.length === 0) {
    categories.push('business');
  }
  
  return [...new Set(categories)];
}

// Generic filter function for arrays
export function filterByText<T>(
  items: T[],
  searchQuery: string,
  getSearchableText: (item: T) => string[]
): T[] {
  if (!searchQuery.trim()) return items;
  
  const query = searchQuery.toLowerCase();
  return items.filter(item => {
    const searchableTexts = getSearchableText(item);
    return searchableTexts.some(text => 
      text && text.toLowerCase().includes(query)
    );
  });
}

// Sort function with multiple criteria
export function sortItems<T>(
  items: T[],
  sortBy: string,
  getSortValue: (item: T, field: string) => any
): T[] {
  if (!sortBy) return items;
  
  const [field, direction] = sortBy.split('_');
  const isDesc = direction === 'desc';
  
  return [...items].sort((a, b) => {
    const aValue = getSortValue(a, field);
    const bValue = getSortValue(b, field);
    
    // Handle different data types
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const result = aValue.localeCompare(bValue);
      return isDesc ? -result : result;
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      const result = aValue - bValue;
      return isDesc ? -result : result;
    }
    
    if (aValue instanceof Date && bValue instanceof Date) {
      const result = aValue.getTime() - bValue.getTime();
      return isDesc ? -result : result;
    }
    
    // Fallback to string comparison
    const result = String(aValue).localeCompare(String(bValue));
    return isDesc ? -result : result;
  });
}
