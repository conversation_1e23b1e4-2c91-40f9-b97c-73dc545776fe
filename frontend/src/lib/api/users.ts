import { ApiR<PERSON>ponse, PaginatedResponse, User, SearchParams } from "@/types";
import { apiClient } from "./client";

export interface CreateUserData {
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  role_id?: string;
  phone?: string;
  department?: string;
  position?: string;
  location?: string;
  timezone?: string;
  language?: string;
  is_active?: boolean;
}

export interface UpdateUserData {
  email?: string;
  first_name?: string;
  last_name?: string;
  role_id?: string | null;
  phone?: string;
  department?: string;
  position?: string;
  location?: string;
  timezone?: string;
  language?: string;
  avatar?: string;
  is_active?: boolean;
  email_verified?: boolean;
  two_factor_enabled?: boolean;
}

export interface UserFilters {
  search?: string;
  role_id?: string;
  is_active?: boolean;
  department?: string;
  page?: number;
  limit?: number;
}

export interface BulkAction {
  user_ids: string[];
  action: 'delete' | 'activate' | 'deactivate' | 'assign_role';
  role_id?: string;
}

export const usersApi = {
  // Get users list with filters and pagination
  getUsers: async (filters?: UserFilters): Promise<PaginatedResponse<User>> => {
    const searchParams = new URLSearchParams();
    
    if (filters?.search) searchParams.append("search", filters.search);
    if (filters?.role_id) searchParams.append("role_id", filters.role_id);
    if (filters?.is_active !== undefined) searchParams.append("is_active", filters.is_active.toString());
    if (filters?.department) searchParams.append("department", filters.department);
    if (filters?.page) searchParams.append("page", filters.page.toString());
    if (filters?.limit) searchParams.append("limit", filters.limit.toString());

    const queryString = searchParams.toString();
    const url = queryString ? `/api/v1/users?${queryString}` : "/api/v1/users";
    
    return apiClient.get<User[]>(url);
  },

  // Create a new user
  createUser: async (data: CreateUserData): Promise<ApiResponse<User>> => {
    return apiClient.post<User>("/api/v1/users", data);
  },

  // Get single user
  getUser: async (id: string): Promise<ApiResponse<User>> => {
    return apiClient.get<User>(`/api/v1/users/${id}`);
  },

  // Get current user profile
  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    return apiClient.get<User>("/api/v1/users/me");
  },

  // Update user
  updateUser: async (id: string, data: UpdateUserData): Promise<ApiResponse<User>> => {
    return apiClient.put<User>(`/api/v1/users/${id}`, data);
  },

  // Delete user
  deleteUser: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete<void>(`/api/v1/users/${id}`);
  },

  // Toggle user status (active/inactive)
  toggleUserStatus: async (id: string): Promise<ApiResponse<User>> => {
    return apiClient.post<User>(`/api/v1/users/${id}/toggle-status`, {});
  },

  // Assign role to user
  assignRole: async (id: string, roleId: string): Promise<ApiResponse<void>> => {
    return apiClient.post<void>(`/api/v1/users/${id}/role`, { role_id: roleId });
  },

  // Remove role from user
  removeRole: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete<void>(`/api/v1/users/${id}/role`);
  },

  // Update user password (admin action)
  updatePassword: async (id: string, newPassword: string): Promise<ApiResponse<void>> => {
    return apiClient.put<void>(`/api/v1/users/${id}/password`, { new_password: newPassword });
  },

  // Change own password
  changePassword: async (oldPassword: string, newPassword: string): Promise<ApiResponse<void>> => {
    return apiClient.post<void>("/api/v1/users/me/change-password", {
      old_password: oldPassword,
      new_password: newPassword,
    });
  },

  // Get users by role
  getUsersByRole: async (roleId: string): Promise<ApiResponse<User[]>> => {
    return apiClient.get<User[]>(`/api/v1/users/role/${roleId}`);
  },

  // Bulk operations on users
  bulkOperations: async (data: BulkAction): Promise<ApiResponse<void>> => {
    return apiClient.post<void>("/api/v1/users/bulk", data);
  },

  // Get user statistics
  getStatistics: async (): Promise<ApiResponse<any>> => {
    return apiClient.get<any>("/api/v1/users/statistics");
  },

  // Get user permissions
  getUserPermissions: async (id: string): Promise<ApiResponse<any[]>> => {
    return apiClient.get<any[]>(`/api/v1/users/${id}/permissions`);
  },
};