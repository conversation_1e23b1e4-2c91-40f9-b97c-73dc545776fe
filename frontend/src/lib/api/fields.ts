import {
  FieldDefinition,
  EntityType,
  FieldType,
  ApiResponse,
} from "@/types";
import { apiClient } from "./client";

export const fieldsApi = {
  // Field Definition operations
  async getFieldDefinitions(entityType?: EntityType): Promise<ApiResponse<FieldDefinition[]>> {
    const params = entityType ? `?entity_type=${entityType}` : "";
    return apiClient.get<FieldDefinition[]>(`/api/field-definitions${params}`);
  },

  async getFieldDefinition(id: string): Promise<ApiResponse<FieldDefinition>> {
    return apiClient.get<FieldDefinition>(`/api/field-definitions/${id}`);
  },

  async createFieldDefinition(fieldData: {
    name: string;
    label: string;
    field_type: FieldType;
    entity_type: EntityType;
    is_required?: boolean;
    options?: string[];
    default_value?: any;
    validation_rules?: Record<string, any>;
  }): Promise<ApiResponse<FieldDefinition>> {
    return apiClient.post<FieldDefinition>("/api/field-definitions", fieldData);
  },

  async updateFieldDefinition(id: string, fieldData: {
    label?: string;
    is_required?: boolean;
    options?: string[];
    default_value?: any;
    validation_rules?: Record<string, any>;
    display_order?: number;
  }): Promise<ApiResponse<FieldDefinition>> {
    return apiClient.put<FieldDefinition>(`/api/field-definitions/${id}`, fieldData);
  },

  async deleteFieldDefinition(id: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/field-definitions/${id}`);
  },

  async reorderFieldDefinitions(entityType: EntityType, fieldOrders: Array<{
    field_id: string;
    display_order: number;
  }>): Promise<ApiResponse<FieldDefinition[]>> {
    return apiClient.patch<FieldDefinition[]>(`/api/field-definitions/reorder`, {
      entity_type: entityType,
      fields: fieldOrders,
    });
  },

  // Field validation
  async validateFieldValue(fieldId: string, value: any): Promise<ApiResponse<{
    is_valid: boolean;
    errors: string[];
  }>> {
    return apiClient.post(`/api/field-definitions/${fieldId}/validate`, { value });
  },

  // Field templates
  async getFieldTemplates(entityType: EntityType): Promise<ApiResponse<Array<{
    name: string;
    label: string;
    field_type: FieldType;
    options?: string[];
    validation_rules?: Record<string, any>;
  }>>> {
    return apiClient.get(`/api/field-definitions/templates?entity_type=${entityType}`);
  },

  async createFieldFromTemplate(entityType: EntityType, templateName: string): Promise<ApiResponse<FieldDefinition>> {
    return apiClient.post<FieldDefinition>("/api/field-definitions/from-template", {
      entity_type: entityType,
      template_name: templateName,
    });
  },
};