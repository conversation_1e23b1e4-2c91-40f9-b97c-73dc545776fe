import {
  Company,
  CreateCompanyRequest,
  SearchParams,
  PaginatedResponse,
  ApiResponse,
  Activity,
} from "@/types";
import { apiClient } from "./client";

export const companiesApi = {
  // Company CRUD operations
  async getCompanies(params?: SearchParams): Promise<PaginatedResponse<Company>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const response = await apiClient.get<any>(`/api/v1/companies?${searchParams.toString()}`);
    
    // Transform the backend response to match frontend expectations
    // Backend returns { data: [], meta: { total, per_page, current_page, last_page } }
    // Frontend expects { data: [], pagination: { total, limit, page, total_pages } }
    if (response.meta) {
      return {
        ...response,
        pagination: {
          total: response.meta.total || 0,
          limit: response.meta.per_page || 20,
          page: response.meta.current_page || 1,
          total_pages: response.meta.last_page || 1,
        }
      } as PaginatedResponse<Company>;
    }
    
    return response as PaginatedResponse<Company>;
  },

  async getCompany(id: string): Promise<ApiResponse<Company>> {
    return apiClient.get<Company>(`/api/v1/companies/${id}`);
  },

  async createCompany(companyData: CreateCompanyRequest): Promise<ApiResponse<Company>> {
    return apiClient.post<Company>("/api/v1/companies", companyData);
  },

  async updateCompany(id: string, companyData: Partial<CreateCompanyRequest>): Promise<ApiResponse<Company>> {
    return apiClient.put<Company>(`/api/v1/companies/${id}`, companyData);
  },

  async deleteCompany(id: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/v1/companies/${id}`);
  },

  // Company activities
  async getCompanyActivities(companyId: string): Promise<ApiResponse<Activity[]>> {
    return apiClient.get<Activity[]>(`/api/v1/companies/${companyId}/activities`);
  },

  async addCompanyActivity(companyId: string, activity: {
    type: string;
    title: string;
    description?: string;
    metadata?: Record<string, any>;
  }): Promise<ApiResponse<Activity>> {
    return apiClient.post<Activity>(`/api/v1/companies/${companyId}/activities`, activity);
  },

  // Company tags
  async addCompanyTags(companyId: string, tagIds: string[]): Promise<ApiResponse<Company>> {
    return apiClient.post<Company>(`/api/v1/companies/${companyId}/tags`, { tag_ids: tagIds });
  },

  async removeCompanyTag(companyId: string, tagId: string): Promise<ApiResponse<Company>> {
    return apiClient.delete<Company>(`/api/v1/companies/${companyId}/tags/${tagId}`);
  },

  // Company relationships
  async getCompanyContacts(companyId: string): Promise<ApiResponse<any[]>> {
    return apiClient.get(`/api/v1/companies/${companyId}/contacts`);
  },

  async getCompanyCards(companyId: string): Promise<ApiResponse<any[]>> {
    return apiClient.get(`/api/v1/companies/${companyId}/cards`);
  },

  // Bulk operations
  async bulkUpdateCompanies(companyIds: string[], updates: Partial<CreateCompanyRequest>): Promise<ApiResponse<Company[]>> {
    return apiClient.patch<Company[]>("/api/v1/companies/bulk", {
      company_ids: companyIds,
      updates,
    });
  },

  async bulkDeleteCompanies(companyIds: string[]): Promise<ApiResponse> {
    return apiClient.delete("/api/v1/companies/bulk", {
      data: { company_ids: companyIds },
    });
  },

  // Import/Export
  async importCompanies(file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<{
    imported: number;
    failed: number;
    errors: Array<{ row: number; errors: string[] }>;
  }>> {
    return apiClient.uploadFile("/api/v1/companies/import", file, onProgress);
  },

  async exportCompanies(params?: SearchParams, format: "csv" | "xlsx" = "csv"): Promise<Blob> {
    const searchParams = new URLSearchParams();
    searchParams.append("format", format);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await apiClient.get(`/api/v1/companies/export?${searchParams.toString()}`, {
      responseType: "blob",
    });
    
    return response.data as unknown as Blob;
  },
};