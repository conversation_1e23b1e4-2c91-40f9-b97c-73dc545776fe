import { ApiResponse, PaginatedResponse, Activity, CreateActivityRequest, SearchParams } from "@/types";
import { apiClient } from "./client";

interface ActivityFilters extends SearchParams {
  card_id?: string;
  contact_id?: string;
  company_id?: string;
  user_id?: string;
  type?: string[];
  date_from?: string;
  date_to?: string;
}

export const activitiesApi = {
  // Get activities list with filters and pagination
  getActivities: async (params?: ActivityFilters): Promise<PaginatedResponse<Activity>> => {
    const searchParams = new URLSearchParams();
    
    if (params?.q) searchParams.append("q", params.q);
    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.limit) searchParams.append("limit", params.limit.toString());
    if (params?.sort_by) searchParams.append("sort_by", params.sort_by);
    if (params?.sort_direction) searchParams.append("sort_direction", params.sort_direction);
    
    // Activity specific filters
    if (params?.card_id) searchParams.append("card_id", params.card_id);
    if (params?.contact_id) searchParams.append("contact_id", params.contact_id);
    if (params?.company_id) searchParams.append("company_id", params.company_id);
    if (params?.user_id) searchParams.append("user_id", params.user_id);
    if (params?.date_from) searchParams.append("date_from", params.date_from);
    if (params?.date_to) searchParams.append("date_to", params.date_to);
    
    if (params?.type && params.type.length > 0) {
      params.type.forEach(type => searchParams.append("type[]", type));
    }

    const queryString = searchParams.toString();
    const url = queryString ? `/activities?${queryString}` : "/activities";
    
    return apiClient.get<Activity[]>(url);
  },

  // Get single activity
  getActivity: async (id: string): Promise<ApiResponse<Activity>> => {
    return apiClient.get<Activity>(`/activities/${id}`);
  },

  // Create new activity
  createActivity: async (data: CreateActivityRequest): Promise<ApiResponse<Activity>> => {
    return apiClient.post<Activity>("/activities", data);
  },

  // Update activity
  updateActivity: async (id: string, data: Partial<CreateActivityRequest>): Promise<ApiResponse<Activity>> => {
    return apiClient.put<Activity>(`/activities/${id}`, data);
  },

  // Delete activity
  deleteActivity: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/activities/${id}`);
  },

  // Get activities for a specific card
  getCardActivities: async (cardId: string): Promise<ApiResponse<Activity[]>> => {
    return apiClient.get<Activity[]>(`/cards/${cardId}/activities`);
  },

  // Get activities for a specific contact
  getContactActivities: async (contactId: string): Promise<ApiResponse<Activity[]>> => {
    return apiClient.get<Activity[]>(`/contacts/${contactId}/activities`);
  },

  // Get activities for a specific company
  getCompanyActivities: async (companyId: string): Promise<ApiResponse<Activity[]>> => {
    return apiClient.get<Activity[]>(`/companies/${companyId}/activities`);
  },

  // Get recent activities (dashboard)
  getRecentActivities: async (limit = 20): Promise<ApiResponse<Activity[]>> => {
    return apiClient.get<Activity[]>(`/activities/recent?limit=${limit}`);
  },
};