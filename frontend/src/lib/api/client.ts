import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { ApiResponse } from "@/types";

class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor(baseURL: string = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8082") {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL,
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 10000,
    });

    this.setupInterceptors();
  }


  private setupInterceptors(): void {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        // Token is set via defaults when store hydrates
        // No need to check localStorage here
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        // Only redirect to login on 401 (Unauthorized) errors
        if (error.response?.status === 401) {
          // Token expired or invalid
          this.removeAuthToken();
          if (typeof window !== "undefined") {
            window.location.href = "/auth/login";
          }
        }

        // Transform error response to match our ApiResponse interface
        const errorResponse: ApiResponse = {
          success: false,
          message: error.response?.data?.message || error.message || "An error occurred",
          errors: error.response?.data?.errors || {},
        };

        return Promise.reject({
          ...error,
          response: {
            ...error.response,
            data: errorResponse,
          },
        });
      }
    );
  }


  public removeAuthToken(): void {
    if (typeof window === "undefined") return;
    // Remove from axios defaults
    delete this.client.defaults.headers.common["Authorization"];
    // Note: Token removal from storage is handled by Zustand persist middleware
  }

  public setAuthToken(token: string): void {
    if (typeof window === "undefined") return;
    // Set it in axios defaults (token storage is handled by Zustand persist middleware)
    this.client.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  }

  // HTTP Methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get<ApiResponse<T>>(url, config);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  // File upload method
  async uploadFile(url: string, file: File, onUploadProgress?: (progress: number) => void): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await this.client.post<ApiResponse>(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onUploadProgress) {
          const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
          onUploadProgress(progress);
        }
      },
    });

    return response.data;
  }

  // SSE connection method
  createEventSource(endpoint: string): EventSource {
    const authHeader = this.client.defaults.headers.common["Authorization"];
    const url = new URL(endpoint, this.baseURL);
    
    if (authHeader && typeof authHeader === "string") {
      // Extract token from "Bearer TOKEN" format
      const token = authHeader.replace("Bearer ", "");
      url.searchParams.append("token", token);
    }

    return new EventSource(url.toString());
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient();
export default apiClient;