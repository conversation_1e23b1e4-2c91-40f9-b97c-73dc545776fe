// Export all API modules
export { authApi } from "./auth";
export { cardsApi } from "./cards";
export { pipelinesApi } from "./pipelines";
export { contactsApi } from "./contacts";
export { companiesApi } from "./companies";
export { fieldsApi } from "./fields";
export { usersApi } from "./users";
export { rolesApi } from "./roles";
export { activitiesApi } from "./activities";
export { apiClient } from "./client";

// Export unified api object for convenience
export const api = apiClient;

// Dashboard and analytics API
import { ApiResponse, DashboardStats } from "@/types";
import { apiClient } from "./client";

export const dashboardApi = {
  async getDashboardStats(dateFrom?: string, dateTo?: string): Promise<ApiResponse<DashboardStats>> {
    const params = new URLSearchParams();
    if (dateFrom) params.append("date_from", dateFrom);
    if (dateTo) params.append("date_to", dateTo);
    
    return apiClient.get<DashboardStats>(`/api/dashboard/stats?${params.toString()}`);
  },

  async getActivityFeed(limit = 20): Promise<ApiResponse<any[]>> {
    return apiClient.get(`/api/dashboard/activities?limit=${limit}`);
  },
};

// Tags API
import { Tag, EntityType } from "@/types";

export const tagsApi = {
  async getTags(entityType?: EntityType): Promise<ApiResponse<Tag[]>> {
    const params = entityType ? `?entity_type=${entityType}` : "";
    return apiClient.get<Tag[]>(`/api/tags${params}`);
  },

  async getTag(id: string): Promise<ApiResponse<Tag>> {
    return apiClient.get<Tag>(`/api/tags/${id}`);
  },

  async createTag(tagData: {
    name: string;
    color: string;
    entity_type: EntityType;
  }): Promise<ApiResponse<Tag>> {
    return apiClient.post<Tag>("/api/tags", tagData);
  },

  async updateTag(id: string, tagData: {
    name?: string;
    color?: string;
  }): Promise<ApiResponse<Tag>> {
    return apiClient.put<Tag>(`/api/tags/${id}`, tagData);
  },

  async deleteTag(id: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/tags/${id}`);
  },
};

// Files/Attachments API
export const filesApi = {
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<{
    filename: string;
    original_filename: string;
    file_size: number;
    mime_type: string;
    url: string;
  }>> {
    return apiClient.uploadFile("/api/files/upload", file, onProgress);
  },

  async deleteFile(filename: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/files/${filename}`);
  },

  getFileUrl(filename: string): string {
    return `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080"}/api/files/${filename}`;
  },
};

// Search API
export const searchApi = {
  async globalSearch(query: string, entityTypes?: EntityType[]): Promise<ApiResponse<{
    cards: any[];
    contacts: any[];
    companies: any[];
  }>> {
    const params = new URLSearchParams();
    params.append("q", query);
    
    if (entityTypes && entityTypes.length > 0) {
      entityTypes.forEach(type => params.append("types[]", type));
    }
    
    return apiClient.get(`/api/search?${params.toString()}`);
  },
};