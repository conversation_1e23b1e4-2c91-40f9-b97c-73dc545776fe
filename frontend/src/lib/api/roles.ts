import { ApiResponse, PaginatedResponse } from "@/types";
import { apiClient } from "./client";

export interface Permission {
  id: string;
  resource: string;
  action: string;
  description: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  color?: string;
  is_system: boolean;
  is_active: boolean;
  permissions?: Permission[];
  user_count?: number;
  created_at: string;
  updated_at: string;
}

export interface CreateRoleData {
  name: string;
  description: string;
  color?: string;
  permission_ids?: string[];
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  color?: string;
  is_active?: boolean;
  permission_ids?: string[];
}

export interface RoleFilters {
  search?: string;
  is_active?: boolean;
  is_system?: boolean;
  page?: number;
  limit?: number;
}

export const rolesApi = {
  // Get roles list with filters
  getRoles: async (filters?: RoleFilters): Promise<PaginatedResponse<Role>> => {
    const searchParams = new URLSearchParams();
    
    if (filters?.search) searchParams.append("search", filters.search);
    if (filters?.is_active !== undefined) searchParams.append("is_active", filters.is_active.toString());
    if (filters?.is_system !== undefined) searchParams.append("is_system", filters.is_system.toString());
    if (filters?.page) searchParams.append("page", filters.page.toString());
    if (filters?.limit) searchParams.append("limit", filters.limit.toString());

    const queryString = searchParams.toString();
    const url = queryString ? `/api/v1/roles?${queryString}` : "/api/v1/roles";
    
    return apiClient.get<Role[]>(url);
  },

  // Create a new role
  createRole: async (data: CreateRoleData): Promise<ApiResponse<Role>> => {
    return apiClient.post<Role>("/api/v1/roles", data);
  },

  // Get single role
  getRole: async (id: string): Promise<ApiResponse<Role>> => {
    return apiClient.get<Role>(`/api/v1/roles/${id}`);
  },

  // Update role
  updateRole: async (id: string, data: UpdateRoleData): Promise<ApiResponse<Role>> => {
    return apiClient.put<Role>(`/api/v1/roles/${id}`, data);
  },

  // Delete role
  deleteRole: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete<void>(`/api/v1/roles/${id}`);
  },

  // Duplicate role
  duplicateRole: async (id: string, newName: string): Promise<ApiResponse<Role>> => {
    return apiClient.post<Role>(`/api/v1/roles/${id}/duplicate`, { name: newName });
  },

  // Get all permissions
  getPermissions: async (): Promise<ApiResponse<Permission[]>> => {
    return apiClient.get<Permission[]>("/api/v1/permissions");
  },

  // Get permissions for a role
  getRolePermissions: async (id: string): Promise<ApiResponse<Permission[]>> => {
    return apiClient.get<Permission[]>(`/api/v1/roles/${id}/permissions`);
  },

  // Update role permissions (PUT - replaces all)
  updateRolePermissions: async (id: string, permissionIds: string[]): Promise<ApiResponse<void>> => {
    return apiClient.put<void>(`/api/v1/roles/${id}/permissions`, { permission_ids: permissionIds });
  },

  // Assign permissions to role (POST - replaces all)
  assignPermissionsToRole: async (id: string, permissionIds: string[]): Promise<ApiResponse<void>> => {
    return apiClient.post<void>(`/api/v1/roles/${id}/permissions`, { permission_ids: permissionIds });
  },

  // Get users with a specific role
  getRoleUsers: async (id: string): Promise<ApiResponse<any[]>> => {
    return apiClient.get<any[]>(`/api/v1/users/role/${id}`);
  },

  // Get role statistics
  getRoleStatistics: async (): Promise<ApiResponse<any>> => {
    return apiClient.get<any>("/api/v1/roles/statistics");
  },

  // Get permissions by resource
  getPermissionsByResource: async (resource: string): Promise<ApiResponse<Permission[]>> => {
    return apiClient.get<Permission[]>(`/api/v1/permissions/resource/${resource}`);
  },
};