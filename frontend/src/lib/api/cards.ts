import {
  Card,
  CreateCardRequest,
  UpdateCardRequest,
  MoveCardRequest,
  CardFilters,
  PaginatedResponse,
  ApiResponse,
  Activity,
  Attachment,
} from "@/types";
import { apiClient } from "./client";

export const cardsApi = {
  // Card CRUD operations
  async getCards(filters?: CardFilters): Promise<PaginatedResponse<Card>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(`${key}[]`, v.toString()));
          } else {
            params.append(key, value.toString());
          }
        }
      });
    }
    
    const response = await apiClient.get<Card[]>(`/api/v1/cards?${params.toString()}`);
    return response as PaginatedResponse<Card>;
  },

  async getCard(id: string): Promise<ApiResponse<Card>> {
    return apiClient.get<Card>(`/api/v1/cards/${id}`);
  },

  async createCard(cardData: CreateCardRequest): Promise<ApiResponse<Card>> {
    return apiClient.post<Card>("/api/v1/cards", cardData);
  },

  async updateCard(cardData: UpdateCardRequest): Promise<ApiResponse<Card>> {
    const { id, ...data } = cardData;
    return apiClient.put<Card>(`/api/v1/cards/${id}`, data);
  },

  async deleteCard(id: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/v1/cards/${id}`);
  },

  // Card positioning and movement
  async moveCard(moveData: MoveCardRequest): Promise<ApiResponse<Card>> {
    return apiClient.patch<Card>(`/api/v1/cards/${moveData.card_id}/move/${moveData.stage_id}`, {
      stage_id: moveData.stage_id,
      position: moveData.position,
    });
  },

  async duplicateCard(id: string): Promise<ApiResponse<Card>> {
    return apiClient.post<Card>(`/api/v1/cards/${id}/duplicate`);
  },

  // Card activities
  async getCardActivities(cardId: string): Promise<ApiResponse<Activity[]>> {
    return apiClient.get<Activity[]>(`/api/v1/cards/${cardId}/activities`);
  },

  async addCardActivity(cardId: string, activity: {
    type: string;
    title: string;
    description?: string;
    metadata?: Record<string, any>;
  }): Promise<ApiResponse<Activity>> {
    return apiClient.post<Activity>(`/api/v1/cards/${cardId}/activities`, activity);
  },

  // Card attachments
  async getCardAttachments(cardId: string): Promise<ApiResponse<Attachment[]>> {
    return apiClient.get<Attachment[]>(`/api/v1/cards/${cardId}/attachments`);
  },

  async uploadCardAttachment(
    cardId: string, 
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<Attachment>> {
    return apiClient.uploadFile(`/api/v1/cards/${cardId}/attachments`, file, onProgress);
  },

  async deleteCardAttachment(cardId: string, attachmentId: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/v1/cards/${cardId}/attachments/${attachmentId}`);
  },

  // Card tags
  async addCardTags(cardId: string, tagIds: string[]): Promise<ApiResponse<Card>> {
    return apiClient.post<Card>(`/api/v1/cards/${cardId}/tags`, { tag_ids: tagIds });
  },

  async removeCardTag(cardId: string, tagId: string): Promise<ApiResponse<Card>> {
    return apiClient.delete<Card>(`/api/v1/cards/${cardId}/tags/${tagId}`);
  },

  // Bulk operations
  async bulkUpdateCards(cardIds: string[], updates: Partial<UpdateCardRequest>): Promise<ApiResponse<Card[]>> {
    return apiClient.patch<Card[]>("/api/v1/cards/bulk", {
      card_ids: cardIds,
      updates,
    });
  },

  async bulkDeleteCards(cardIds: string[]): Promise<ApiResponse> {
    return apiClient.delete("/api/v1/cards/bulk", {
      data: { card_ids: cardIds },
    });
  },

  async bulkMoveCards(cardIds: string[], stageId: string): Promise<ApiResponse<Card[]>> {
    return apiClient.patch<Card[]>("/api/v1/cards/bulk/move", {
      card_ids: cardIds,
      stage_id: stageId,
    });
  },

  // Card statistics
  async getCardStats(filters?: CardFilters): Promise<ApiResponse<{
    total_count: number;
    total_value: number;
    average_value: number;
    conversion_rate: number;
    by_stage: Array<{ stage_name: string; count: number; value: number }>;
    by_user: Array<{ user_name: string; count: number; value: number }>;
  }>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.append(key, value.toString());
        }
      });
    }
    
    return apiClient.get(`/api/v1/cards/stats?${params.toString()}`);
  },

  // Export cards
  async exportCards(filters?: CardFilters, format: "csv" | "xlsx" = "csv"): Promise<Blob> {
    const params = new URLSearchParams();
    params.append("format", format);
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.append(key, value.toString());
        }
      });
    }

    const response = await apiClient.get(`/api/v1/cards/export?${params.toString()}`, {
      responseType: "blob",
    });
    
    return response.data as unknown as Blob;
  },
};