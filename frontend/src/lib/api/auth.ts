import { 
  LoginRequest, 
  LoginResponse, 
  CreateUserRequest, 
  User, 
  ApiResponse 
} from "@/types";
import { apiClient } from "./client";

export const authApi = {
  // Authentication
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return apiClient.post<LoginResponse>("/api/v1/auth/login", credentials);
  },

  async logout(): Promise<ApiResponse> {
    return apiClient.post("/api/v1/auth/logout");
  },

  async register(userData: CreateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.post<User>("/api/v1/auth/register", userData);
  },

  async getCurrentUser(): Promise<ApiResponse<User>> {
    return apiClient.get<User>("/api/v1/auth/profile");
  },

  async refreshToken(): Promise<ApiResponse<LoginResponse>> {
    return apiClient.post<LoginResponse>("/api/v1/auth/refresh");
  },

  async forgotPassword(email: string): Promise<ApiResponse> {
    return apiClient.post("/api/auth/forgot-password", { email });
  },

  async resetPassword(token: string, password: string): Promise<ApiResponse> {
    return apiClient.post("/api/auth/reset-password", { token, password });
  },

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    return apiClient.post("/api/auth/change-password", {
      current_password: currentPassword,
      new_password: newPassword,
    });
  },

  // User management (admin only)
  async getUsers(): Promise<ApiResponse<User[]>> {
    return apiClient.get<User[]>("/api/users");
  },

  async getUser(id: string): Promise<ApiResponse<User>> {
    return apiClient.get<User>(`/api/users/${id}`);
  },

  async createUser(userData: CreateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.post<User>("/api/users", userData);
  },

  async updateUser(id: string, userData: Partial<CreateUserRequest>): Promise<ApiResponse<User>> {
    return apiClient.put<User>(`/api/users/${id}`, userData);
  },

  async deleteUser(id: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/users/${id}`);
  },

  async toggleUserStatus(id: string): Promise<ApiResponse<User>> {
    return apiClient.patch<User>(`/api/users/${id}/toggle-status`);
  },
};