import {
  Pipeline,
  Stage,
  ApiResponse,
} from "@/types";
import { apiClient } from "./client";

export const pipelinesApi = {
  // Pipeline operations
  async getPipelines(): Promise<ApiResponse<Pipeline[]>> {
    return apiClient.get<Pipeline[]>("/api/v1/pipelines");
  },

  async getPipeline(id: string): Promise<ApiResponse<Pipeline>> {
    return apiClient.get<Pipeline>(`/api/v1/pipelines/${id}`);
  },

  async createPipeline(pipelineData: {
    name: string;
    description?: string;
  }): Promise<ApiResponse<Pipeline>> {
    return apiClient.post<Pipeline>("/api/v1/pipelines", pipelineData);
  },

  async updatePipeline(id: string, pipelineData: {
    name?: string;
    description?: string;
    is_active?: boolean;
  }): Promise<ApiResponse<Pipeline>> {
    return apiClient.put<Pipeline>(`/api/v1/pipelines/${id}`, pipelineData);
  },

  async deletePipeline(id: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/v1/pipelines/${id}`);
  },

  async duplicatePipeline(id: string, name: string): Promise<ApiResponse<Pipeline>> {
    return apiClient.post<Pipeline>(`/api/v1/pipelines/${id}/duplicate`, { name });
  },

  // Stage operations
  async getStages(pipelineId: string): Promise<ApiResponse<Stage[]>> {
    return apiClient.get<Stage[]>(`/api/v1/pipelines/${pipelineId}/stages`);
  },

  async getStage(pipelineId: string, stageId: string): Promise<ApiResponse<Stage>> {
    return apiClient.get<Stage>(`/api/v1/pipelines/${pipelineId}/stages/${stageId}`);
  },

  async createStage(pipelineId: string, stageData: {
    name: string;
    description?: string;
    color?: string;
  }): Promise<ApiResponse<Stage>> {
    return apiClient.post<Stage>(`/api/v1/pipelines/${pipelineId}/stages`, stageData);
  },

  async updateStage(pipelineId: string, stageId: string, stageData: {
    name?: string;
    description?: string;
    color?: string;
    is_closed?: boolean;
  }): Promise<ApiResponse<Stage>> {
    return apiClient.put<Stage>(`/api/v1/pipelines/${pipelineId}/stages/${stageId}`, stageData);
  },

  async deleteStage(pipelineId: string, stageId: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/v1/pipelines/${pipelineId}/stages/${stageId}`);
  },

  async reorderStages(pipelineId: string, stageOrders: Array<{
    stage_id: string;
    position: number;
  }>): Promise<ApiResponse<Stage[]>> {
    return apiClient.patch<Stage[]>(`/api/v1/pipelines/${pipelineId}/stages/reorder`, {
      stages: stageOrders,
    });
  },

  // Pipeline analytics
  async getPipelineStats(pipelineId: string, dateFrom?: string, dateTo?: string): Promise<ApiResponse<{
    total_cards: number;
    total_value: number;
    conversion_rate: number;
    average_deal_size: number;
    average_sales_cycle: number;
    stage_conversion_rates: Array<{
      stage_name: string;
      conversion_rate: number;
      average_time: number;
    }>;
    velocity_chart: Array<{
      date: string;
      cards_moved: number;
      value_moved: number;
    }>;
  }>> {
    const params = new URLSearchParams();
    if (dateFrom) params.append("date_from", dateFrom);
    if (dateTo) params.append("date_to", dateTo);
    
    return apiClient.get(`/api/v1/pipelines/${pipelineId}/stats?${params.toString()}`);
  },

  async getFunnelData(pipelineId: string): Promise<ApiResponse<Array<{
    stage_name: string;
    card_count: number;
    total_value: number;
    conversion_rate: number;
  }>>> {
    return apiClient.get(`/api/v1/pipelines/${pipelineId}/funnel`);
  },
};