import {
  Contact,
  CreateContactRequest,
  SearchParams,
  PaginatedResponse,
  ApiResponse,
  Activity,
} from "@/types";
import { apiClient } from "./client";

export const contactsApi = {
  // Contact CRUD operations
  async getContacts(params?: SearchParams): Promise<PaginatedResponse<Contact>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const response = await apiClient.get<any>(`/api/v1/contacts?${searchParams.toString()}`);
    
    // Transform the backend response to match frontend expectations
    // Backend returns { data: [], meta: { total, per_page, current_page, last_page } }
    // Frontend expects { data: [], pagination: { total, limit, page, total_pages } }
    if (response.meta) {
      return {
        ...response,
        pagination: {
          total: response.meta.total || 0,
          limit: response.meta.per_page || 20,
          page: response.meta.current_page || 1,
          total_pages: response.meta.last_page || 1,
        }
      } as PaginatedResponse<Contact>;
    }
    
    return response as PaginatedResponse<Contact>;
  },

  async getContact(id: string): Promise<ApiResponse<Contact>> {
    return apiClient.get<Contact>(`/api/v1/contacts/${id}`);
  },

  async createContact(contactData: CreateContactRequest): Promise<ApiResponse<Contact>> {
    return apiClient.post<Contact>("/api/v1/contacts", contactData);
  },

  async updateContact(id: string, contactData: Partial<CreateContactRequest>): Promise<ApiResponse<Contact>> {
    return apiClient.put<Contact>(`/api/v1/contacts/${id}`, contactData);
  },

  async deleteContact(id: string): Promise<ApiResponse> {
    return apiClient.delete(`/api/v1/contacts/${id}`);
  },

  // Contact activities
  async getContactActivities(contactId: string): Promise<ApiResponse<Activity[]>> {
    return apiClient.get<Activity[]>(`/api/v1/contacts/${contactId}/activities`);
  },

  async addContactActivity(contactId: string, activity: {
    type: string;
    title: string;
    description?: string;
    metadata?: Record<string, any>;
  }): Promise<ApiResponse<Activity>> {
    return apiClient.post<Activity>(`/api/v1/contacts/${contactId}/activities`, activity);
  },

  // Contact tags
  async addContactTags(contactId: string, tagIds: string[]): Promise<ApiResponse<Contact>> {
    return apiClient.post<Contact>(`/api/v1/contacts/${contactId}/tags`, { tag_ids: tagIds });
  },

  async removeContactTag(contactId: string, tagId: string): Promise<ApiResponse<Contact>> {
    return apiClient.delete<Contact>(`/api/v1/contacts/${contactId}/tags/${tagId}`);
  },

  // Contact relationships
  async getContactCards(contactId: string): Promise<ApiResponse<any[]>> {
    return apiClient.get(`/api/v1/contacts/${contactId}/cards`);
  },

  // Bulk operations
  async bulkUpdateContacts(contactIds: string[], updates: Partial<CreateContactRequest>): Promise<ApiResponse<Contact[]>> {
    return apiClient.patch<Contact[]>("/api/v1/contacts/bulk", {
      contact_ids: contactIds,
      updates,
    });
  },

  async bulkDeleteContacts(contactIds: string[]): Promise<ApiResponse> {
    return apiClient.delete("/api/v1/contacts/bulk", {
      data: { contact_ids: contactIds },
    });
  },

  // Import/Export
  async importContacts(file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<{
    imported: number;
    failed: number;
    errors: Array<{ row: number; errors: string[] }>;
  }>> {
    return apiClient.uploadFile("/api/v1/contacts/import", file, onProgress);
  },

  async exportContacts(params?: SearchParams, format: "csv" | "xlsx" = "csv"): Promise<Blob> {
    const searchParams = new URLSearchParams();
    searchParams.append("format", format);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await apiClient.get(`/api/v1/contacts/export?${searchParams.toString()}`, {
      responseType: "blob",
    });
    
    return response.data as unknown as Blob;
  },
};