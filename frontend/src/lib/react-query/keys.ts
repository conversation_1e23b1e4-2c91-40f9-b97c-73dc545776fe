// Query key factory for consistent key management
export const queryKeys = {
  // Auth
  auth: {
    currentUser: ["auth", "current-user"] as const,
    users: ["auth", "users"] as const,
    user: (id: string) => ["auth", "user", id] as const,
  },

  // Pipelines
  pipelines: {
    all: ["pipelines"] as const,
    pipeline: (id: string) => ["pipelines", id] as const,
    stages: (pipelineId: string) => ["pipelines", pipelineId, "stages"] as const,
    stats: (pipelineId: string, dateFrom?: string, dateTo?: string) => 
      ["pipelines", pipelineId, "stats", { dateFrom, dateTo }] as const,
    funnel: (pipelineId: string) => ["pipelines", pipelineId, "funnel"] as const,
  },

  // Cards
  cards: {
    all: (filters?: any) => ["cards", { filters }] as const,
    card: (id: string) => ["cards", id] as const,
    activities: (cardId: string) => ["cards", cardId, "activities"] as const,
    attachments: (cardId: string) => ["cards", cardId, "attachments"] as const,
    stats: (filters?: any) => ["cards", "stats", { filters }] as const,
  },

  // Contacts
  contacts: {
    all: (params?: any) => ["contacts", { params }] as const,
    contact: (id: string) => ["contacts", id] as const,
    activities: (contactId: string) => ["contacts", contactId, "activities"] as const,
    cards: (contactId: string) => ["contacts", contactId, "cards"] as const,
  },

  // Companies
  companies: {
    all: (params?: any) => ["companies", { params }] as const,
    company: (id: string) => ["companies", id] as const,
    activities: (companyId: string) => ["companies", companyId, "activities"] as const,
    contacts: (companyId: string) => ["companies", companyId, "contacts"] as const,
    cards: (companyId: string) => ["companies", companyId, "cards"] as const,
  },

  // Field Definitions
  fieldDefinitions: {
    all: (entityType?: string) => ["field-definitions", { entityType }] as const,
    fieldDefinition: (id: string) => ["field-definitions", id] as const,
    templates: (entityType: string) => ["field-definitions", "templates", entityType] as const,
  },

  // Tags
  tags: {
    all: (entityType?: string) => ["tags", { entityType }] as const,
    tag: (id: string) => ["tags", id] as const,
  },

  // Dashboard
  dashboard: {
    stats: (dateFrom?: string, dateTo?: string) => 
      ["dashboard", "stats", { dateFrom, dateTo }] as const,
    activities: (limit?: number) => ["dashboard", "activities", { limit }] as const,
  },

  // Search
  search: {
    global: (query: string, entityTypes?: string[]) => 
      ["search", "global", { query, entityTypes }] as const,
  },
} as const;