import { useEffect, useState, useCallback, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { sseClient, SSEEventHandler } from "./client";
import { SSEEvent, CardUpdatedEvent, CardMovedEvent } from "@/types";
import { queryKeys } from "@/lib/react-query/keys";
import { usePipelineStore, useNotifications } from "@/stores";

// Hook to manage SSE connection
export const useSSEConnection = () => {
  const [connectionState, setConnectionState] = useState<
    "connecting" | "connected" | "disconnected" | "error"
  >("disconnected");

  useEffect(() => {
    const handleConnectionChange = (data: { status: string }) => {
      setConnectionState(data.status as any);
    };

    sseClient.on("connection", handleConnectionChange);
    
    // Temporarily disable auto-connect to fix authentication issues
    // sseClient.connect();
    
    // Update initial state
    setConnectionState("disconnected");

    return () => {
      sseClient.off("connection", handleConnectionChange);
      sseClient.disconnect();
    };
  }, []);

  const reconnect = useCallback(() => {
    sseClient.disconnect();
    setTimeout(() => sseClient.connect(), 1000);
  }, []);

  return {
    connectionState,
    isConnected: connectionState === "connected",
    reconnect,
  };
};

// Hook for listening to specific SSE events
export const useSSEEvent = (
  eventType: string,
  handler: SSEEventHandler,
  deps: any[] = []
) => {
  const handlerRef = useRef(handler);
  
  // Update handler ref when dependencies change
  useEffect(() => {
    handlerRef.current = handler;
  }, deps);

  useEffect(() => {
    const stableHandler = (event: SSEEvent) => {
      handlerRef.current(event);
    };

    sseClient.on(eventType, stableHandler);
    
    return () => {
      sseClient.off(eventType, stableHandler);
    };
  }, [eventType]);
};

// Hook for real-time pipeline updates
export const usePipelineSSE = (pipelineId?: string) => {
  const queryClient = useQueryClient();
  const pipelineStore = usePipelineStore();
  const notifications = useNotifications();

  // Handle card updates
  useSSEEvent(
    "card_updated",
    useCallback((event: SSEEvent) => {
      const cardEvent = event as CardUpdatedEvent;
      const { card, changes } = cardEvent.data;

      // Update pipeline store
      pipelineStore.updateCard(card);

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.cards.card(card.id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.cards.all() });

      // Show notification for significant changes
      const significantChanges = ["stage_id", "value", "assigned_user_id"];
      const hasSignificantChange = Object.keys(changes).some(key => 
        significantChanges.includes(key)
      );

      if (hasSignificantChange) {
        notifications.info(
          "Card Updated",
          `${card.title} has been updated`,
          3000
        );
      }
    }, [queryClient, pipelineStore, notifications]),
    [queryClient, pipelineStore, notifications]
  );

  // Handle card movements
  useSSEEvent(
    "card_moved",
    useCallback((event: SSEEvent) => {
      const cardEvent = event as CardMovedEvent;
      const { card, from_stage_id, to_stage_id, position } = cardEvent.data;

      // Update pipeline store
      pipelineStore.moveCardOptimistic(card.id, to_stage_id, position);

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.cards.all() });

      // Show notification
      notifications.info(
        "Card Moved",
        `${card.title} has been moved`,
        3000
      );
    }, [queryClient, pipelineStore, notifications]),
    [queryClient, pipelineStore, notifications]
  );

  // Handle new cards
  useSSEEvent(
    "card_created",
    useCallback((event: SSEEvent) => {
      const { card } = event.data;
      
      // Only add if it's in the current pipeline
      if (!pipelineId || card.pipeline_id === pipelineId) {
        pipelineStore.addCard(card);
        queryClient.invalidateQueries({ queryKey: queryKeys.cards.all() });
        
        notifications.success(
          "New Card Created",
          `${card.title} has been created`,
          3000
        );
      }
    }, [pipelineId, queryClient, pipelineStore, notifications]),
    [pipelineId, queryClient, pipelineStore, notifications]
  );

  // Handle card deletions
  useSSEEvent(
    "card_deleted",
    useCallback((event: SSEEvent) => {
      const { card_id } = event.data;
      
      pipelineStore.removeCard(card_id);
      queryClient.invalidateQueries({ queryKey: queryKeys.cards.all() });
      
      notifications.info(
        "Card Deleted",
        "A card has been deleted",
        3000
      );
    }, [queryClient, pipelineStore, notifications]),
    [queryClient, pipelineStore, notifications]
  );
};

// Hook for contact/company updates
export const useEntitySSE = (entityType: "contacts" | "companies") => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  const entityCreatedEvent = `${entityType.slice(0, -1)}_created`;
  const entityUpdatedEvent = `${entityType.slice(0, -1)}_updated`;

  useSSEEvent(
    entityCreatedEvent,
    useCallback((event: SSEEvent) => {
      const entity = event.data[entityType.slice(0, -1)];
      
      queryClient.invalidateQueries({ 
        queryKey: entityType === "contacts" 
          ? queryKeys.contacts.all() 
          : queryKeys.companies.all() 
      });
      
      notifications.success(
        `New ${entityType.slice(0, -1).charAt(0).toUpperCase() + entityType.slice(1, -1)} Created`,
        `${entity.name || `${entity.first_name} ${entity.last_name}`} has been created`,
        3000
      );
    }, [queryClient, notifications, entityType]),
    [queryClient, notifications, entityType]
  );

  useSSEEvent(
    entityUpdatedEvent,
    useCallback((event: SSEEvent) => {
      const entity = event.data[entityType.slice(0, -1)];
      
      queryClient.invalidateQueries({ 
        queryKey: entityType === "contacts" 
          ? queryKeys.contacts.contact(entity.id)
          : queryKeys.companies.company(entity.id)
      });
      
      queryClient.invalidateQueries({ 
        queryKey: entityType === "contacts" 
          ? queryKeys.contacts.all() 
          : queryKeys.companies.all() 
      });
    }, [queryClient, entityType]),
    [queryClient, entityType]
  );
};

// Hook for user activity notifications
export const useActivitySSE = () => {
  const notifications = useNotifications();

  useSSEEvent(
    "user_activity",
    useCallback((event: SSEEvent) => {
      const { activity_type, user_name, entity_name, message } = event.data;
      
      // Show notification for relevant activities
      const notifiableActivities = ["mention", "assignment", "comment"];
      
      if (notifiableActivities.includes(activity_type)) {
        notifications.info(
          "New Activity",
          message || `${user_name} performed an action on ${entity_name}`,
          5000
        );
      }
    }, [notifications]),
    [notifications]
  );
};