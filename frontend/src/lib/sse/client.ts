import { apiClient } from "@/lib/api/client";
import { SSEEvent, CardUpdatedEvent, CardMovedEvent } from "@/types";

export type SSEEventHandler = (event: SSEEvent) => void;

class SSEClient {
  private eventSource: EventSource | null = null;
  private handlers: Map<string, SSEEventHandler[]> = new Map();
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private isConnecting = false;
  private isManualClose = false;

  constructor() {
    // Clean up on page unload
    if (typeof window !== "undefined") {
      window.addEventListener("beforeunload", () => this.disconnect());
    }
  }

  connect(): void {
    if (this.eventSource || this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    this.isManualClose = false;

    try {
      this.eventSource = apiClient.createEventSource("/api/v1/realtime/events");

      this.eventSource.onopen = () => {
        console.log("SSE connection opened");
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        
        // Emit connection event
        this.emit("connection", { status: "connected" });
      };

      this.eventSource.onmessage = (event) => {
        try {
          const data: SSEEvent = JSON.parse(event.data);
          this.handleEvent(data);
        } catch (error) {
          console.error("Failed to parse SSE event:", error);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error("SSE connection error:", error);
        this.isConnecting = false;
        
        if (!this.isManualClose) {
          this.handleReconnect();
        }
      };

      // Set up specific event listeners
      this.setupEventListeners();

    } catch (error) {
      console.error("Failed to create SSE connection:", error);
      this.isConnecting = false;
      
      if (!this.isManualClose) {
        this.handleReconnect();
      }
    }
  }

  disconnect(): void {
    this.isManualClose = true;
    
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }

    this.handlers.clear();
    this.reconnectAttempts = 0;
    
    // Emit disconnection event
    this.emit("connection", { status: "disconnected" });
  }

  private setupEventListeners(): void {
    if (!this.eventSource) return;

    // Listen for specific server-sent events
    this.eventSource.addEventListener("card_updated", (event) => {
      try {
        const data: CardUpdatedEvent = {
          id: "",
          event: "card_updated",
          data: JSON.parse(event.data),
          timestamp: new Date().toISOString(),
        };
        this.handleEvent(data);
      } catch (error) {
        console.error("Failed to parse card_updated event:", error);
      }
    });

    this.eventSource.addEventListener("card_moved", (event) => {
      try {
        const data: CardMovedEvent = {
          id: "",
          event: "card_moved",
          data: JSON.parse(event.data),
          timestamp: new Date().toISOString(),
        };
        this.handleEvent(data);
      } catch (error) {
        console.error("Failed to parse card_moved event:", error);
      }
    });

    // Add more event listeners as needed
    const eventTypes = [
      "card_created",
      "card_deleted",
      "contact_created",
      "contact_updated",
      "company_created",
      "company_updated",
      "user_activity",
    ];

    eventTypes.forEach((eventType) => {
      this.eventSource!.addEventListener(eventType, (event) => {
        try {
          const data: SSEEvent = {
            id: "",
            event: eventType,
            data: JSON.parse(event.data),
            timestamp: new Date().toISOString(),
          };
          this.handleEvent(data);
        } catch (error) {
          console.error(`Failed to parse ${eventType} event:`, error);
        }
      });
    });
  }

  private handleEvent(event: SSEEvent): void {
    // Emit to general listeners
    this.emit("event", event);
    
    // Emit to specific event type listeners
    this.emit(event.event, event);
  }

  private handleReconnect(): void {
    if (this.isManualClose || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log("Max reconnection attempts reached or manually closed");
      return;
    }

    this.reconnectAttempts++;
    
    console.log(`Attempting to reconnect SSE (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay}ms`);
    
    setTimeout(() => {
      if (!this.isManualClose) {
        this.eventSource = null;
        this.connect();
      }
    }, this.reconnectDelay);

    // Exponential backoff with jitter
    this.reconnectDelay = Math.min(
      this.reconnectDelay * 2 + Math.random() * 1000,
      this.maxReconnectDelay
    );
  }

  // Event handler management
  on(eventType: string, handler: SSEEventHandler): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    this.handlers.get(eventType)!.push(handler);
  }

  off(eventType: string, handler: SSEEventHandler): void {
    const handlers = this.handlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emit(eventType: string, data: any): void {
    const handlers = this.handlers.get(eventType);
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in SSE event handler for ${eventType}:`, error);
        }
      });
    }
  }

  // Connection status
  getConnectionState(): "connecting" | "connected" | "disconnected" | "error" {
    if (this.isConnecting) return "connecting";
    if (!this.eventSource) return "disconnected";
    
    switch (this.eventSource.readyState) {
      case EventSource.CONNECTING:
        return "connecting";
      case EventSource.OPEN:
        return "connected";
      case EventSource.CLOSED:
        return "disconnected";
      default:
        return "error";
    }
  }

  isConnected(): boolean {
    return this.getConnectionState() === "connected";
  }
}

// Export singleton instance
export const sseClient = new SSEClient();
export default sseClient;