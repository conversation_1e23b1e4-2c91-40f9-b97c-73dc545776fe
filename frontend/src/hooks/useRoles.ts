"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import { rolesApi, Role, Permission, CreateRoleData, UpdateRoleData, RoleFilters } from "@/lib/api/roles";

export function useRoles(filters?: RoleFilters) {
  const queryClient = useQueryClient();
  const [currentFilters, setCurrentFilters] = useState<RoleFilters>(filters || {});

  // Fetch roles with filters
  const {
    data: rolesData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["roles", currentFilters],
    queryFn: () => rolesApi.getRoles(currentFilters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch all permissions
  const {
    data: permissionsData,
    isLoading: permissionsLoading,
  } = useQuery({
    queryKey: ["permissions"],
    queryFn: () => rolesApi.getPermissions(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Create role mutation
  const createRoleMutation = useMutation({
    mutationFn: (data: CreateRoleData) => rolesApi.createRole(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast({
        title: "Успешно",
        description: "Роль успешно создана",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Ошибка",
        description: error.response?.data?.message || "Не удалось создать роль",
        variant: "destructive",
      });
    },
  });

  // Update role mutation
  const updateRoleMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateRoleData }) =>
      rolesApi.updateRole(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast({
        title: "Успешно",
        description: "Роль успешно обновлена",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Ошибка",
        description: error.response?.data?.message || "Не удалось обновить роль",
        variant: "destructive",
      });
    },
  });

  // Delete role mutation
  const deleteRoleMutation = useMutation({
    mutationFn: (id: string) => rolesApi.deleteRole(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast({
        title: "Успешно",
        description: "Роль успешно удалена",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Ошибка",
        description: error.response?.data?.message || "Не удалось удалить роль",
        variant: "destructive",
      });
    },
  });

  // Duplicate role mutation
  const duplicateRoleMutation = useMutation({
    mutationFn: ({ id, newName }: { id: string; newName: string }) =>
      rolesApi.duplicateRole(id, newName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast({
        title: "Успешно",
        description: "Роль успешно скопирована",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Ошибка",
        description: error.response?.data?.message || "Не удалось скопировать роль",
        variant: "destructive",
      });
    },
  });

  // Update role permissions mutation
  const updateRolePermissionsMutation = useMutation({
    mutationFn: ({ id, permissionIds }: { id: string; permissionIds: string[] }) =>
      rolesApi.assignPermissionsToRole(id, permissionIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast({
        title: "Успешно",
        description: "Права роли успешно обновлены",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Ошибка",
        description: error.response?.data?.message || "Не удалось обновить права роли",
        variant: "destructive",
      });
    },
  });

  // Filter functions
  const searchRoles = (search: string) => {
    setCurrentFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const filterByStatus = (is_active: boolean | undefined) => {
    setCurrentFilters(prev => ({ ...prev, is_active, page: 1 }));
  };

  const filterBySystemRole = (is_system: boolean | undefined) => {
    setCurrentFilters(prev => ({ ...prev, is_system, page: 1 }));
  };

  const setPage = (page: number) => {
    setCurrentFilters(prev => ({ ...prev, page }));
  };

  const setLimit = (limit: number) => {
    setCurrentFilters(prev => ({ ...prev, limit, page: 1 }));
  };

  // Reset filters
  const resetFilters = () => {
    setCurrentFilters({});
  };

  // Helper functions
  const getRoleById = (id: string) => {
    return rolesData?.data?.find(role => role.id === id);
  };

  const getPermissionsByResource = (resource: string) => {
    return permissionsData?.data?.filter(perm => perm.resource === resource) || [];
  };

  return {
    // Data
    roles: rolesData?.data || [],
    meta: rolesData?.meta || { total: 0, page: 1, limit: 20, pages: 0 },
    permissions: permissionsData?.data || [],
    
    // Loading states
    isLoading,
    permissionsLoading,
    
    // Error states
    error,
    
    // Filters
    currentFilters,
    searchRoles,
    filterByStatus,
    filterBySystemRole,
    setPage,
    setLimit,
    resetFilters,
    
    // Actions
    createRole: createRoleMutation.mutate,
    updateRole: updateRoleMutation.mutate,
    deleteRole: deleteRoleMutation.mutate,
    duplicateRole: duplicateRoleMutation.mutate,
    updateRolePermissions: updateRolePermissionsMutation.mutate,
    
    // Action states
    isCreating: createRoleMutation.isPending,
    isUpdating: updateRoleMutation.isPending,
    isDeleting: deleteRoleMutation.isPending,
    isDuplicating: duplicateRoleMutation.isPending,
    isUpdatingPermissions: updateRolePermissionsMutation.isPending,
    
    // Utilities
    refetch,
    getRoleById,
    getPermissionsByResource,
  };
}

// Hook for getting a single role
export function useRole(id: string) {
  const queryClient = useQueryClient();

  const {
    data: roleData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["role", id],
    queryFn: () => rolesApi.getRole(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });

  // Get role permissions
  const {
    data: rolePermissionsData,
    isLoading: permissionsLoading,
  } = useQuery({
    queryKey: ["role-permissions", id],
    queryFn: () => rolesApi.getRolePermissions(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });

  return {
    role: roleData?.data,
    rolePermissions: rolePermissionsData?.data || [],
    isLoading,
    permissionsLoading,
    error,
    refetch,
  };
}

// Hook for getting permissions by resource
export function usePermissionsByResource(resource?: string) {
  const {
    data: permissionsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["permissions-by-resource", resource],
    queryFn: () => resource ? rolesApi.getPermissionsByResource(resource) : rolesApi.getPermissions(),
    enabled: !!resource,
    staleTime: 10 * 60 * 1000,
  });

  return {
    permissions: permissionsData?.data || [],
    isLoading,
    error,
  };
}