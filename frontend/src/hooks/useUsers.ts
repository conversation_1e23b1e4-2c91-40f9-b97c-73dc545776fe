import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { usersApi, rolesApi, CreateUserData, UpdateUserData, UserFilters, BulkAction } from "@/lib/api";
import { User } from "@/types";

export function useUsers(filters?: UserFilters) {
  const [currentFilters, setCurrentFilters] = useState<UserFilters>(filters || {
    page: 1,
    limit: 20,
  });

  const queryClient = useQueryClient();

  // Fetch users list
  const {
    data: usersData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["users", currentFilters],
    queryFn: () => usersApi.getUsers(currentFilters),
  });

  // Fetch roles for dropdown
  const { data: rolesData } = useQuery({
    queryKey: ["roles"],
    queryFn: () => rolesApi.getRoles({ limit: 100 }),
  });

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: (data: CreateUserData) => usersApi.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success("Пользователь успешно создан");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Ошибка при создании пользователя");
    },
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserData }) => 
      usersApi.updateUser(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success("Пользователь успешно обновлен");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Ошибка при обновлении пользователя");
    },
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: (id: string) => usersApi.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success("Пользователь успешно удален");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Ошибка при удалении пользователя");
    },
  });

  // Toggle user status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: (id: string) => usersApi.toggleUserStatus(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success("Статус пользователя изменен");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Ошибка при изменении статуса");
    },
  });

  // Assign role mutation
  const assignRoleMutation = useMutation({
    mutationFn: ({ userId, roleId }: { userId: string; roleId: string }) => 
      usersApi.assignRole(userId, roleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success("Роль успешно назначена");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Ошибка при назначении роли");
    },
  });

  // Update password mutation
  const updatePasswordMutation = useMutation({
    mutationFn: ({ id, password }: { id: string; password: string }) => 
      usersApi.updatePassword(id, password),
    onSuccess: () => {
      toast.success("Пароль успешно обновлен");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Ошибка при обновлении пароля");
    },
  });

  // Bulk operations mutation
  const bulkOperationsMutation = useMutation({
    mutationFn: (data: BulkAction) => usersApi.bulkOperations(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      
      const messages: Record<string, string> = {
        delete: "Пользователи удалены",
        activate: "Пользователи активированы",
        deactivate: "Пользователи деактивированы",
        assign_role: "Роль назначена пользователям",
      };
      
      toast.success(messages[variables.action] || "Операция выполнена");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || "Ошибка при выполнении операции");
    },
  });

  // Helper functions
  const updateFilters = (newFilters: Partial<UserFilters>) => {
    setCurrentFilters(prev => ({ ...prev, ...newFilters }));
  };

  const resetFilters = () => {
    setCurrentFilters({ page: 1, limit: 20 });
  };

  const goToPage = (page: number) => {
    updateFilters({ page });
  };

  const changePageSize = (limit: number) => {
    updateFilters({ limit, page: 1 });
  };

  const searchUsers = (search: string) => {
    updateFilters({ search, page: 1 });
  };

  const filterByRole = (roleId?: string) => {
    updateFilters({ role_id: roleId, page: 1 });
  };

  const filterByStatus = (isActive?: boolean) => {
    updateFilters({ is_active: isActive, page: 1 });
  };

  const filterByDepartment = (department?: string) => {
    updateFilters({ department, page: 1 });
  };

  return {
    // Data
    users: usersData?.data || [],
    meta: usersData?.meta || {
      total: 0,
      page: currentFilters.page || 1,
      limit: currentFilters.limit || 20,
      totalPages: 0,
    },
    roles: rolesData?.data || [],
    
    // Loading states
    isLoading,
    isCreating: createUserMutation.isPending,
    isUpdating: updateUserMutation.isPending,
    isDeleting: deleteUserMutation.isPending,
    
    // Error states
    error,
    
    // Mutations
    createUser: createUserMutation.mutate,
    updateUser: updateUserMutation.mutate,
    deleteUser: deleteUserMutation.mutate,
    toggleStatus: toggleStatusMutation.mutate,
    assignRole: assignRoleMutation.mutate,
    updatePassword: updatePasswordMutation.mutate,
    bulkOperations: bulkOperationsMutation.mutate,
    
    // Filter functions
    updateFilters,
    resetFilters,
    goToPage,
    changePageSize,
    searchUsers,
    filterByRole,
    filterByStatus,
    filterByDepartment,
    
    // Other
    refetch,
    currentFilters,
  };
}

// Hook for single user
export function useUser(id: string) {
  const queryClient = useQueryClient();

  const {
    data: userData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["user", id],
    queryFn: () => usersApi.getUser(id),
    enabled: !!id,
  });

  const {
    data: permissionsData,
    isLoading: isLoadingPermissions,
  } = useQuery({
    queryKey: ["user-permissions", id],
    queryFn: () => usersApi.getUserPermissions(id),
    enabled: !!id,
  });

  return {
    user: userData?.data,
    permissions: permissionsData?.data || [],
    isLoading: isLoading || isLoadingPermissions,
    error,
  };
}

// Hook for user statistics
export function useUserStatistics() {
  const {
    data: statsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["user-statistics"],
    queryFn: () => usersApi.getStatistics(),
  });

  return {
    statistics: statsData?.data,
    isLoading,
    error,
  };
}