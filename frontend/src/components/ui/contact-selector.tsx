"use client"

import * as React from "react"
import { Check, ChevronsUpDown, User, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  <PERSON>overContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Contact } from "@/types"
import { contactsApi } from "@/lib/api/contacts"

interface ContactSelectorProps {
  value?: string
  onValueChange: (value: string | undefined) => void
  placeholder?: string
  className?: string
}

export function ContactSelector({
  value,
  onValueChange,
  placeholder = "Select contact...",
  className,
}: ContactSelectorProps) {
  const [open, setOpen] = React.useState(false)
  const [contacts, setContacts] = React.useState<Contact[]>([])
  const [loading, setLoading] = React.useState(false)
  const [query, setQuery] = React.useState("")

  const selectedContact = contacts.find((contact) => contact.id === value)

  const loadContacts = React.useCallback(async (searchQuery: string = "") => {
    try {
      setLoading(true)
      const response = await contactsApi.getContacts({ 
        q: searchQuery,
        limit: 50 
      })
      // Handle both response formats
      if (response) {
        if (response.success && response.data) {
          setContacts(response.data)
        } else if (Array.isArray(response)) {
          setContacts(response)
        } else if (response.data && Array.isArray(response.data)) {
          setContacts(response.data)
        }
      }
    } catch (error) {
      console.error("Failed to load contacts:", error)
    } finally {
      setLoading(false)
    }
  }, [])

  React.useEffect(() => {
    if (open) {
      loadContacts(query)
    }
  }, [open, query, loadContacts])

  const handleSelect = (contactId: string) => {
    if (contactId === value) {
      onValueChange(undefined)
    } else {
      onValueChange(contactId)
    }
    setOpen(false)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onValueChange(undefined)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
        >
          {selectedContact ? (
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <Avatar className="h-5 w-5">
                <AvatarFallback className="text-xs">
                  {selectedContact.first_name?.[0]}{selectedContact.last_name?.[0]}
                </AvatarFallback>
              </Avatar>
              <span className="truncate">
                {selectedContact.first_name} {selectedContact.last_name}
              </span>
              {selectedContact.company?.name && (
                <Badge variant="secondary" className="text-xs">
                  {selectedContact.company.name}
                </Badge>
              )}
              <X
                className="h-4 w-4 shrink-0 opacity-50 hover:opacity-100"
                onClick={handleClear}
              />
            </div>
          ) : (
            <>
              <div className="flex items-center gap-2 text-muted-foreground">
                <User className="h-4 w-4" />
                {placeholder}
              </div>
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput 
            placeholder="Search contacts..." 
            value={query}
            onValueChange={setQuery}
          />
          <CommandList>
            <CommandEmpty>
              {loading ? "Loading..." : "No contacts found."}
            </CommandEmpty>
            <CommandGroup>
              {contacts.map((contact) => (
                <CommandItem
                  key={contact.id}
                  value={contact.id}
                  onSelect={handleSelect}
                  className="flex items-center gap-2 py-2"
                >
                  <Avatar className="h-6 w-6">
                    <AvatarFallback className="text-xs">
                      {contact.first_name?.[0]}{contact.last_name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">
                      {contact.first_name} {contact.last_name}
                    </div>
                    <div className="text-sm text-muted-foreground truncate">
                      {contact.email}
                      {contact.company?.name && (
                        <span className="ml-2">• {contact.company.name}</span>
                      )}
                    </div>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      contact.id === value ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}