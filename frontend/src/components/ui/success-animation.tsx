"use client";

import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle2, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, PartyPopper } from "lucide-react";
import { useEffect, useState } from "react";
import confetti from "canvas-confetti";

interface SuccessAnimationProps {
  show: boolean;
  message?: string;
  type?: "success" | "achievement" | "milestone" | "celebration";
  onComplete?: () => void;
}

export function SuccessAnimation({ 
  show, 
  message = "Success!", 
  type = "success",
  onComplete 
}: SuccessAnimationProps) {
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number }>>([]);

  useEffect(() => {
    if (show && type === "celebration") {
      // Trigger confetti for celebrations
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
        colors: ["#3B82F6", "#86EFAC", "#EAB308", "#EF4444", "#A855F7"],
      });
    }

    if (show) {
      // Generate sparkle particles
      const newParticles = Array.from({ length: 12 }, (_, i) => ({
        id: i,
        x: Math.random() * 200 - 100,
        y: Math.random() * 200 - 100,
      }));
      setParticles(newParticles);

      // Auto-hide after animation
      const timer = setTimeout(() => {
        onComplete?.();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [show, type, onComplete]);

  const icons = {
    success: CheckCircle2,
    achievement: Trophy,
    milestone: Star,
    celebration: PartyPopper,
  };

  const Icon = icons[type];

  const colors = {
    success: "from-green-500 to-emerald-600",
    achievement: "from-yellow-500 to-orange-600",
    milestone: "from-purple-500 to-pink-600",
    celebration: "from-blue-500 to-purple-600",
  };

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ 
            type: "spring",
            stiffness: 300,
            damping: 20,
          }}
          className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none"
        >
          <div className="relative">
            {/* Sparkle particles */}
            {particles.map((particle) => (
              <motion.div
                key={particle.id}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ 
                  opacity: [0, 1, 0],
                  scale: [0, 1.5, 0],
                  x: particle.x,
                  y: particle.y,
                }}
                transition={{
                  duration: 1.5,
                  delay: particle.id * 0.05,
                  ease: "easeOut",
                }}
                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
              >
                <Sparkles className="h-4 w-4 text-yellow-400" />
              </motion.div>
            ))}

            {/* Main icon container */}
            <motion.div
              animate={{ 
                rotate: [0, -10, 10, -10, 10, 0],
              }}
              transition={{
                duration: 0.5,
                delay: 0.2,
              }}
              className={`relative bg-gradient-to-br ${colors[type]} p-8 rounded-full shadow-2xl`}
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                }}
                transition={{
                  duration: 0.6,
                  repeat: 2,
                  repeatType: "reverse",
                }}
              >
                <Icon className="h-16 w-16 text-white" />
              </motion.div>
            </motion.div>

            {/* Message */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="absolute -bottom-16 left-1/2 -translate-x-1/2 whitespace-nowrap"
            >
              <div className="bg-background/95 backdrop-blur-sm border rounded-lg px-6 py-3 shadow-lg">
                <p className="text-lg font-semibold gradient-text">{message}</p>
              </div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Micro-interaction button with ripple effect
interface RippleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: "primary" | "secondary" | "ghost";
}

export function RippleButton({ 
  children, 
  onClick, 
  variant = "primary",
  className = "",
  ...props 
}: RippleButtonProps) {
  const [ripples, setRipples] = useState<Array<{ x: number; y: number; id: number }>>([]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    const button = e.currentTarget;
    const rect = button.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const newRipple = { x, y, id: Date.now() };
    setRipples((prev) => [...prev, newRipple]);

    // Remove ripple after animation
    setTimeout(() => {
      setRipples((prev) => prev.filter((r) => r.id !== newRipple.id));
    }, 600);

    onClick?.(e);
  };

  const variantStyles = {
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    ghost: "hover:bg-accent hover:text-accent-foreground",
  };

  return (
    <button
      onClick={handleClick}
      className={`relative overflow-hidden px-4 py-2 rounded-lg font-medium transition-all transform active:scale-95 ${variantStyles[variant]} ${className}`}
      {...props}
    >
      {children}
      {ripples.map((ripple) => (
        <span
          key={ripple.id}
          className="absolute bg-white/30 rounded-full pointer-events-none animate-ripple"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: "20px",
            height: "20px",
            transform: "translate(-50%, -50%)",
          }}
        />
      ))}
    </button>
  );
}

// Hover card effect with tilt
interface TiltCardProps {
  children: React.ReactNode;
  className?: string;
}

export function TiltCard({ children, className = "" }: TiltCardProps) {
  const [transform, setTransform] = useState("");

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const card = e.currentTarget;
    const rect = card.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width - 0.5) * 20;
    const y = ((e.clientY - rect.top) / rect.height - 0.5) * -20;
    
    setTransform(`perspective(1000px) rotateY(${x}deg) rotateX(${y}deg) scale(1.02)`);
  };

  const handleMouseLeave = () => {
    setTransform("");
  };

  return (
    <div
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      className={`transition-transform duration-200 ease-out ${className}`}
      style={{ transform }}
    >
      {children}
    </div>
  );
}

// Loading skeleton with shimmer effect
export function ShimmerSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`relative overflow-hidden bg-muted rounded-lg ${className}`}>
      <div className="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/10 to-transparent" />
    </div>
  );
}