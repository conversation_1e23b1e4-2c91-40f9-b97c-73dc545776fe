"use client"

import * as React from "react"
import { Check, ChevronsUpDown, Building2, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Company } from "@/types"
import { companiesApi } from "@/lib/api/companies"

interface CompanySelectorProps {
  value?: string
  onValueChange: (value: string | undefined) => void
  placeholder?: string
  className?: string
}

export function CompanySelector({
  value,
  onValueChange,
  placeholder = "Select company...",
  className,
}: CompanySelectorProps) {
  const [open, setOpen] = React.useState(false)
  const [companies, setCompanies] = React.useState<Company[]>([])
  const [loading, setLoading] = React.useState(false)
  const [query, setQuery] = React.useState("")

  const selectedCompany = companies.find((company) => company.id === value)

  const loadCompanies = React.useCallback(async (searchQuery: string = "") => {
    try {
      setLoading(true)
      const response = await companiesApi.getCompanies({ 
        q: searchQuery,
        limit: 50 
      })
      // Handle both response formats
      if (response) {
        if (response.success && response.data) {
          setCompanies(response.data)
        } else if (Array.isArray(response)) {
          setCompanies(response)
        } else if (response.data && Array.isArray(response.data)) {
          setCompanies(response.data)
        }
      }
    } catch (error) {
      console.error("Failed to load companies:", error)
    } finally {
      setLoading(false)
    }
  }, [])

  React.useEffect(() => {
    if (open) {
      loadCompanies(query)
    }
  }, [open, query, loadCompanies])

  const handleSelect = (companyId: string) => {
    if (companyId === value) {
      onValueChange(undefined)
    } else {
      onValueChange(companyId)
    }
    setOpen(false)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onValueChange(undefined)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
        >
          {selectedCompany ? (
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="truncate">{selectedCompany.name}</span>
              {selectedCompany.industry && (
                <Badge variant="secondary" className="text-xs">
                  {selectedCompany.industry}
                </Badge>
              )}
              <X
                className="h-4 w-4 shrink-0 opacity-50 hover:opacity-100"
                onClick={handleClear}
              />
            </div>
          ) : (
            <>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Building2 className="h-4 w-4" />
                {placeholder}
              </div>
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput 
            placeholder="Search companies..." 
            value={query}
            onValueChange={setQuery}
          />
          <CommandList>
            <CommandEmpty>
              {loading ? "Loading..." : "No companies found."}
            </CommandEmpty>
            <CommandGroup>
              {companies.map((company) => (
                <CommandItem
                  key={company.id}
                  value={company.id}
                  onSelect={handleSelect}
                  className="flex items-center gap-2 py-2"
                >
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{company.name}</div>
                    <div className="text-sm text-muted-foreground truncate">
                      {company.industry}
                      {company.size && (
                        <span className="ml-2">• {company.size} employees</span>
                      )}
                      {company.website && (
                        <span className="ml-2">• {company.website}</span>
                      )}
                    </div>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      company.id === value ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}