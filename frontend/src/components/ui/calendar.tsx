"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

export type CalendarProps = {
  mode?: "single" | "range";
  selected?: Date | { from?: Date; to?: Date };
  onSelect?: (date: Date | { from?: Date; to?: Date } | undefined) => void;
  className?: string;
  classNames?: {
    months?: string;
    month?: string;
    caption?: string;
    nav?: string;
    nav_button?: string;
    nav_button_previous?: string;
    nav_button_next?: string;
    table?: string;
    head_row?: string;
    head_cell?: string;
    row?: string;
    cell?: string;
    day?: string;
    day_selected?: string;
    day_today?: string;
    day_outside?: string;
    day_disabled?: string;
    day_range_middle?: string;
    day_hidden?: string;
  };
  showOutsideDays?: boolean;
  numberOfMonths?: number;
  defaultMonth?: Date;
  initialFocus?: boolean;
};

const WEEKDAYS = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];
const MONTHS = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  mode = "single",
  selected,
  onSelect,
  numberOfMonths = 1,
  defaultMonth = new Date(),
  initialFocus, // Accept but don't pass to DOM
  ...props
}: CalendarProps) {
  const [currentMonth, setCurrentMonth] = React.useState(defaultMonth);
  const [internalSelected, setInternalSelected] = React.useState(selected);
  const [rangeStart, setRangeStart] = React.useState<Date | undefined>(
    mode === "range" && selected && typeof selected === "object" && "from" in selected
      ? selected.from
      : undefined
  );
  const [rangeEnd, setRangeEnd] = React.useState<Date | undefined>(
    mode === "range" && selected && typeof selected === "object" && "to" in selected
      ? selected.to
      : undefined
  );

  React.useEffect(() => {
    setInternalSelected(selected);
    if (mode === "range" && selected && typeof selected === "object" && "from" in selected) {
      setRangeStart(selected.from);
      setRangeEnd(selected.to);
    }
  }, [selected, mode]);

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1));
  };

  const handleDayClick = (day: number) => {
    const clickedDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    
    if (mode === "single") {
      setInternalSelected(clickedDate);
      onSelect?.(clickedDate);
    } else if (mode === "range") {
      if (!rangeStart || (rangeStart && rangeEnd)) {
        // Start a new range
        setRangeStart(clickedDate);
        setRangeEnd(undefined);
        onSelect?.({ from: clickedDate, to: undefined });
      } else {
        // Complete the range
        if (clickedDate < rangeStart) {
          setRangeEnd(rangeStart);
          setRangeStart(clickedDate);
          onSelect?.({ from: clickedDate, to: rangeStart });
        } else {
          setRangeEnd(clickedDate);
          onSelect?.({ from: rangeStart, to: clickedDate });
        }
      }
    }
  };

  const isDateSelected = (day: number) => {
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    
    if (mode === "single" && internalSelected instanceof Date) {
      return (
        date.getDate() === internalSelected.getDate() &&
        date.getMonth() === internalSelected.getMonth() &&
        date.getFullYear() === internalSelected.getFullYear()
      );
    }
    
    if (mode === "range") {
      if (rangeStart && !rangeEnd) {
        return (
          date.getDate() === rangeStart.getDate() &&
          date.getMonth() === rangeStart.getMonth() &&
          date.getFullYear() === rangeStart.getFullYear()
        );
      }
      if (rangeStart && rangeEnd) {
        return (
          (date.getDate() === rangeStart.getDate() &&
           date.getMonth() === rangeStart.getMonth() &&
           date.getFullYear() === rangeStart.getFullYear()) ||
          (date.getDate() === rangeEnd.getDate() &&
           date.getMonth() === rangeEnd.getMonth() &&
           date.getFullYear() === rangeEnd.getFullYear())
        );
      }
    }
    
    return false;
  };

  const isDateInRange = (day: number) => {
    if (mode !== "range" || !rangeStart || !rangeEnd) return false;
    
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    return date > rangeStart && date < rangeEnd;
  };

  const isToday = (day: number) => {
    const today = new Date();
    return (
      day === today.getDate() &&
      currentMonth.getMonth() === today.getMonth() &&
      currentMonth.getFullYear() === today.getFullYear()
    );
  };

  const renderMonth = (monthOffset: number = 0) => {
    const displayMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth() + monthOffset
    );
    const daysInMonth = getDaysInMonth(displayMonth);
    const firstDay = getFirstDayOfMonth(displayMonth);
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="p-2" />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const isSelected = isDateSelected(day);
      const isInRange = isDateInRange(day);
      const isTodayDate = monthOffset === 0 && isToday(day);

      days.push(
        <button
          key={day}
          onClick={() => handleDayClick(day)}
          className={cn(
            "p-2 text-sm rounded-md hover:bg-accent hover:text-accent-foreground transition-colors",
            isSelected && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground",
            isInRange && "bg-accent",
            isTodayDate && "font-bold underline",
            classNames?.day
          )}
        >
          {day}
        </button>
      );
    }

    return (
      <div key={monthOffset} className={cn("space-y-4", classNames?.month)}>
        <div className={cn("text-sm font-medium text-center", classNames?.caption)}>
          {MONTHS[displayMonth.getMonth()]} {displayMonth.getFullYear()}
        </div>
        <div className={cn("grid grid-cols-7 gap-1", classNames?.table)}>
          {WEEKDAYS.map((day) => (
            <div
              key={day}
              className={cn("text-xs text-center text-muted-foreground p-2", classNames?.head_cell)}
            >
              {day}
            </div>
          ))}
          {days}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("p-3", className)} {...props}>
      <div className={cn("flex items-center justify-between mb-4", classNames?.nav)}>
        <Button
          variant="outline"
          size="icon"
          onClick={handlePreviousMonth}
          className={cn("h-7 w-7", classNames?.nav_button, classNames?.nav_button_previous)}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={handleNextMonth}
          className={cn("h-7 w-7", classNames?.nav_button, classNames?.nav_button_next)}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      <div className={cn("grid gap-4", numberOfMonths === 2 && "grid-cols-2", classNames?.months)}>
        {Array.from({ length: numberOfMonths }, (_, i) => renderMonth(i))}
      </div>
    </div>
  );
}
Calendar.displayName = "Calendar";

export { Calendar };