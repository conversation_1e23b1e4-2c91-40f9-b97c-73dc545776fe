"use client";

import { useCallback, useRef, useState, useEffect, memo } from "react";
import { useInView } from "@/hooks/useInView";

interface VirtualTableProps<T> {
  data: T[];
  rowHeight: number;
  visibleRows: number;
  renderRow: (item: T, index: number) => React.ReactNode;
  className?: string;
}

function VirtualTableInner<T>({
  data,
  rowHeight,
  visibleRows,
  renderRow,
  className = "",
}: VirtualTableProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const totalHeight = data.length * rowHeight;
  const scrollHeight = visibleRows * rowHeight;
  
  const startIndex = Math.floor(scrollTop / rowHeight);
  const endIndex = Math.min(
    startIndex + visibleRows + 1,
    data.length
  );

  const offsetY = startIndex * rowHeight;
  const visibleData = data.slice(startIndex, endIndex);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: scrollHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleData.map((item, index) => (
            <div
              key={startIndex + index}
              style={{ height: rowHeight }}
              className="virtual-row"
            >
              {renderRow(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export const VirtualTable = memo(VirtualTableInner) as typeof VirtualTableInner;