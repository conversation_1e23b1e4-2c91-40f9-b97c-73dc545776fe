"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check, Minus } from "lucide-react"
import { cn } from "@/lib/utils"

interface OptimizedCheckboxProps extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> {
  indeterminate?: boolean
  onCheckedChange?: (checked: boolean) => void
  optimistic?: boolean
}

const OptimizedCheckbox = React.memo(
  React.forwardRef<
    React.ElementRef<typeof CheckboxPrimitive.Root>,
    OptimizedCheckboxProps
  >(({ className, indeterminate, onCheckedChange, optimistic = true, checked, disabled, ...props }, ref) => {
    const [optimisticChecked, setOptimisticChecked] = React.useState(checked)
    const [isTransitioning, setIsTransitioning] = React.useState(false)

    React.useEffect(() => {
      setOptimisticChecked(checked)
      setIsTransitioning(false)
    }, [checked])

    const handleCheckedChange = React.useCallback((newChecked: boolean | 'indeterminate') => {
      if (disabled) return
      
      const booleanChecked = newChecked === true
      
      if (optimistic) {
        // Immediately update visual state
        setOptimisticChecked(booleanChecked)
        setIsTransitioning(true)
        
        // Small haptic-like feedback delay
        setTimeout(() => {
          setIsTransitioning(false)
        }, 150)
      }
      
      // Call the parent handler
      if (onCheckedChange) {
        onCheckedChange(booleanChecked)
      }
    }, [disabled, optimistic, onCheckedChange])

    const displayChecked = optimistic ? optimisticChecked : checked

    return (
      <CheckboxPrimitive.Root
        ref={ref}
        className={cn(
          "peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background",
          "transition-all duration-150 ease-in-out",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
          "hover:border-primary/80 hover:shadow-sm",
          isTransitioning && "scale-95",
          className
        )}
        checked={displayChecked}
        onCheckedChange={handleCheckedChange}
        disabled={disabled}
        {...props}
      >
        <CheckboxPrimitive.Indicator
          className={cn(
            "flex items-center justify-center text-current",
            "transition-all duration-150",
            isTransitioning && "scale-110"
          )}
        >
          {indeterminate ? (
            <Minus className="h-3 w-3 animate-in fade-in-0 zoom-in-95" />
          ) : displayChecked ? (
            <Check className="h-4 w-4 animate-in fade-in-0 zoom-in-95" />
          ) : null}
        </CheckboxPrimitive.Indicator>
      </CheckboxPrimitive.Root>
    )
  })
)

OptimizedCheckbox.displayName = "OptimizedCheckbox"

export { OptimizedCheckbox }