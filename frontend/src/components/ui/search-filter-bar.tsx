"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { InstantSearchInput } from "@/components/ui/instant-search-input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon,
  ChevronDown,
  Download,
  Upload,
  SlidersHorizontal,
  Check,
} from "lucide-react";

interface FilterOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
}

interface SearchFilterBarProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  onFiltersChange?: (filters: any) => void;
  onExport?: () => void;
  onImport?: () => void;
  
  // Filter options
  sortOptions?: FilterOption[];
  statusOptions?: FilterOption[];
  categoryOptions?: FilterOption[];
  customFilters?: React.ReactNode;
  
  // Current values
  searchValue?: string;  // Changed to controlled value
  defaultSearchValue?: string;  // Keep for backward compatibility
  totalCount?: number;
  filteredCount?: number;
  
  className?: string;
}

export function SearchFilterBar({
  placeholder = "Search...",
  onSearch,
  onFiltersChange,
  onExport,
  onImport,
  sortOptions = [],
  statusOptions = [],
  categoryOptions = [],
  customFilters,
  searchValue: controlledSearchValue,
  defaultSearchValue = "",
  totalCount,
  filteredCount,
  className,
}: SearchFilterBarProps) {
  // Simple controlled mode
  const searchValue = controlledSearchValue ?? defaultSearchValue;
  
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({});
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>("");

  // Simple handler for search change - just call the callback
  const handleSearchChange = useCallback((value: string) => {
    if (onSearch) {
      onSearch(value);
    }
  }, [onSearch]);

  // Update filters when they change
  useEffect(() => {
    if (onFiltersChange) {
      const filters = {
        ...activeFilters,
        dateRange,
        statuses: selectedStatuses,
        categories: selectedCategories,
        sortBy,
      };
      onFiltersChange(filters);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeFilters, dateRange, selectedStatuses, selectedCategories, sortBy]); // Remove onFiltersChange from dependencies

  const handleClearFilters = () => {
    handleSearchChange("");
    setActiveFilters({});
    setDateRange({ from: undefined, to: undefined });
    setSelectedStatuses([]);
    setSelectedCategories([]);
    setSortBy("");
  };

  const hasActiveFilters = 
    searchValue ||
    Object.keys(activeFilters).length > 0 ||
    dateRange.from ||
    selectedStatuses.length > 0 ||
    selectedCategories.length > 0 ||
    sortBy;

  const activeFilterCount = 
    (searchValue ? 1 : 0) +
    Object.keys(activeFilters).length +
    (dateRange.from ? 1 : 0) +
    selectedStatuses.length +
    selectedCategories.length +
    (sortBy ? 1 : 0);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main search and filter bar */}
      <div className="flex flex-col lg:flex-row gap-3">
        {/* Search Input */}
        <div className="flex-1">
          <InstantSearchInput
            placeholder={placeholder}
            value={searchValue}
            onChange={handleSearchChange}
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2">
          {/* Status Filter */}
          {statusOptions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="min-w-[120px]">
                  <Filter className="h-4 w-4 mr-2" />
                  Статус
                  {selectedStatuses.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {selectedStatuses.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Фильтр по статусу</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {statusOptions.map((option) => (
                  <DropdownMenuCheckboxItem
                    key={option.value}
                    checked={selectedStatuses.includes(option.value)}
                    onCheckedChange={(checked) => {
                      setSelectedStatuses(
                        checked
                          ? [...selectedStatuses, option.value]
                          : selectedStatuses.filter((s) => s !== option.value)
                      );
                    }}
                  >
                    {option.icon}
                    {option.label}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Category Filter */}
          {categoryOptions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="min-w-[120px]">
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Категория
                  {selectedCategories.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {selectedCategories.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Фильтр по категории</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {categoryOptions.map((option) => (
                  <DropdownMenuCheckboxItem
                    key={option.value}
                    checked={selectedCategories.includes(option.value)}
                    onCheckedChange={(checked) => {
                      setSelectedCategories(
                        checked
                          ? [...selectedCategories, option.value]
                          : selectedCategories.filter((c) => c !== option.value)
                      );
                    }}
                  >
                    {option.icon}
                    {option.label}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Date Range Filter */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="min-w-[240px] justify-start">
                <CalendarIcon className="h-4 w-4 mr-2" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Период</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="range"
                defaultMonth={dateRange.from}
                selected={dateRange}
                onSelect={(range: any) => setDateRange(range || {})}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>

          {/* Sort Options */}
          {sortOptions.length > 0 && (
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="min-w-[150px]">
                <SelectValue placeholder="Сортировать" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Опции сортировки</SelectLabel>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          )}

          {/* Custom Filters */}
          {customFilters}

          {/* Action Buttons */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onExport && (
                <DropdownMenuItem onClick={onExport}>
                  <Download className="h-4 w-4 mr-2" />
                  Экспорт
                </DropdownMenuItem>
              )}
              {onImport && (
                <DropdownMenuItem onClick={onImport}>
                  <Upload className="h-4 w-4 mr-2" />
                  Импорт
                </DropdownMenuItem>
              )}
              {(onExport || onImport) && hasActiveFilters && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleClearFilters}>
                    <X className="h-4 w-4 mr-2" />
                    Очистить все фильтры
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Активные фильтры:</span>
          
          {searchValue && (
            <Badge variant="secondary" className="gap-1">
              Поиск: {searchValue}
              <button
                onClick={() => handleSearchChange("")}
                className="ml-1 hover:text-destructive"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {selectedStatuses.map((status) => {
            const option = statusOptions.find((o) => o.value === status);
            return (
              <Badge key={status} variant="secondary" className="gap-1">
                {option?.label || status}
                <button
                  onClick={() =>
                    setSelectedStatuses(selectedStatuses.filter((s) => s !== status))
                  }
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            );
          })}
          
          {selectedCategories.map((category) => {
            const option = categoryOptions.find((o) => o.value === category);
            return (
              <Badge key={category} variant="secondary" className="gap-1">
                {option?.label || category}
                <button
                  onClick={() =>
                    setSelectedCategories(
                      selectedCategories.filter((c) => c !== category)
                    )
                  }
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            );
          })}
          
          {dateRange.from && (
            <Badge variant="secondary" className="gap-1">
              Дата: {format(dateRange.from, "LLL dd")}
              {dateRange.to && ` - ${format(dateRange.to, "LLL dd")}`}
              <button
                onClick={() => setDateRange({ from: undefined, to: undefined })}
                className="ml-1 hover:text-destructive"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {sortBy && (
            <Badge variant="secondary" className="gap-1">
              Сортировка: {sortOptions.find((o) => o.value === sortBy)?.label || sortBy}
              <button
                onClick={() => setSortBy("")}
                className="ml-1 hover:text-destructive"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearFilters}
            className="h-6 px-2 text-xs"
          >
            Очистить все
          </Button>
        </div>
      )}

      {/* Results Count */}
      {(totalCount !== undefined || filteredCount !== undefined) && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          {filteredCount !== undefined && filteredCount !== totalCount ? (
            <>
              Показано <span className="font-medium">{filteredCount}</span> из{" "}
              <span className="font-medium">{totalCount}</span> результатов
              {hasActiveFilters && (
                <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                  Мгновенная фильтрация
                </Badge>
              )}
            </>
          ) : totalCount !== undefined ? (
            <>
              <span className="font-medium">{totalCount}</span> результатов
            </>
          ) : null}
        </div>
      )}
    </div>
  );
}