"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { contactsApi } from "@/lib/api/contacts";
import { Contact } from "@/types";
import { Loader2, Trash2 } from "lucide-react";

interface DeleteContactDialogProps {
  contact: Contact | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DeleteContactDialog({
  contact,
  open,
  onOpenChange,
}: DeleteContactDialogProps) {
  const queryClient = useQueryClient();

  const deleteContactMutation = useMutation({
    mutationFn: (contactId: string) => contactsApi.deleteContact(contactId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      onOpenChange(false);
    },
    onError: (error) => {
      console.error("Error deleting contact:", error);
    },
  });

  const handleDelete = () => {
    if (!contact) return;
    deleteContactMutation.mutate(contact.id);
  };

  if (!contact) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Delete Contact
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this contact? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="rounded-lg border bg-muted p-4">
            <h4 className="font-medium text-sm">
              {contact.first_name} {contact.last_name}
            </h4>
            {contact.email && (
              <p className="text-sm text-muted-foreground mt-1">
                {contact.email}
              </p>
            )}
            {contact.job_title && (
              <p className="text-sm text-muted-foreground">
                {contact.job_title}
              </p>
            )}
            {contact.company && (
              <p className="text-sm text-muted-foreground">
                {contact.company.name}
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={deleteContactMutation.isPending}
          >
            Cancel
          </Button>
          <Button 
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteContactMutation.isPending}
          >
            {deleteContactMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Delete Contact
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}