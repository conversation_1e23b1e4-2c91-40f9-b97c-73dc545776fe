"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { companiesApi } from "@/lib/api/companies";
import { Company } from "@/types";
import { Loader2, Trash2 } from "lucide-react";

interface DeleteCompanyDialogProps {
  company: Company | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DeleteCompanyDialog({
  company,
  open,
  onOpenChange,
}: DeleteCompanyDialogProps) {
  const queryClient = useQueryClient();

  const deleteCompanyMutation = useMutation({
    mutationFn: (companyId: string) => companiesApi.deleteCompany(companyId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      onOpenChange(false);
    },
    onError: (error) => {
      console.error("Error deleting company:", error);
    },
  });

  const handleDelete = () => {
    if (!company) return;
    deleteCompanyMutation.mutate(company.id);
  };

  if (!company) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Delete Company
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this company? This action cannot be undone and will also affect related contacts and deals.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="rounded-lg border bg-muted p-4">
            <h4 className="font-medium text-sm">{company.name}</h4>
            {company.industry && (
              <p className="text-sm text-muted-foreground mt-1">
                Industry: {company.industry}
              </p>
            )}
            {company.size && (
              <p className="text-sm text-muted-foreground">
                Size: {company.size} employees
              </p>
            )}
            {company.website && (
              <p className="text-sm text-muted-foreground">
                Website: {company.website}
              </p>
            )}
            {company.contacts && company.contacts.length > 0 && (
              <p className="text-sm text-yellow-600 mt-2">
                ⚠️ This company has {company.contacts.length} associated contact(s)
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={deleteCompanyMutation.isPending}
          >
            Cancel
          </Button>
          <Button 
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteCompanyMutation.isPending}
          >
            {deleteCompanyMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Delete Company
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}