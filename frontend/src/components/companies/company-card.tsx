"use client";

import React from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Globe, Users, Mail, Phone, MoreHorizontal, Edit, Trash2 } from "lucide-react";
import { Company } from "@/types";

interface CompanyCardProps {
  company: Company;
  onEdit: (company: Company) => void;
  onDelete: (company: Company) => void;
}

export const CompanyCard = React.memo(({ company, onEdit, onDelete }: CompanyCardProps) => {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg truncate mb-1">
              {company.name}
            </CardTitle>
            {company.industry && (
              <p className="text-sm text-muted-foreground">
                {company.industry}
              </p>
            )}
            {company.size && (
              <Badge variant="outline" className="text-xs mt-1">
                {company.size} employees
              </Badge>
            )}
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(company)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(company)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="space-y-2">
          {company.website && (
            <div className="flex items-center space-x-2 text-sm">
              <Globe className="h-4 w-4 text-gray-400" />
              <a 
                href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline truncate"
              >
                {company.website}
              </a>
            </div>
          )}
          
          {company.email && (
            <div className="flex items-center space-x-2 text-sm">
              <Mail className="h-4 w-4 text-gray-400" />
              <span className="truncate">{company.email}</span>
            </div>
          )}
          
          {company.phone && (
            <div className="flex items-center space-x-2 text-sm">
              <Phone className="h-4 w-4 text-gray-400" />
              <span>{company.phone}</span>
            </div>
          )}

          {company.contacts && company.contacts.length > 0 && (
            <div className="flex items-center space-x-2 text-sm">
              <Users className="h-4 w-4 text-gray-400" />
              <span>{company.contacts.length} contact{company.contacts.length !== 1 ? 's' : ''}</span>
            </div>
          )}
        </div>

        {company.address && (
          <div className="text-sm text-muted-foreground">
            <p className="line-clamp-2">{company.address}</p>
          </div>
        )}

        {company.tags && company.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {company.tags.slice(0, 3).map((tag) => (
              <Badge
                key={tag.id}
                variant="secondary"
                className="text-xs"
                style={{ backgroundColor: `${tag.color}20`, color: tag.color }}
              >
                {tag.name}
              </Badge>
            ))}
            {company.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{company.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
});

CompanyCard.displayName = "CompanyCard";