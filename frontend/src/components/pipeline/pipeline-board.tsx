"use client";

import { useEffect, useState } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import { usePipelineStore, usePipelineSelectors } from "@/stores/pipeline";
import { usePipelineSSE } from "@/lib/sse/hooks";
import { StageColumn } from "./stage-column";
import { CardItem } from "./card-item";
import { EnhancedCardItem } from "./enhanced-card-item";
import { EnhancedDragOverlay } from "./enhanced-drag-overlay";
import { AddCardDialog } from "./add-card-dialog";
import { EditCardDialog } from "./edit-card-dialog";
import { DeleteCardDialog } from "./delete-card-dialog";
import { Button } from "@/components/ui/button";
import { Plus, RefreshCw } from "lucide-react";
import { Card as CardType, Stage } from "@/types";

interface PipelineBoardProps {
  pipelineId: string;
}

export function PipelineBoard({ pipelineId }: PipelineBoardProps) {
  const [addCardOpen, setAddCardOpen] = useState(false);
  const [selectedStageId, setSelectedStageId] = useState<string>("");
  const [editCardOpen, setEditCardOpen] = useState(false);
  const [deleteCardOpen, setDeleteCardOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<CardType | null>(null);
  const [activeCard, setActiveCard] = useState<CardType | null>(null);

  const { 
    stages, 
    cards, 
    isLoading, 
    error, 
    loadPipelineData, 
    moveCard 
  } = usePipelineStore();
  
  const { getCardsByStage } = usePipelineSelectors();
  
  // Set up SSE for real-time updates
  usePipelineSSE(pipelineId);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required to start dragging
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    loadPipelineData(pipelineId);
  }, [pipelineId, loadPipelineData]);

  const handleEditCard = (card: CardType) => {
    setSelectedCard(card);
    setEditCardOpen(true);
  };

  const handleDeleteCard = async (cardId: string) => {
    const card = cards.find(c => c.id === cardId);
    if (card) {
      setSelectedCard(card);
      setDeleteCardOpen(true);
    }
  };

  const handleAddCard = (stageId?: string) => {
    setSelectedStageId(stageId || "");
    setAddCardOpen(true);
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const activeCardId = active.id as string;
    const card = cards.find((card) => card.id === activeCardId);
    setActiveCard(card || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    // Clear active card
    setActiveCard(null);

    if (!over) return;

    const activeCardId = active.id as string;
    const overStageId = over.data?.current?.stageId as string;

    if (!overStageId) return;

    const activeCard = cards.find((card) => card.id === activeCardId);
    if (!activeCard) return;

    // If card is already in the target stage, don't move
    if (activeCard.stage_id === overStageId) return;

    // Calculate new position (add to end of stage)
    const targetStageCards = getCardsByStage(overStageId);
    const newPosition = targetStageCards.length;

    // Move the card with haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate([50, 50, 100]);
    }

    await moveCard(activeCardId, overStageId, newPosition);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-destructive/15 border border-destructive/20 rounded-md p-4">
        <p className="text-destructive mb-4">{error}</p>
        <Button onClick={() => loadPipelineData(pipelineId)} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Повторить
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Воронка продаж</h1>
          <p className="text-gray-600 mt-1">
            Управляйте сделками и отслеживайте прогресс
          </p>
        </div>
        <Button onClick={() => handleAddCard()}>
          <Plus className="h-4 w-4 mr-2" />
          Добавить карточку
        </Button>
      </div>

      {/* Pipeline Board */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex space-x-6 overflow-x-auto pb-6">
          <SortableContext
            items={stages.map(stage => stage.id)}
            strategy={horizontalListSortingStrategy}
          >
            {stages.map((stage) => (
              <StageColumn
                key={stage.id}
                stage={stage}
                cards={getCardsByStage(stage.id)}
                onEditCard={handleEditCard}
                onDeleteCard={handleDeleteCard}
                onAddCard={handleAddCard}
              />
            ))}
          </SortableContext>
        </div>

        {/* Enhanced Drag Overlay */}
        <EnhancedDragOverlay activeCard={activeCard} />
      </DndContext>

      {stages.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No pipeline stages found</p>
          <p className="text-gray-400 mt-2">
            Create some stages to start managing your deals
          </p>
        </div>
      )}

      {/* Add Card Dialog */}
      <AddCardDialog
        open={addCardOpen}
        onOpenChange={setAddCardOpen}
        stages={stages}
        defaultStageId={selectedStageId}
        pipelineId={pipelineId}
      />

      {/* Edit Card Dialog */}
      <EditCardDialog
        card={selectedCard}
        open={editCardOpen}
        onOpenChange={setEditCardOpen}
        stages={stages}
        pipelineId={pipelineId}
      />

      {/* Delete Card Dialog */}
      <DeleteCardDialog
        card={selectedCard}
        open={deleteCardOpen}
        onOpenChange={setDeleteCardOpen}
        pipelineId={pipelineId}
      />
    </div>
  );
}