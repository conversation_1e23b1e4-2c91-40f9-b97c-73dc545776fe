"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { cardsApi } from "@/lib/api/cards";
import { Card } from "@/types";
import { Loader2, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface DeleteCardDialogProps {
  card: Card | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pipelineId: string;
}

export function DeleteCardDialog({
  card,
  open,
  onOpenChange,
  pipelineId,
}: DeleteCardDialogProps) {
  const queryClient = useQueryClient();

  const deleteCardMutation = useMutation({
    mutationFn: (cardId: string) => cardsApi.deleteCard(cardId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pipeline", pipelineId] });
      queryClient.invalidateQueries({ queryKey: ["cards"] });
      toast.success("Карточка успешно удалена", {
        description: `"${card?.title}" удалена из воронки.`
      });
      onOpenChange(false);
    },
    onError: (error: any) => {
      console.error("Error deleting card:", error);
      toast.error("Не удалось удалить карточку", {
        description: error.response?.data?.message || "Пожалуйста, попробуйте позже."
      });
    },
  });

  const handleDelete = () => {
    if (!card) return;
    deleteCardMutation.mutate(card.id);
  };

  if (!card) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            Удалить карточку
          </DialogTitle>
          <DialogDescription>
            Вы уверены, что хотите удалить эту карточку? Это действие нельзя отменить.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="rounded-lg border bg-muted p-4">
            <h4 className="font-medium text-sm">{card.title}</h4>
            {card.value && (
              <p className="text-sm text-muted-foreground mt-1">
                Сумма: {card.value} {card.currency}
              </p>
            )}
            {card.stage && (
              <p className="text-sm text-muted-foreground">
                Этап: {card.stage.name}
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={deleteCardMutation.isPending}
          >
            Отмена
          </Button>
          <Button 
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteCardMutation.isPending}
          >
            {deleteCardMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Удалить карточку
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}