"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cardsApi } from "@/lib/api/cards";
import { Card, UpdateCardRequest, Stage, CardPriority } from "@/types";
import { Loader2 } from "lucide-react";
import { ContactSelector } from "@/components/ui/contact-selector";
import { CompanySelector } from "@/components/ui/company-selector";
import { UserSelector } from "@/components/ui/user-selector";
import { useEffect } from "react";

const editCardSchema = z.object({
  title: z.string().min(1, "Название обязательно"),
  description: z.string().optional(),
  value: z.number().min(0).optional(),
  currency: z.string().default("USD"),
  probability: z.number().min(0).max(100).default(50),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  expected_close_date: z.string().optional(),
  stage_id: z.string().min(1, "Этап обязателен"),
  contact_id: z.string().optional(),
  company_id: z.string().optional(),
  assigned_user_id: z.string().optional(),
});

type EditCardFormData = z.infer<typeof editCardSchema>;

interface EditCardDialogProps {
  card: Card | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stages: Stage[];
  pipelineId: string;
}

export function EditCardDialog({
  card,
  open,
  onOpenChange,
  stages,
  pipelineId,
}: EditCardDialogProps) {
  const queryClient = useQueryClient();

  const form = useForm<EditCardFormData>({
    resolver: zodResolver(editCardSchema),
    defaultValues: {
      title: "",
      description: "",
      value: undefined,
      currency: "USD",
      probability: 50,
      priority: "medium" as CardPriority,
      stage_id: "",
      expected_close_date: "",
      contact_id: undefined,
      company_id: undefined,
      assigned_user_id: undefined,
    },
  });

  // Reset form when card changes
  useEffect(() => {
    if (card && open) {
      form.reset({
        title: card.title,
        description: card.description || "",
        value: card.value || undefined,
        currency: card.currency || "USD",
        probability: card.probability || 50,
        priority: card.priority || "medium",
        stage_id: card.stage_id,
        expected_close_date: card.expected_close_date 
          ? new Date(card.expected_close_date).toISOString().split('T')[0] 
          : "",
        contact_id: card.contact_id,
        company_id: card.company_id,
        assigned_user_id: card.assigned_user_id,
      });
    }
  }, [card, open, form]);

  const updateCardMutation = useMutation({
    mutationFn: (data: UpdateCardRequest) => cardsApi.updateCard(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pipeline", pipelineId] });
      queryClient.invalidateQueries({ queryKey: ["cards"] });
      onOpenChange(false);
    },
    onError: (error) => {
      console.error("Error updating card:", error);
    },
  });

  const onSubmit = (data: EditCardFormData) => {
    if (!card) return;
    
    const updateData: UpdateCardRequest = {
      id: card.id,
      ...data,
      value: data.value || 0,
    };
    updateCardMutation.mutate(updateData);
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  if (!card) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Редактировать карточку</DialogTitle>
          <DialogDescription>
            Обновите данные карточки и отслеживайте прогресс сделки.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Название *</FormLabel>
                  <FormControl>
                    <Input placeholder="Введите название карточки" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Описание</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Введите описание карточки" 
                      className="min-h-[80px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Сумма</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="0"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value ? parseFloat(value) : undefined);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Валюта</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Выберите валюту" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                        <SelectItem value="RUB">RUB</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="probability"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Вероятность (%)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0" 
                        max="100" 
                        {...field}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          field.onChange(isNaN(value) ? 0 : Math.min(100, Math.max(0, value)));
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expected_close_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ожидаемая дата закрытия</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Приоритет</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Выберите приоритет" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="low">Низкий</SelectItem>
                      <SelectItem value="medium">Средний</SelectItem>
                      <SelectItem value="high">Высокий</SelectItem>
                      <SelectItem value="urgent">Срочный</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="contact_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Контакт</FormLabel>
                    <FormControl>
                      <ContactSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Выберите контакт..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Компания</FormLabel>
                    <FormControl>
                      <CompanySelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Выберите компанию..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="assigned_user_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ответственный</FormLabel>
                  <FormControl>
                    <UserSelector
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Назначить ответственного..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="stage_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Этап *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Выберите этап" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {stages.map((stage) => (
                        <SelectItem key={stage.id} value={stage.id}>
                          {stage.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={updateCardMutation.isPending}
              >
                Отмена
              </Button>
              <Button 
                type="submit" 
                disabled={updateCardMutation.isPending}
              >
                {updateCardMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Обновить карточку
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}