"use client";

import { Drag<PERSON>ver<PERSON> } from "@dnd-kit/core";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card as CardType } from "@/types";
import { formatCurrency, formatDate } from "@/lib/utils";
import { 
  Calendar, 
  DollarSign, 
  User, 
  Building2, 
  GripVertical,
  AlertTriangle,
  ArrowUp,
  Minus,
  ArrowDown,
  Clock,
  TrendingUp,
  Sparkles
} from "lucide-react";

interface EnhancedDragOverlayProps {
  activeCard: CardType | null;
}

export function EnhancedDragOverlay({ activeCard }: EnhancedDragOverlayProps) {
  if (!activeCard) return null;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'from-red-500 to-red-600';
      case 'high': return 'from-orange-500 to-orange-600';
      case 'medium': return 'from-yellow-500 to-yellow-600';
      case 'low': return 'from-gray-400 to-gray-500';
      default: return 'from-blue-500 to-blue-600';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <AlertTriangle className="h-3 w-3" />;
      case 'high': return <ArrowUp className="h-3 w-3" />;
      case 'medium': return <Minus className="h-3 w-3" />;
      case 'low': return <ArrowDown className="h-3 w-3" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'contacted': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'qualified': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'proposal': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'negotiation': return 'bg-red-100 text-red-800 border-red-200';
      case 'closed_won': return 'bg-green-100 text-green-800 border-green-200';
      case 'closed_lost': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <DragOverlay>
      <AnimatePresence>
        <motion.div
          initial={{ 
            scale: 1,
            rotate: 0,
            opacity: 1
          }}
          animate={{ 
            scale: 1.05,
            rotate: 3,
            opacity: 0.95
          }}
          exit={{ 
            scale: 0.95,
            rotate: 0,
            opacity: 0
          }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 25,
            duration: 0.2
          }}
          className="relative"
        >
          {/* Glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-xl blur-xl scale-110" />
          
          {/* Sparkle effects */}
          <div className="absolute -top-2 -right-2 text-primary/60">
            <motion.div
              animate={{ 
                rotate: 360,
                scale: [1, 1.2, 1]
              }}
              transition={{ 
                rotate: { duration: 2, repeat: Infinity, ease: "linear" },
                scale: { duration: 1, repeat: Infinity, ease: "easeInOut" }
              }}
            >
              <Sparkles className="h-4 w-4" />
            </motion.div>
          </div>

          <Card className="w-80 group relative overflow-hidden backdrop-blur-xl bg-card/95 border-2 border-primary/30 shadow-2xl shadow-primary/20">
            {/* Priority Accent with pulse */}
            {activeCard.priority && (
              <motion.div 
                className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${getPriorityColor(activeCard.priority)}`}
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
              />
            )}

            <CardContent className="p-4">
              {/* Drag Handle */}
              <div className="flex items-center justify-between mb-3">
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity, 
                    ease: "easeInOut" 
                  }}
                  className="cursor-grabbing p-1 rounded bg-primary/10"
                >
                  <GripVertical className="h-4 w-4 text-primary" />
                </motion.div>
                
                {activeCard.priority && (
                  <Badge 
                    variant="outline" 
                    className={`text-xs border ${getPriorityColor(activeCard.priority).replace('from-', 'border-').replace(' to-red-600', '').replace(' to-orange-600', '').replace(' to-yellow-600', '').replace(' to-gray-500', '').replace(' to-blue-600', '')} bg-gradient-to-r ${getPriorityColor(activeCard.priority)} text-white border-none`}
                  >
                    {getPriorityIcon(activeCard.priority)}
                    <span className="ml-1 capitalize">{activeCard.priority}</span>
                  </Badge>
                )}
              </div>

              {/* Card Title */}
              <h3 className="font-semibold text-lg mb-2 line-clamp-2 text-foreground">
                {activeCard.title}
              </h3>

              {/* Card Description */}
              {activeCard.description && (
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {activeCard.description}
                </p>
              )}

              {/* Value */}
              {activeCard.value && (
                <div className="flex items-center gap-2 mb-3">
                  <div className="p-1.5 rounded-full bg-green-100 dark:bg-green-900/30">
                    <DollarSign className="h-3 w-3 text-green-600 dark:text-green-400" />
                  </div>
                  <span className="font-semibold text-green-600 dark:text-green-400">
                    {formatCurrency(activeCard.value)}
                  </span>
                </div>
              )}

              {/* Contact Info */}
              {(activeCard.contact_name || activeCard.company_name) && (
                <div className="space-y-2 mb-3">
                  {activeCard.contact_name && (
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">{activeCard.contact_name}</span>
                    </div>
                  )}
                  {activeCard.company_name && (
                    <div className="flex items-center gap-2 text-sm">
                      <Building2 className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">{activeCard.company_name}</span>
                    </div>
                  )}
                </div>
              )}

              {/* Dates */}
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                {activeCard.expected_close_date && (
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDate(activeCard.expected_close_date)}</span>
                  </div>
                )}
                {activeCard.created_at && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{formatDate(activeCard.created_at)}</span>
                  </div>
                )}
              </div>

              {/* Status Badge */}
              {activeCard.status && (
                <div className="mt-3 flex justify-end">
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${getStatusColor(activeCard.status)}`}
                  >
                    {activeCard.status.replace('_', ' ').toUpperCase()}
                  </Badge>
                </div>
              )}
            </CardContent>

            {/* Animated border */}
            <motion.div
              className="absolute inset-0 rounded-xl border-2 border-primary/50"
              animate={{ 
                borderColor: ["rgba(var(--primary), 0.3)", "rgba(var(--primary), 0.7)", "rgba(var(--primary), 0.3)"]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity, 
                ease: "easeInOut" 
              }}
            />
          </Card>
        </motion.div>
      </AnimatePresence>
    </DragOverlay>
  );
}
