"use client";

import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { EmptyState } from "@/components/ui/empty-state";
import { CardItem } from "./card-item";
import { Stage, Card as CardType } from "@/types";
import { formatCurrency } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Inbox, TrendingUp } from "lucide-react";

interface StageColumnProps {
  stage: Stage;
  cards: CardType[];
  onEditCard?: (card: CardType) => void;
  onDeleteCard?: (cardId: string) => void;
  onAddCard?: (stageId: string) => void;
}

export function StageColumn({ stage, cards, onEditCard, onDeleteCard, onAddCard }: StageColumnProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: stage.id,
    data: {
      stageId: stage.id,
    },
  });

  const totalValue = cards.reduce((sum, card) => sum + (card.value || 0), 0);

  return (
    <motion.div 
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="flex-shrink-0 w-80"
    >
      <Card className="h-full bg-card/50 backdrop-blur-sm border border-border/50 hover:shadow-lg transition-all duration-300">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full shadow-sm"
                style={{ backgroundColor: stage.color || "#6366f1" }}
              />
              <span className="text-foreground">{stage.name}</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge 
                variant="secondary" 
                className="text-xs bg-muted/50 hover:bg-muted transition-colors"
              >
                {cards.length}
              </Badge>
            </div>
          </div>
          {totalValue > 0 && (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center gap-1 mt-2"
            >
              <TrendingUp className="h-3 w-3 text-green-600" />
              <p className="text-sm font-medium text-green-600">
                {formatCurrency(totalValue)}
              </p>
            </motion.div>
          )}
        </CardHeader>

        <CardContent className="pt-0">
          <div
            ref={setNodeRef}
            className={`space-y-3 min-h-96 rounded-xl border-2 border-dashed p-4 transition-all duration-300 ${
              isOver
                ? "border-primary bg-primary/10 scale-[1.02] shadow-lg"
                : "border-border/50 bg-background/30 hover:bg-background/50"
            }`}
          >
            <SortableContext
              items={cards.map((card) => card.id)}
              strategy={verticalListSortingStrategy}
            >
              <AnimatePresence mode="popLayout">
                {cards.map((card) => (
                  <motion.div
                    key={card.id}
                    layout
                    initial={{ opacity: 0, y: 20, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -20, scale: 0.9 }}
                    transition={{ 
                      type: "spring", 
                      stiffness: 300, 
                      damping: 25,
                      opacity: { duration: 0.2 }
                    }}
                  >
                    <CardItem 
                      card={card} 
                      onEditCard={onEditCard}
                      onDeleteCard={onDeleteCard}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </SortableContext>

            {cards.length === 0 && !isOver && (
              <EmptyState
                icon={Inbox}
                title="Нет карточек на этом этапе"
                description="Начните с создания первой карточки или перетащите карточки с других этапов."
                action={{
                  label: "Добавить первую карточку",
                  onClick: () => onAddCard?.(stage.id)
                }}
              />
            )}

            {isOver && (
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="flex items-center justify-center py-8 text-primary font-medium bg-primary/5 rounded-lg border border-primary/20"
              >
                <Plus className="h-5 w-5 mr-2" />
                Перетащите карточку сюда
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}