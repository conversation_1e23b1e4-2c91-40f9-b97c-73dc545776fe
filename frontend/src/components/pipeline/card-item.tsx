"use client";

import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card as CardType } from "@/types";
import { formatCurrency, formatDate } from "@/lib/utils";
import { motion } from "framer-motion";
import { 
  Calendar, 
  DollarSign, 
  User, 
  Building2, 
  GripVertical,
  MoreHorizontal,
  Trash2,
  Edit,
  AlertTriangle,
  ArrowUp,
  Minus,
  ArrowDown,
  Clock,
  TrendingUp
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface CardItemProps {
  card: CardType;
  onEditCard?: (card: CardType) => void;
  onDeleteCard?: (cardId: string) => void;
}

export function CardItem({ card, onEditCard, onDeleteCard }: CardItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: card.id,
    data: {
      card,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'from-red-500 to-red-600';
      case 'high': return 'from-orange-500 to-orange-600';
      case 'medium': return 'from-yellow-500 to-yellow-600';
      case 'low': return 'from-gray-400 to-gray-500';
      default: return 'from-blue-500 to-blue-600';
    }
  };

  return (
    <motion.div
      whileHover={{ y: -2, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <Card
        ref={setNodeRef}
        style={style}
        className={`group relative overflow-hidden backdrop-blur-sm bg-card/90 border border-border/50 transition-all duration-300 hover:shadow-xl hover:shadow-primary/10 hover:border-primary/30 ${
          isDragging ? "opacity-60 shadow-2xl z-50 scale-105" : ""
        }`}
      >
        {/* Priority Accent */}
        {card.priority && (
          <div 
            className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${getPriorityColor(card.priority)}`}
          />
        )}
        <CardContent className="p-4">
          {/* Drag Handle & Actions */}
          <div className="flex items-center justify-between mb-3">
            <motion.div
              whileHover={{ scale: 1.1 }}
              className="cursor-grab active:cursor-grabbing opacity-50 hover:opacity-100 transition-opacity p-1 rounded"
              {...attributes}
              {...listeners}
            >
              <GripVertical className="h-4 w-4 text-muted-foreground" />
            </motion.div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted/80"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="backdrop-blur-sm">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onEditCard?.(card);
                  }}
                  className="cursor-pointer"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteCard?.(card.id);
                  }}
                  className="text-destructive focus:text-destructive cursor-pointer"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Card Title with Priority */}
          <div
            className="cursor-pointer group/title"
            onClick={() => onEditCard?.(card)}
          >
            <div className="flex items-start gap-2 mb-2">
              {card.priority && (
                <div className="flex-shrink-0 mt-0.5 p-1 rounded-full bg-muted/50">
                  {card.priority === 'urgent' && (
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                  )}
                  {card.priority === 'high' && (
                    <ArrowUp className="h-3 w-3 text-orange-500" />
                  )}
                  {card.priority === 'medium' && (
                    <Minus className="h-3 w-3 text-yellow-500" />
                  )}
                  {card.priority === 'low' && (
                    <ArrowDown className="h-3 w-3 text-gray-400" />
                  )}
                </div>
              )}
              <h3 className="font-semibold text-sm leading-tight line-clamp-2 flex-1 group-hover/title:text-primary transition-colors">
                {card.title}
              </h3>
            </div>
          </div>

          {/* Card Value */}
          {card.value && (
            <motion.div 
              whileHover={{ scale: 1.05 }}
              className="flex items-center gap-2 p-2 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-lg border border-green-200/50 dark:border-green-800/50 mb-3"
            >
              <div className="p-1 bg-green-100 dark:bg-green-900 rounded">
                <TrendingUp className="h-3 w-3 text-green-600" />
              </div>
              <span className="text-sm font-semibold text-green-700 dark:text-green-400">
                {formatCurrency(card.value, card.currency)}
              </span>
            </motion.div>
          )}

        {/* Card Details */}
        <div className="space-y-2 text-xs text-muted-foreground">
          {/* Assigned User */}
          {card.assigned_user && (
            <div className="flex items-center space-x-2">
              <Avatar className="h-5 w-5">
                <AvatarImage 
                  src={card.assigned_user.avatar_url} 
                  alt={card.assigned_user.first_name} 
                />
                <AvatarFallback className="text-xs">
                  {card.assigned_user.first_name?.charAt(0)}
                  {card.assigned_user.last_name?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span className="truncate">
                {card.assigned_user.first_name} {card.assigned_user.last_name}
              </span>
            </div>
          )}

          {/* Contact */}
          {card.contact && (
            <div className="flex items-center space-x-1">
              <User className="h-3 w-3" />
              <span className="truncate">
                {card.contact.first_name} {card.contact.last_name}
              </span>
            </div>
          )}

          {/* Company */}
          {card.company && (
            <div className="flex items-center space-x-1">
              <Building2 className="h-3 w-3" />
              <span className="truncate">{card.company.name}</span>
            </div>
          )}

          {/* Expected Close Date */}
          {card.expected_close_date && (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(card.expected_close_date)}</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {card.tags && card.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {card.tags.slice(0, 2).map((tag) => (
              <Badge
                key={tag.id}
                variant="secondary"
                className="text-xs py-0 px-2"
                style={{ backgroundColor: `${tag.color}20`, color: tag.color }}
              >
                {tag.name}
              </Badge>
            ))}
            {card.tags.length > 2 && (
              <Badge variant="secondary" className="text-xs py-0 px-2">
                +{card.tags.length - 2}
              </Badge>
            )}
          </div>
        )}

          {/* Probability Bar */}
          {card.probability !== undefined && card.probability > 0 && (
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs mb-2">
                <span className="flex items-center gap-1 text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  Probability
                </span>
                <span className="font-medium text-foreground">{card.probability}%</span>
              </div>
              <div className="relative w-full bg-muted rounded-full h-2 overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${card.probability}%` }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-sm"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}