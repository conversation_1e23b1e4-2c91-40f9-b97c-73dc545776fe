"use client";

import { useDroppable } from "@dnd-kit/core";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { 
  Plus, 
  Target, 
  Zap, 
  ArrowDown,
  CheckCircle,
  XCircle
} from "lucide-react";

interface ModernDropZoneProps {
  id: string;
  children: React.ReactNode;
  className?: string;
  isActive?: boolean;
  canDrop?: boolean;
  isEmpty?: boolean;
  onAddCard?: () => void;
}

export function ModernDropZone({ 
  id, 
  children, 
  className, 
  isActive = false,
  canDrop = true,
  isEmpty = false,
  onAddCard 
}: ModernDropZoneProps) {
  const { setNodeRef, isOver } = useDroppable({
    id,
    data: {
      stageId: id,
    },
  });

  const isDropActive = isOver && canDrop;
  const isDropInvalid = isOver && !canDrop;

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "relative min-h-96 rounded-xl transition-all duration-300 ease-out",
        "border-2 border-dashed p-4",
        // Default state
        !isOver && "border-border/30 bg-background/20 hover:bg-background/40 hover:border-border/50",
        // Valid drop state
        isDropActive && [
          "border-primary bg-gradient-to-br from-primary/10 via-primary/5 to-transparent",
          "shadow-lg shadow-primary/20 scale-[1.02]",
          "border-primary/60"
        ],
        // Invalid drop state
        isDropInvalid && [
          "border-destructive bg-gradient-to-br from-destructive/10 via-destructive/5 to-transparent",
          "shadow-lg shadow-destructive/20",
          "border-destructive/60"
        ],
        className
      )}
    >
      {/* Animated background gradient */}
      <AnimatePresence>
        {isDropActive && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute inset-0 rounded-xl bg-gradient-to-br from-primary/5 via-transparent to-primary/10"
          />
        )}
      </AnimatePresence>

      {/* Pulse effect for active drop */}
      <AnimatePresence>
        {isDropActive && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ 
              opacity: [0, 0.3, 0],
              scale: [0.5, 1.2, 1.5]
            }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeOut"
            }}
            className="absolute inset-0 rounded-xl border-2 border-primary/30"
          />
        )}
      </AnimatePresence>

      {/* Ripple effect */}
      <AnimatePresence>
        {isDropActive && (
          <>
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ 
                  opacity: [0, 0.4, 0],
                  scale: [0, 1, 1.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.3,
                  ease: "easeOut"
                }}
                className="absolute inset-0 rounded-xl border border-primary/20"
              />
            ))}
          </>
        )}
      </AnimatePresence>

      {/* Drop indicator */}
      <AnimatePresence>
        {isOver && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
            className="absolute inset-0 flex items-center justify-center z-10"
          >
            <div className={cn(
              "flex flex-col items-center gap-3 p-6 rounded-xl backdrop-blur-sm",
              "border-2 border-dashed transition-all duration-300",
              isDropActive && [
                "border-primary bg-primary/10 text-primary",
                "shadow-lg shadow-primary/20"
              ],
              isDropInvalid && [
                "border-destructive bg-destructive/10 text-destructive",
                "shadow-lg shadow-destructive/20"
              ]
            )}>
              {/* Icon with animation */}
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: isDropActive ? [0, 10, -10, 0] : 0
                }}
                transition={{ 
                  duration: 1.5, 
                  repeat: Infinity, 
                  ease: "easeInOut" 
                }}
                className={cn(
                  "p-3 rounded-full",
                  isDropActive && "bg-primary/20",
                  isDropInvalid && "bg-destructive/20"
                )}
              >
                {isDropActive && <CheckCircle className="h-8 w-8" />}
                {isDropInvalid && <XCircle className="h-8 w-8" />}
              </motion.div>

              {/* Text */}
              <div className="text-center">
                <motion.p 
                  className="font-semibold text-lg"
                  animate={{ opacity: [0.7, 1, 0.7] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  {isDropActive && "Отпустите карточку здесь"}
                  {isDropInvalid && "Нельзя поместить сюда"}
                </motion.p>
                <p className="text-sm opacity-75 mt-1">
                  {isDropActive && "Карточка будет перемещена в этот этап"}
                  {isDropInvalid && "Выберите другой этап"}
                </p>
              </div>

              {/* Animated arrow */}
              {isDropActive && (
                <motion.div
                  animate={{ 
                    y: [0, 10, 0],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{ 
                    duration: 1.5, 
                    repeat: Infinity, 
                    ease: "easeInOut" 
                  }}
                >
                  <ArrowDown className="h-6 w-6" />
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Empty state with add button */}
      {isEmpty && !isOver && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onAddCard}
            className={cn(
              "flex flex-col items-center gap-3 p-6 rounded-xl",
              "border-2 border-dashed border-muted-foreground/25",
              "hover:border-primary/50 hover:bg-primary/5",
              "transition-all duration-300 group"
            )}
          >
            <motion.div
              animate={{ 
                rotate: [0, 180, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity, 
                ease: "easeInOut" 
              }}
              className="p-3 rounded-full bg-muted/50 group-hover:bg-primary/20 transition-colors"
            >
              <Plus className="h-6 w-6 text-muted-foreground group-hover:text-primary transition-colors" />
            </motion.div>
            <div className="text-center">
              <p className="font-medium text-muted-foreground group-hover:text-primary transition-colors">
                Добавить карточку
              </p>
              <p className="text-sm text-muted-foreground/75 mt-1">
                Или перетащите карточку сюда
              </p>
            </div>
          </motion.button>
        </motion.div>
      )}

      {/* Content */}
      <div className={cn(
        "relative z-0 transition-all duration-300",
        isOver && "opacity-30 blur-sm"
      )}>
        {children}
      </div>

      {/* Corner decorations */}
      <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-muted-foreground/20 rounded-tl-lg" />
      <div className="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-muted-foreground/20 rounded-tr-lg" />
      <div className="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-muted-foreground/20 rounded-bl-lg" />
      <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-muted-foreground/20 rounded-br-lg" />
    </div>
  );
}
