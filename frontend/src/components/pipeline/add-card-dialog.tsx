"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cardsApi } from "@/lib/api/cards";
import { CreateCardRequest, Stage, CardPriority } from "@/types";
import { Loader2, <PERSON><PERSON>ronRight, ChevronLeft, Check, Zap, DollarSign, Users } from "lucide-react";
import { ContactSelector } from "@/components/ui/contact-selector";
import { CompanySelector } from "@/components/ui/company-selector";
import { UserSelector } from "@/components/ui/user-selector";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

const addCardSchema = z.object({
  title: z.string().min(1, "Название обязательно"),
  description: z.string().optional(),
  value: z.number().min(0).optional(),
  currency: z.string().default("USD"),
  probability: z.number().min(0).max(100).default(50),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  expected_close_date: z.string().optional(),
  stage_id: z.string().min(1, "Этап обязателен"),
  contact_id: z.string().optional(),
  company_id: z.string().optional(),
  assigned_user_id: z.string().optional(),
});

type AddCardFormData = z.infer<typeof addCardSchema>;

interface AddCardDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stages: Stage[];
  defaultStageId?: string;
  pipelineId: string;
}

export function AddCardDialog({
  open,
  onOpenChange,
  stages,
  defaultStageId,
  pipelineId,
}: AddCardDialogProps) {
  const queryClient = useQueryClient();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const form = useForm<AddCardFormData>({
    resolver: zodResolver(addCardSchema),
    defaultValues: {
      title: "",
      description: "",
      value: undefined,
      currency: "USD",
      probability: 50,
      priority: "medium" as CardPriority,
      stage_id: defaultStageId || stages[0]?.id || "",
      contact_id: undefined,
      company_id: undefined,
      assigned_user_id: undefined,
    },
  });

  const createCardMutation = useMutation({
    mutationFn: (data: CreateCardRequest) => cardsApi.createCard(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["pipeline", pipelineId] });
      queryClient.invalidateQueries({ queryKey: ["cards"] });
      toast.success("Карточка успешно создана", {
        description: `"${data.title}" добавлена в воронку.`
      });
      onOpenChange(false);
      form.reset();
    },
    onError: (error: any) => {
      console.error("Error creating card:", error);
      toast.error("Не удалось создать карточку", {
        description: error.response?.data?.message || "Пожалуйста, попробуйте позже."
      });
    },
  });

  const onSubmit = (data: AddCardFormData) => {
    const createData: CreateCardRequest = {
      ...data,
      value: data.value || 0,
    };
    createCardMutation.mutate(createData);
  };

  const handleClose = () => {
    form.reset();
    setCurrentStep(1);
    onOpenChange(false);
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceedStep1 = () => {
    const title = form.watch("title");
    const stageId = form.watch("stage_id");
    return title && title.length > 0 && stageId && stageId.length > 0;
  };

  const isLastStep = currentStep === totalSteps;

  const steps = [
    { number: 1, title: "Основное", icon: Zap, description: "Название и этап" },
    { number: 2, title: "Детали", icon: DollarSign, description: "Сумма и приоритет" },
    { number: 3, title: "Контакты", icon: Users, description: "Участники сделки" },
  ];

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Добавить новую карточку</DialogTitle>
          <DialogDescription>
            Создайте новую карточку в воронке для отслеживания лида или сделки.
          </DialogDescription>
        </DialogHeader>

        {/* Step Progress Indicator */}
        <div className="relative mt-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = step.number === currentStep;
              const isCompleted = step.number < currentStep;
              
              return (
                <div key={step.number} className="flex-1">
                  <div className="flex flex-col items-center">
                    <div className="flex items-center w-full">
                      {index > 0 && (
                        <div 
                          className={cn(
                            "flex-1 h-0.5 -mt-5",
                            isCompleted || isActive ? "bg-primary" : "bg-muted"
                          )}
                        />
                      )}
                      <div 
                        className={cn(
                          "relative flex h-10 w-10 items-center justify-center rounded-full border-2 transition-all",
                          isActive
                            ? "border-primary bg-primary text-primary-foreground shadow-lg scale-110"
                            : isCompleted
                            ? "border-primary bg-primary text-primary-foreground"
                            : "border-muted bg-background text-muted-foreground"
                        )}
                      >
                        {isCompleted ? (
                          <Check className="h-5 w-5" />
                        ) : (
                          <Icon className="h-5 w-5" />
                        )}
                      </div>
                      {index < steps.length - 1 && (
                        <div 
                          className={cn(
                            "flex-1 h-0.5 -mt-5",
                            isCompleted ? "bg-primary" : "bg-muted"
                          )}
                        />
                      )}
                    </div>
                    <div className="mt-2 text-center">
                      <p className={cn(
                        "text-sm font-medium transition-colors",
                        isActive ? "text-foreground" : "text-muted-foreground"
                      )}>
                        {step.title}
                      </p>
                      <p className="text-xs text-muted-foreground mt-0.5">
                        {step.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mt-6">
            {/* Step 1: Essential Information */}
            {currentStep === 1 && (
              <div className="space-y-4 animate-in fade-in-50 duration-300">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Название карточки *</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Например: Сделка с ООО 'Компания'" 
                          {...field} 
                          autoFocus
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="stage_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Этап воронки *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Выберите этап" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {stages.map((stage) => (
                            <SelectItem key={stage.id} value={stage.id}>
                              {stage.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Описание</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Краткое описание сделки или важные детали" 
                          className="min-h-[80px] resize-none"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Step 2: Enhancement Details */}
            {currentStep === 2 && (
              <div className="space-y-4 animate-in fade-in-50 duration-300">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Сумма сделки</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            placeholder="0"
                            {...field}
                            value={field.value || ""}
                            onChange={(e) => {
                              const value = e.target.value;
                              field.onChange(value ? parseFloat(value) : undefined);
                            }}
                            autoFocus
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Валюта</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Выберите валюту" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                            <SelectItem value="RUB">RUB</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="probability"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Вероятность закрытия (%)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="100" 
                            {...field}
                            onChange={(e) => {
                              const value = parseInt(e.target.value);
                              field.onChange(isNaN(value) ? 0 : Math.min(100, Math.max(0, value)));
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expected_close_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ожидаемая дата закрытия</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Приоритет</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Выберите приоритет" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">Низкий</SelectItem>
                          <SelectItem value="medium">Средний</SelectItem>
                          <SelectItem value="high">Высокий</SelectItem>
                          <SelectItem value="urgent">Срочный</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Step 3: Contacts and Assignment */}
            {currentStep === 3 && (
              <div className="space-y-4 animate-in fade-in-50 duration-300">
                <FormField
                  control={form.control}
                  name="contact_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Контактное лицо</FormLabel>
                      <FormControl>
                        <ContactSelector
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Выберите контакт..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="company_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Компания</FormLabel>
                      <FormControl>
                        <CompanySelector
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Выберите компанию..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="assigned_user_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ответственный менеджер</FormLabel>
                      <FormControl>
                        <UserSelector
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Назначить ответственного..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Quick summary of what will be created */}
                <div className="rounded-lg border bg-muted/50 p-4 space-y-2">
                  <p className="text-sm font-medium">Итоговая информация:</p>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>• <span className="font-medium">Название:</span> {form.watch("title") || "Не указано"}</p>
                    <p>• <span className="font-medium">Этап:</span> {stages.find(s => s.id === form.watch("stage_id"))?.name || "Не выбран"}</p>
                    {form.watch("value") && (
                      <p>• <span className="font-medium">Сумма:</span> {form.watch("value")} {form.watch("currency")}</p>
                    )}
                    <p>• <span className="font-medium">Приоритет:</span> {form.watch("priority") === "low" ? "Низкий" : form.watch("priority") === "medium" ? "Средний" : form.watch("priority") === "high" ? "Высокий" : "Срочный"}</p>
                  </div>
                </div>
              </div>
            )}

            <DialogFooter className="flex justify-between sm:justify-between">
              <div className="flex gap-2">
                {currentStep > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    disabled={createCardMutation.isPending}
                  >
                    <ChevronLeft className="mr-1 h-4 w-4" />
                    Назад
                  </Button>
                )}
              </div>
              
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={createCardMutation.isPending}
                >
                  Отмена
                </Button>
                
                {!isLastStep ? (
                  <Button
                    type="button"
                    onClick={nextStep}
                    disabled={currentStep === 1 && !canProceedStep1()}
                  >
                    Далее
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                ) : (
                  <Button 
                    type="submit" 
                    disabled={createCardMutation.isPending || !canProceedStep1()}
                  >
                    {createCardMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Создать карточку
                  </Button>
                )}
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}