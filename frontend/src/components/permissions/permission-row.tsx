"use client";

import { memo } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { OptimizedCheckbox } from "@/components/ui/optimized-checkbox";
import { Square } from "lucide-react";

interface PermissionRowProps {
  resource: string;
  resourceLabel: string;
  resourceIcon?: React.ReactNode;
  availableActions: string[];
  hasPermission: (resource: string, action: string) => boolean;
  handlePermissionToggle: (permissionId: string, checked: boolean) => void;
  handleResourceToggle: (resource: string, checked: boolean) => void;
  getResourceState: (resource: string) => "none" | "all" | "partial";
  permissions: Array<{
    id: string;
    resource: string;
    action: string;
    description?: string;
  }>;
  isSystemRole: boolean;
  isUpdating: boolean;
}

const PermissionRow = memo(function PermissionRow({
  resource,
  resourceLabel,
  resourceIcon,
  availableActions,
  hasPermission,
  handlePermissionToggle,
  handleResourceToggle,
  getResourceState,
  permissions,
  isSystemRole,
  isUpdating,
}: PermissionRowProps) {
  const state = getResourceState(resource);

  return (
    <TableRow>
      <TableCell>
        <div className="flex items-center gap-2 font-medium">
          {resourceIcon || <Square className="h-4 w-4" />}
          {resourceLabel}
        </div>
      </TableCell>
      {availableActions.map((action) => {
        const permission = permissions.find(p => p.resource === resource && p.action === action);
        return (
          <TableCell key={action} className="text-center">
            <OptimizedCheckbox
              checked={hasPermission(resource, action)}
              disabled={isSystemRole || isUpdating}
              onCheckedChange={(checked) => {
                if (permission) {
                  handlePermissionToggle(permission.id, checked);
                }
              }}
              optimistic={true}
            />
          </TableCell>
        );
      })}
      <TableCell className="text-center">
        <OptimizedCheckbox
          checked={state === "all"}
          indeterminate={state === "partial"}
          disabled={isSystemRole || isUpdating}
          onCheckedChange={(checked) =>
            handleResourceToggle(resource, checked)
          }
          optimistic={true}
        />
      </TableCell>
    </TableRow>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for better performance
  if (prevProps.resource !== nextProps.resource) return false;
  if (prevProps.isSystemRole !== nextProps.isSystemRole) return false;
  if (prevProps.isUpdating !== nextProps.isUpdating) return false;
  if (prevProps.getResourceState(prevProps.resource) !== nextProps.getResourceState(nextProps.resource)) return false;
  
  // Check if any permission state changed for this resource
  for (const action of prevProps.availableActions) {
    if (prevProps.hasPermission(prevProps.resource, action) !== nextProps.hasPermission(nextProps.resource, action)) {
      return false;
    }
  }
  
  return true;
});

export { PermissionRow };