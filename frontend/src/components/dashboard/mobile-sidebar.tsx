"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { useAuth } from "@/providers/auth-provider";
import { useAuthStore } from "@/stores/auth";
import {
  BarChart3,
  Building2,
  ChevronLeft,
  ChevronRight,
  Home,
  Kanban,
  Layers,
  LogOut,
  Menu,
  Settings,
  Users,
  X,
  Bell,
  Search,
} from "lucide-react";

const navigation = [
  { name: "Главная", href: "/dashboard", icon: Home },
  { name: "Воронка", href: "/dashboard/pipeline", icon: Ka<PERSON><PERSON> },
  { name: "Ворон<PERSON><PERSON>", href: "/dashboard/pipelines", icon: Layers },
  { name: "Контакты", href: "/dashboard/contacts", icon: Users },
  { name: "Компании", href: "/dashboard/companies", icon: Building2 },
  { name: "Отчеты", href: "/dashboard/reports", icon: BarChart3 },
  { name: "Настройки", href: "/dashboard/settings", icon: Settings },
];

// Mobile bottom navigation items (most used)
const mobileNavigation = [
  { name: "Главная", href: "/dashboard", icon: Home },
  { name: "Воронка", href: "/dashboard/pipeline", icon: Kanban },
  { name: "Контакты", href: "/dashboard/contacts", icon: Users },
  { name: "Меню", action: "menu", icon: Menu },
];

interface MobileSidebarProps {
  className?: string;
}

export function MobileSidebar({ className }: MobileSidebarProps) {
  const [collapsed, setCollapsed] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const pathname = usePathname();
  const { user } = useAuth();
  const logout = useAuthStore((state) => state.logout);

  // Detect mobile/tablet/desktop
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 1024) {
        setCollapsed(true);
      }
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const handleLogout = () => {
    logout();
  };

  const handleNavClick = (item: any) => {
    if (item.action === "menu") {
      setDrawerOpen(true);
    }
    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  // Mobile Bottom Navigation
  const MobileBottomNav = () => (
    <nav className="md:hidden fixed bottom-0 left-0 right-0 h-16 bg-background/95 backdrop-blur border-t supports-[backdrop-filter]:bg-background/60 z-50">
      <div className="flex h-full">
        {mobileNavigation.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href;
          
          return (
            <button
              key={item.name}
              onClick={() => {
                if (item.action === "menu") {
                  setDrawerOpen(true);
                }
              }}
              className={cn(
                "flex-1 flex flex-col items-center justify-center py-2 px-1",
                "transition-all duration-200 relative",
                "active:scale-95",
                isActive && !item.action
                  ? "text-primary bg-primary/10"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              )}
            >
              {item.href && !item.action ? (
                <Link href={item.href} className="flex flex-col items-center justify-center w-full h-full">
                  <Icon className="h-5 w-5 mb-1" />
                  <span className="text-xs font-medium">{item.name}</span>
                  {isActive && (
                    <span className="absolute top-0 inset-x-0 h-0.5 bg-primary" />
                  )}
                </Link>
              ) : (
                <>
                  <Icon className="h-5 w-5 mb-1" />
                  <span className="text-xs font-medium">{item.name}</span>
                </>
              )}
            </button>
          );
        })}
      </div>
    </nav>
  );

  // Mobile/Tablet Drawer
  const DrawerContent = () => (
    <>
      <SheetHeader className="px-4 pb-4 border-b">
        <SheetTitle className="flex items-center justify-between">
          <span className="text-xl font-bold">CRM Система</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDrawerOpen(false)}
            className="md:hidden"
          >
            <X className="h-5 w-5" />
          </Button>
        </SheetTitle>
      </SheetHeader>

      <nav className="flex-1 space-y-1 px-2 py-4">
        {navigation.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href || pathname.startsWith(item.href + "/");

          return (
            <Link
              key={item.name}
              href={item.href}
              onClick={() => isMobile && setDrawerOpen(false)}
              className={cn(
                "group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200",
                "active:scale-98",
                isActive
                  ? "bg-primary/10 text-primary shadow-sm"
                  : "text-muted-foreground hover:bg-muted hover:text-foreground"
              )}
            >
              <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
              <span>{item.name}</span>
              {isActive && (
                <span className="ml-auto h-2 w-2 rounded-full bg-primary animate-pulse" />
              )}
            </Link>
          );
        })}
      </nav>

      {/* User Profile */}
      <div className="border-t p-4">
        <div className="flex items-center mb-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={user?.avatar_url} alt={user?.first_name} />
            <AvatarFallback className="bg-primary/10">
              {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium">
              {user?.first_name} {user?.last_name}
            </p>
            <p className="text-xs text-muted-foreground">{user?.email}</p>
          </div>
        </div>
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={handleLogout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Выйти
        </Button>
      </div>
    </>
  );

  // Desktop Sidebar
  const DesktopSidebar = () => (
    <div
      className={cn(
        "hidden md:flex flex-col bg-card border-r transition-all duration-300 ease-in-out",
        collapsed ? "w-16" : "w-64",
        className
      )}
    >
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-4 border-b">
        {!collapsed && (
          <h1 className="text-xl font-bold">CRM Система</h1>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCollapsed(!collapsed)}
          className="hover:bg-muted"
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {collapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-2 py-4">
        {navigation.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href || pathname.startsWith(item.href + "/");

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200",
                isActive
                  ? "bg-primary/10 text-primary"
                  : "text-muted-foreground hover:bg-muted hover:text-foreground",
                collapsed && "justify-center"
              )}
              title={collapsed ? item.name : undefined}
            >
              <Icon className={cn("h-5 w-5 flex-shrink-0", !collapsed && "mr-3")} />
              {!collapsed && <span>{item.name}</span>}
              {!collapsed && isActive && (
                <span className="ml-auto h-2 w-2 rounded-full bg-primary animate-pulse" />
              )}
            </Link>
          );
        })}
      </nav>

      {/* User Profile */}
      <div className="border-t p-4">
        <div className={cn("flex items-center", collapsed && "justify-center")}>
          <Avatar className={cn(collapsed ? "h-8 w-8" : "h-10 w-10")}>
            <AvatarImage src={user?.avatar_url} alt={user?.first_name} />
            <AvatarFallback className="bg-primary/10">
              {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}
            </AvatarFallback>
          </Avatar>
          {!collapsed && (
            <div className="ml-3 flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">
                    {user?.first_name} {user?.last_name}
                  </p>
                  <p className="text-xs text-muted-foreground">{user?.email}</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLogout}
                  className="hover:bg-muted"
                  title="Выйти"
                >
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
          {collapsed && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="hover:bg-muted"
              title="Выйти"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <DesktopSidebar />

      {/* Mobile Header */}
      <header className="md:hidden fixed top-0 left-0 right-0 h-14 bg-background/95 backdrop-blur border-b supports-[backdrop-filter]:bg-background/60 z-40 px-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h1 className="font-semibold text-lg">CRM</h1>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
            <Search className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
            <Bell className="h-4 w-4" />
          </Button>
        </div>
      </header>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav />

      {/* Mobile/Tablet Drawer */}
      <Sheet open={drawerOpen} onOpenChange={setDrawerOpen}>
        <SheetContent side="left" className="w-[280px] sm:w-[320px] p-0">
          <DrawerContent />
        </SheetContent>
      </Sheet>
    </>
  );
}