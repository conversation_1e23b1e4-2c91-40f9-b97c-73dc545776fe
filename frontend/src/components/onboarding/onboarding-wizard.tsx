"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  ArrowRight,
  ArrowLeft,
  CheckCircle2,
  Sparkles,
  Users,
  BarChart3,
  Zap,
  Target,
  Rocket,
  X,
} from "lucide-react";
import { useAuthStore } from "@/stores/auth";

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  content: React.ReactNode;
  action?: () => void;
}

export function OnboardingWizard({ onComplete }: { onComplete: () => void }) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const { user } = useAuthStore();

  const steps: OnboardingStep[] = [
    {
      id: "welcome",
      title: `Добро пожаловать, ${user?.first_name}! 🎉`,
      description: "Давайте настроим вашу CRM систему за 3 минуты",
      icon: Sparkles,
      content: (
        <div className="space-y-4">
          <div className="relative h-48 rounded-lg bg-gradient-to-br from-primary/20 to-primary/5 flex items-center justify-center">
            <Rocket className="h-24 w-24 text-primary animate-pulse" />
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Что вас ждет:</h3>
            <ul className="space-y-1">
              <li className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                Настройка воронки продаж
              </li>
              <li className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                Импорт контактов
              </li>
              <li className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                Интеграция с инструментами
              </li>
            </ul>
          </div>
        </div>
      ),
    },
    {
      id: "pipeline",
      title: "Настройте вашу воронку продаж",
      description: "Выберите шаблон или создайте свою",
      icon: Target,
      content: (
        <div className="space-y-4">
          <div className="grid gap-3">
            <Card className="cursor-pointer hover:border-primary transition-colors">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">B2B Продажи</CardTitle>
                <CardDescription className="text-xs">
                  Лид → Квалификация → Предложение → Переговоры → Закрыто
                </CardDescription>
              </CardHeader>
            </Card>
            <Card className="cursor-pointer hover:border-primary transition-colors">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">E-commerce</CardTitle>
                <CardDescription className="text-xs">
                  Посетитель → Корзина → Оплата → Доставка → Завершено
                </CardDescription>
              </CardHeader>
            </Card>
            <Card className="cursor-pointer hover:border-primary transition-colors">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Услуги</CardTitle>
                <CardDescription className="text-xs">
                  Запрос → Консультация → Предложение → Договор → Выполнено
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      ),
    },
    {
      id: "contacts",
      title: "Импортируйте контакты",
      description: "Загрузите существующую базу клиентов",
      icon: Users,
      content: (
        <div className="space-y-4">
          <div className="border-2 border-dashed rounded-lg p-8 text-center space-y-3">
            <Users className="h-12 w-12 mx-auto text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Перетащите файл сюда или нажмите для выбора</p>
              <p className="text-xs text-muted-foreground mt-1">
                Поддерживаются CSV, Excel, Google Contacts
              </p>
            </div>
            <Button variant="outline" size="sm">
              Выбрать файл
            </Button>
          </div>
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <span>или</span>
            <Button variant="link" size="sm" className="h-auto p-0">
              Пропустить и добавить вручную
            </Button>
          </div>
        </div>
      ),
    },
    {
      id: "integrations",
      title: "Подключите интеграции",
      description: "Соедините с вашими любимыми инструментами",
      icon: Zap,
      content: (
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <Card className="cursor-pointer hover:border-primary transition-colors">
              <CardContent className="p-4 flex items-center gap-3">
                <div className="h-8 w-8 rounded bg-blue-500/10 flex items-center justify-center">
                  <span className="text-xs font-bold text-blue-500">G</span>
                </div>
                <span className="text-sm font-medium">Gmail</span>
              </CardContent>
            </Card>
            <Card className="cursor-pointer hover:border-primary transition-colors">
              <CardContent className="p-4 flex items-center gap-3">
                <div className="h-8 w-8 rounded bg-purple-500/10 flex items-center justify-center">
                  <span className="text-xs font-bold text-purple-500">S</span>
                </div>
                <span className="text-sm font-medium">Slack</span>
              </CardContent>
            </Card>
            <Card className="cursor-pointer hover:border-primary transition-colors">
              <CardContent className="p-4 flex items-center gap-3">
                <div className="h-8 w-8 rounded bg-green-500/10 flex items-center justify-center">
                  <span className="text-xs font-bold text-green-500">W</span>
                </div>
                <span className="text-sm font-medium">WhatsApp</span>
              </CardContent>
            </Card>
            <Card className="cursor-pointer hover:border-primary transition-colors">
              <CardContent className="p-4 flex items-center gap-3">
                <div className="h-8 w-8 rounded bg-orange-500/10 flex items-center justify-center">
                  <span className="text-xs font-bold text-orange-500">Z</span>
                </div>
                <span className="text-sm font-medium">Zapier</span>
              </CardContent>
            </Card>
          </div>
        </div>
      ),
    },
    {
      id: "complete",
      title: "Все готово! 🚀",
      description: "Ваша CRM система настроена и готова к работе",
      icon: CheckCircle2,
      content: (
        <div className="space-y-4">
          <div className="relative h-48 rounded-lg bg-gradient-to-br from-green-500/20 to-green-500/5 flex items-center justify-center">
            <CheckCircle2 className="h-24 w-24 text-green-500" />
          </div>
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Что дальше?</h3>
            <div className="space-y-2">
              <Card className="cursor-pointer hover:border-primary transition-colors">
                <CardContent className="p-3 flex items-center gap-3">
                  <BarChart3 className="h-4 w-4 text-primary" />
                  <span className="text-sm">Посмотреть аналитику</span>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:border-primary transition-colors">
                <CardContent className="p-3 flex items-center gap-3">
                  <Users className="h-4 w-4 text-primary" />
                  <span className="text-sm">Пригласить команду</span>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:border-primary transition-colors">
                <CardContent className="p-3 flex items-center gap-3">
                  <Target className="h-4 w-4 text-primary" />
                  <span className="text-sm">Создать первую сделку</span>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const progress = ((currentStep + 1) / steps.length) * 100;
  const currentStepData = steps[currentStep];
  const Icon = currentStepData.icon;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps(new Set([...completedSteps, currentStepData.id]));
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    // Mark onboarding as complete in localStorage
    localStorage.setItem("onboardingCompleted", "true");
    onComplete();
  };

  const handleSkip = () => {
    handleComplete();
  };

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="w-full max-w-2xl"
      >
        <Card className="border-2">
          <CardHeader className="relative pb-3">
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-4 top-4"
              onClick={handleSkip}
            >
              <X className="h-4 w-4" />
            </Button>
            
            <div className="flex items-center gap-3 mb-4">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <Icon className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-xl">{currentStepData.title}</CardTitle>
                <CardDescription>{currentStepData.description}</CardDescription>
              </div>
            </div>
            
            <Progress value={progress} className="h-2" />
            <div className="flex justify-between mt-2">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={cn(
                    "h-2 w-2 rounded-full transition-colors",
                    index === currentStep
                      ? "bg-primary"
                      : index < currentStep
                      ? "bg-primary/50"
                      : "bg-muted"
                  )}
                />
              ))}
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                {currentStepData.content}
              </motion.div>
            </AnimatePresence>
            
            <div className="flex justify-between mt-6">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Назад
              </Button>
              
              <div className="flex gap-2">
                {currentStep < steps.length - 2 && (
                  <Button
                    variant="ghost"
                    onClick={handleSkip}
                  >
                    Пропустить
                  </Button>
                )}
                
                <Button
                  onClick={handleNext}
                  className="gap-2"
                >
                  {currentStep === steps.length - 1 ? (
                    <>
                      Начать работу
                      <Rocket className="h-4 w-4" />
                    </>
                  ) : (
                    <>
                      Далее
                      <ArrowRight className="h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}