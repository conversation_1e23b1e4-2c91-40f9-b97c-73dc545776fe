import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Add the paths that require authentication
const protectedPaths = [
  '/dashboard',
  '/pipeline',
  '/contacts',
  '/companies',
  '/settings',
]

// Add the paths that should redirect to dashboard if user is authenticated
const authPaths = [
  '/auth/login',
  '/auth/register',
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // ВРЕМЕННО ОТКЛЮЧЕНО ДЛЯ ТЕСТИРОВАНИЯ
  // TODO: Исправить сохранение cookies в auth store
  
  // Только редирект с корня
  if (pathname === '/') {
    const url = request.nextUrl.clone()
    url.pathname = '/dashboard'
    return NextResponse.redirect(url)
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}