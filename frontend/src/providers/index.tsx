"use client";

import { ReactNode } from "react";
import { QueryProvider } from "./query-provider";
import { AuthProvider } from "./auth-provider";
import { SSEProvider } from "./sse-provider";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "sonner";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <QueryProvider>
        <AuthProvider>
          <SSEProvider>
            {children}
            <Toaster 
              position="top-right" 
              richColors 
              closeButton
              expand
            />
          </SSEProvider>
        </AuthProvider>
      </QueryProvider>
    </ThemeProvider>
  );
}

// Re-export individual providers and hooks
export { QueryProvider } from "./query-provider";
export { AuthProvider, useAuth } from "./auth-provider";
export { SSEProvider, useSSE } from "./sse-provider";