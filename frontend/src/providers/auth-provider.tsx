"use client";

import { createContext, useContext, useEffect } from "react";
import { useAuthStore } from "@/stores/auth";
import { User } from "@/types";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  hasHydrated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user, isAuthenticated, isLoading, error, getCurrentUser, token, _hasHydrated } = useAuthStore();

  useEffect(() => {
    // Wait for store to hydrate before checking authentication
    if (!_hasHydrated) return;
    
    // If we have a token after hydration but no user, fetch user data
    if (token && isAuthenticated && !user && !isLoading) {
      getCurrentUser();
    }
  }, [_hasHydrated, token, isAuthenticated, user, isLoading, getCurrentUser]);

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    error,
    hasHydrated: _hasHydrated,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}