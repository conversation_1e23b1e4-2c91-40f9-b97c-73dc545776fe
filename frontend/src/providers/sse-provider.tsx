"use client";

import { createContext, useContext, useEffect, ReactNode } from "react";
import { useSSEConnection } from "@/lib/sse/hooks";
import { useAuth } from "./auth-provider";

interface SSEContextType {
  connectionState: "connecting" | "connected" | "disconnected" | "error";
  isConnected: boolean;
  reconnect: () => void;
}

const SSEContext = createContext<SSEContextType | undefined>(undefined);

export function SSEProvider({ children }: { children: ReactNode }) {
  const { isAuthenticated } = useAuth();
  const { connectionState, isConnected, reconnect } = useSSEConnection();

  // Only maintain SSE connection when authenticated
  useEffect(() => {
    if (!isAuthenticated && isConnected) {
      // Disconnect SSE when user logs out
      // This is handled by the SSE client's disconnect method
    }
  }, [isAuthenticated, isConnected]);

  const contextValue: SSEContextType = {
    connectionState,
    isConnected,
    reconnect,
  };

  return (
    <SSEContext.Provider value={contextValue}>
      {children}
    </SSEContext.Provider>
  );
}

export function useSSE() {
  const context = useContext(SSEContext);
  if (context === undefined) {
    throw new Error("useSSE must be used within an SSEProvider");
  }
  return context;
}