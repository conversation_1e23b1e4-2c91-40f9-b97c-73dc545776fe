// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// User types
export interface User extends BaseEntity {
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  is_active: boolean;
  last_login_at?: string;
  avatar_url?: string;
}

export type UserRole = "admin" | "manager" | "user";

export interface CreateUserRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  role: UserRole;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  expires_at: string;
}

// Field Definition types
export interface FieldDefinition extends BaseEntity {
  name: string;
  label: string;
  field_type: FieldType;
  entity_type: EntityType;
  is_required: boolean;
  is_system: boolean;
  options?: string[];
  default_value?: any;
  validation_rules?: Record<string, any>;
  display_order: number;
}

export type FieldType = 
  | "text"
  | "textarea" 
  | "number"
  | "email"
  | "phone"
  | "url"
  | "date"
  | "datetime"
  | "select"
  | "multiselect"
  | "checkbox"
  | "radio"
  | "file"
  | "currency"
  | "jsonb";

export type EntityType = "card" | "contact" | "company";

// Pipeline types
export interface Pipeline extends BaseEntity {
  name: string;
  description?: string;
  is_active: boolean;
  stages: Stage[];
}

export interface Stage extends BaseEntity {
  name: string;
  description?: string;
  color: string;
  position: number;
  pipeline_id: string;
  is_closed: boolean;
  cards?: Card[];
}

// Card types
export interface Card extends BaseEntity {
  title: string;
  description?: string;
  value?: number;
  currency: string;
  probability: number;
  priority: CardPriority;
  expected_close_date?: string;
  actual_close_date?: string;
  stage_id: string;
  pipeline_id: string;
  assigned_user_id?: string;
  contact_id?: string;
  company_id?: string;
  position: number;
  custom_fields: Record<string, any>;
  tags: Tag[];
  attachments: Attachment[];
  activities: Activity[];
  stage?: Stage;
  assigned_user?: User;
  contact?: Contact;
  company?: Company;
}

export interface CreateCardRequest {
  title: string;
  description?: string;
  value?: number;
  currency?: string;
  probability?: number;
  priority?: CardPriority;
  expected_close_date?: string;
  stage_id: string;
  assigned_user_id?: string;
  contact_id?: string;
  company_id?: string;
  custom_fields?: Record<string, any>;
  tag_ids?: string[];
}

export interface UpdateCardRequest extends Partial<CreateCardRequest> {
  id: string;
}

export interface MoveCardRequest {
  card_id: string;
  stage_id: string;
  position: number;
}

// Contact types
export interface Contact extends BaseEntity {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  job_title?: string;
  company_id?: string;
  custom_fields: Record<string, any>;
  tags: Tag[];
  company?: Company;
}

export interface CreateContactRequest {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  job_title?: string;
  company_id?: string;
  custom_fields?: Record<string, any>;
  tag_ids?: string[];
}

// Company types
export interface Company extends BaseEntity {
  name: string;
  website?: string;
  industry?: string;
  size?: CompanySize;
  address?: string;
  phone?: string;
  email?: string;
  custom_fields: Record<string, any>;
  tags: Tag[];
  contacts?: Contact[];
}

export type CompanySize = "1-10" | "11-50" | "51-200" | "201-1000" | "1000+";

export type CardPriority = "low" | "medium" | "high" | "urgent";

export interface CreateCompanyRequest {
  name: string;
  website?: string;
  industry?: string;
  size?: CompanySize;
  address?: string;
  phone?: string;
  email?: string;
  custom_fields?: Record<string, any>;
  tag_ids?: string[];
}

// Tag types
export interface Tag extends BaseEntity {
  name: string;
  color: string;
  entity_type: EntityType;
}

// Activity types
export interface Activity extends BaseEntity {
  type: ActivityType;
  title: string;
  description?: string;
  card_id?: string;
  contact_id?: string;
  company_id?: string;
  user_id: string;
  metadata?: Record<string, any>;
  user?: User;
}

export type ActivityType = 
  | "note"
  | "email"
  | "call"
  | "meeting"
  | "task"
  | "stage_changed"
  | "card_created"
  | "card_updated"
  | "contact_created"
  | "contact_updated"
  | "company_created"
  | "company_updated";

// Attachment types
export interface Attachment extends BaseEntity {
  filename: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  url: string;
  card_id?: string;
  contact_id?: string;
  company_id?: string;
  uploaded_by_user_id: string;
}

// Webhook types
export interface Webhook extends BaseEntity {
  url: string;
  events: WebhookEvent[];
  is_active: boolean;
  secret?: string;
  headers?: Record<string, string>;
}

export type WebhookEvent = 
  | "card.created"
  | "card.updated"
  | "card.moved"
  | "contact.created"
  | "contact.updated"
  | "company.created"
  | "company.updated";

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}

// Search and filter types
export interface SearchParams {
  q?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_direction?: "asc" | "desc";
}

export interface CardFilters extends SearchParams {
  stage_id?: string;
  pipeline_id?: string;
  assigned_user_id?: string;
  contact_id?: string;
  company_id?: string;
  priority?: CardPriority[];
  min_value?: number;
  max_value?: number;
  date_from?: string;
  date_to?: string;
  tag_ids?: string[];
}

// Form types
export interface FormFieldError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: Record<string, string>;
  isLoading: boolean;
  isSubmitting: boolean;
}

// SSE Event types
export interface SSEEvent {
  id: string;
  event: string;
  data: any;
  timestamp: string;
}

export interface CardUpdatedEvent extends SSEEvent {
  event: "card_updated";
  data: {
    card: Card;
    changes: Record<string, any>;
  };
}

export interface CardMovedEvent extends SSEEvent {
  event: "card_moved";
  data: {
    card: Card;
    from_stage_id: string;
    to_stage_id: string;
    position: number;
  };
}

// Dashboard types
export interface DashboardStats {
  total_cards: number;
  active_cards: number;
  closed_won_cards: number;
  closed_lost_cards: number;
  overdue_cards: number;
  total_revenue: number;
  expected_revenue: number;
  conversion_rate: number;
  avg_deal_size: number;
  total_pipelines: number;
  total_stages: number;
  cards_by_stage: Array<{
    stage_id: string;
    stage_name: string;
    card_count: number;
    total_value: number;
  }>;
  cards_by_pipeline: Array<{
    pipeline_id: string;
    pipeline_name: string;
    card_count: number;
    total_value: number;
  }>;
  recent_activities: Activity[];
  top_performers: Array<{
    user_id: string;
    user_name: string;
    cards_count: number;
    total_revenue: number;
    win_rate: number;
  }>;
  revenue_by_month: Array<{
    month: string;
    revenue: number;
    count: number;
  }>;
}

// Drag and Drop types
export interface DragEndEvent {
  active: {
    id: string;
    data: {
      current: Card;
    };
  };
  over: {
    id: string;
    data?: {
      current: {
        stage_id: string;
      };
    };
  };
}

export interface DroppableData {
  stage_id: string;
}

export interface DraggableData {
  card: Card;
}

// Global Search types
export interface GlobalSearchResult {
  type: 'card' | 'contact' | 'company';
  id: string;
  title: string;
  subtitle?: string;
  metadata?: Record<string, any>;
}

export interface CreateActivityRequest {
  type: ActivityType;
  title: string;
  description?: string;
  card_id?: string;
  contact_id?: string;
  company_id?: string;
  metadata?: Record<string, any>;
}