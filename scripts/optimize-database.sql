-- CRM Database Performance Optimization Script
-- Run these optimizations to improve query performance

-- Enable timing to measure impact
\timing

-- 1. CRITICAL INDEXES FOR CARDS TABLE
-- These indexes will dramatically improve dashboard and pipeline queries

-- Cards by stage with soft delete consideration
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_stage_deleted_created 
ON cards (stage_id, deleted_at, created_at DESC) 
WHERE deleted_at IS NULL;

-- Cards by assigned user for user performance queries  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_assigned_user_created
ON cards (assigned_to_id, created_at DESC)
WHERE assigned_to_id IS NOT NULL AND deleted_at IS NULL;

-- Cards by company for company detail views
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_company_created
ON cards (company_id, created_at DESC)
WHERE company_id IS NOT NULL AND deleted_at IS NULL;

-- Cards by contact for contact detail views
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_contact_created  
ON cards (contact_id, created_at DESC)
WHERE contact_id IS NOT NULL AND deleted_at IS NULL;

-- JSONB custom fields optimization - CRITICAL for search performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_custom_fields_gin
ON cards USING GIN (custom_fields)
WHERE custom_fields IS NOT NULL;

-- Cards value range queries (for dashboard statistics)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_value_stage
ON cards (value, stage_id) 
WHERE value IS NOT NULL AND deleted_at IS NULL;

-- Expected close date for overdue cards
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_expected_close_active
ON cards (expected_close_date)
WHERE actual_close_date IS NULL AND deleted_at IS NULL;

-- 2. CONTACTS TABLE OPTIMIZATIONS

-- Full-text search optimization for contacts
-- First create a computed column for full-text search
ALTER TABLE contacts 
ADD COLUMN IF NOT EXISTS search_vector tsvector 
GENERATED ALWAYS AS (
    to_tsvector('english', 
        coalesce(first_name, '') || ' ' || 
        coalesce(last_name, '') || ' ' || 
        coalesce(email, '') || ' ' ||
        coalesce(phone, '') || ' ' ||
        coalesce(job_title, '')
    )
) STORED;

-- Index for full-text search
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contacts_search_vector
ON contacts USING GIN (search_vector);

-- Contacts by company with creation time  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contacts_company_created
ON contacts (company_id, created_at DESC)
WHERE company_id IS NOT NULL;

-- Email uniqueness with partial index (faster lookups)
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_contacts_email_unique
ON contacts (email)
WHERE email IS NOT NULL;

-- JSONB custom fields for contacts
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contacts_custom_fields_gin
ON contacts USING GIN (custom_fields)
WHERE custom_fields IS NOT NULL;

-- 3. ACTIVITIES TABLE OPTIMIZATIONS

-- Activities by entity (cards, contacts, companies) with recent-first ordering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_entity_created
ON activities (entity_type, entity_id, created_at DESC);

-- Activities by type for dashboard statistics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_type_created
ON activities (type, created_at DESC);

-- Activities by user for user activity tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_user_created
ON activities (user_id, created_at DESC)
WHERE user_id IS NOT NULL;

-- 4. STAGES TABLE OPTIMIZATIONS

-- Stages by pipeline with sort order
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stages_pipeline_sort
ON stages (pipeline_id, sort_order)
WHERE is_active = true;

-- Closed won/lost stages for quick lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stages_closed_status
ON stages (is_closed_won, is_closed_lost, pipeline_id)
WHERE is_active = true;

-- 5. PIPELINES TABLE OPTIMIZATIONS

-- Active pipelines with sort order
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pipelines_active_sort
ON pipelines (is_active, sort_order)
WHERE is_active = true;

-- 6. COMPANIES TABLE OPTIMIZATIONS

-- Company name search with trigram similarity
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Trigram index for fuzzy name matching
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_name_trgm
ON companies USING GIN (name gin_trgm_ops);

-- Companies by industry for filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_industry
ON companies (industry)
WHERE industry IS NOT NULL;

-- Company custom fields
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_custom_fields_gin
ON companies USING GIN (custom_fields)
WHERE custom_fields IS NOT NULL;

-- 7. PERFORMANCE MONITORING QUERIES

-- Create a view for monitoring slow queries
CREATE OR REPLACE VIEW v_query_performance AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins + n_tup_upd + n_tup_del as total_ops,
    n_tup_ins as inserts,
    n_tup_upd as updates, 
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
ORDER BY total_ops DESC;

-- 8. DATABASE CONFIGURATION RECOMMENDATIONS

-- Display current database configuration that affects performance
SELECT name, setting, unit, context, short_desc 
FROM pg_settings 
WHERE name IN (
    'shared_buffers',
    'effective_cache_size', 
    'maintenance_work_mem',
    'checkpoint_completion_target',
    'wal_buffers',
    'default_statistics_target',
    'random_page_cost',
    'effective_io_concurrency',
    'work_mem',
    'max_connections'
)
ORDER BY name;

-- 9. ANALYZE TABLES TO UPDATE STATISTICS
-- Run after creating indexes to ensure query planner uses them

ANALYZE cards;
ANALYZE contacts;  
ANALYZE companies;
ANALYZE activities;
ANALYZE stages;
ANALYZE pipelines;

-- 10. VACUUM TABLES TO RECLAIM SPACE AND UPDATE VISIBILITY MAP
-- Run during maintenance window

-- VACUUM ANALYZE cards;
-- VACUUM ANALYZE contacts;
-- VACUUM ANALYZE companies;  
-- VACUUM ANALYZE activities;

-- 11. CREATE OPTIMIZED DASHBOARD QUERY
-- Replace the multiple queries in dashboard_handler.go with this single query

CREATE OR REPLACE VIEW v_dashboard_stats AS
WITH card_stats AS (
    SELECT 
        COUNT(*) as total_cards,
        COUNT(*) FILTER (WHERE c.deleted_at IS NULL) as active_cards,
        COUNT(*) FILTER (WHERE s.is_closed_won = true) as closed_won_cards,
        COUNT(*) FILTER (WHERE s.is_closed_lost = true) as closed_lost_cards,
        COUNT(*) FILTER (WHERE c.expected_close_date < NOW() AND c.deleted_at IS NULL AND s.is_closed_won = false AND s.is_closed_lost = false) as overdue_cards,
        COALESCE(SUM(c.value) FILTER (WHERE s.is_closed_won = true), 0) as total_revenue,
        COALESCE(SUM(c.value * s.probability / 100.0) FILTER (WHERE s.is_closed_won = false AND s.is_closed_lost = false AND c.deleted_at IS NULL), 0) as expected_revenue
    FROM cards c
    JOIN stages s ON c.stage_id = s.id
),
pipeline_stats AS (
    SELECT 
        COUNT(*) FILTER (WHERE is_active = true) as total_pipelines
    FROM pipelines
),
stage_stats AS (
    SELECT 
        COUNT(*) FILTER (WHERE is_active = true) as total_stages
    FROM stages
)
SELECT 
    cs.*,
    ps.total_pipelines,
    ss.total_stages,
    CASE 
        WHEN cs.total_cards > 0 THEN (cs.closed_won_cards::float / cs.total_cards::float * 100)
        ELSE 0 
    END as conversion_rate,
    CASE 
        WHEN cs.closed_won_cards > 0 THEN (cs.total_revenue / cs.closed_won_cards)
        ELSE 0 
    END as avg_deal_size
FROM card_stats cs
CROSS JOIN pipeline_stats ps  
CROSS JOIN stage_stats ss;

-- 12. INDEX USAGE MONITORING
-- Create a view to monitor index usage

CREATE OR REPLACE VIEW v_index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE' 
        ELSE 'ACTIVE'
    END as usage_status
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 13. QUERY TO FIND MISSING INDEXES
-- This query helps identify potentially missing indexes

SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    CASE 
        WHEN seq_scan > idx_scan AND seq_tup_read > 1000 THEN 'NEEDS_INDEX'
        ELSE 'OK'
    END as recommendation
FROM pg_stat_user_tables
WHERE seq_scan > idx_scan 
AND seq_tup_read > 1000
ORDER BY seq_tup_read DESC;

-- Performance Improvement Notes:
-- 1. Run CONCURRENTLY index creation during off-peak hours
-- 2. Monitor pg_stat_progress_create_index for large indexes
-- 3. Consider partitioning cards table if > 1M records expected  
-- 4. Use connection pooling (PgBouncer) for production
-- 5. Set appropriate work_mem for complex queries (default 4MB may be low)
-- 6. Consider read replicas for dashboard queries in high-traffic scenarios

\echo 'Database optimization script completed!'
\echo 'Run: SELECT * FROM v_dashboard_stats; to test the optimized dashboard query'
\echo 'Run: SELECT * FROM v_query_performance; to monitor table activity'  
\echo 'Run: SELECT * FROM v_index_usage; to monitor index usage'