package main

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"os"
)

func main() {
	fmt.Println("🔐 JWT Secret Generator for CRM System")
	fmt.Println("=====================================")

	// Generate a secure JWT secret
	secret, err := generateSecureJWTSecret()
	if err != nil {
		fmt.Printf("❌ Error generating JWT secret: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("\n✅ Generated secure JWT secret:")
	fmt.Printf("\n%s\n\n", secret)

	fmt.Println("📋 To use this secret, set it as an environment variable:")
	fmt.Printf("export CRM_AUTH_JWT_SECRET=\"%s\"\n\n", secret)

	fmt.Println("Or add it to your .env file:")
	fmt.Printf("CRM_AUTH_JWT_SECRET=%s\n\n", secret)

	fmt.Println("⚠️  IMPORTANT SECURITY NOTES:")
	fmt.Println("1. Never commit this secret to version control")
	fmt.Println("2. Store it securely (environment variable, secrets manager)")
	fmt.Println("3. Rotate the secret periodically")
	fmt.Println("4. Use different secrets for different environments")
	fmt.Println("5. This secret should be at least 32 characters long")
}

// generateSecureJWTSecret generates a cryptographically secure JWT secret
func generateSecureJWTSecret() (string, error) {
	// Generate 64 bytes of random data for extra security
	bytes := make([]byte, 64)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Encode to base64 for a string representation
	// This will produce approximately 86 characters
	return base64.URLEncoding.EncodeToString(bytes), nil
}
