#!/bin/bash

# CRM Security Setup Script
# This script helps set up critical security configurations

set -e

echo "🔒 CRM Security Setup"
echo "===================="
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if .env file exists
ENV_FILE=".env"
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${YELLOW}Creating .env file...${NC}"
    touch $ENV_FILE
fi

# Function to check if a variable is set in .env
check_env_var() {
    if grep -q "^$1=" "$ENV_FILE"; then
        return 0
    else
        return 1
    fi
}

# 1. JWT Secret Setup
echo "1️⃣  Checking JWT Secret..."
if check_env_var "CRM_AUTH_JWT_SECRET"; then
    echo -e "${GREEN}✓ JWT Secret is already configured${NC}"
else
    echo -e "${YELLOW}⚠️  JWT Secret not found. Generating secure secret...${NC}"
    
    # Generate JWT secret using Go script
    if command -v go &> /dev/null; then
        JWT_SECRET=$(go run scripts/generate-jwt-secret.go 2>/dev/null | grep -A1 "Generated secure JWT secret:" | tail -1 | tr -d ' ')
        
        if [ ! -z "$JWT_SECRET" ]; then
            echo "CRM_AUTH_JWT_SECRET=$JWT_SECRET" >> $ENV_FILE
            echo -e "${GREEN}✓ JWT Secret generated and saved to .env${NC}"
        else
            # Fallback to openssl
            JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
            echo "CRM_AUTH_JWT_SECRET=$JWT_SECRET" >> $ENV_FILE
            echo -e "${GREEN}✓ JWT Secret generated (using openssl) and saved to .env${NC}"
        fi
    else
        # Fallback to openssl
        JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
        echo "CRM_AUTH_JWT_SECRET=$JWT_SECRET" >> $ENV_FILE
        echo -e "${GREEN}✓ JWT Secret generated (using openssl) and saved to .env${NC}"
    fi
fi

# 2. Database Configuration
echo ""
echo "2️⃣  Checking Database Configuration..."
if check_env_var "CRM_DATABASE_PASSWORD"; then
    echo -e "${GREEN}✓ Database password is configured${NC}"
else
    echo -e "${YELLOW}Using default database configuration${NC}"
    echo "CRM_DATABASE_HOST=localhost" >> $ENV_FILE
    echo "CRM_DATABASE_PORT=5432" >> $ENV_FILE
    echo "CRM_DATABASE_USER=postgres" >> $ENV_FILE
    echo "CRM_DATABASE_PASSWORD=postgres" >> $ENV_FILE
    echo "CRM_DATABASE_NAME=crm" >> $ENV_FILE
fi

# 3. Redis Configuration
echo ""
echo "3️⃣  Checking Redis Configuration..."
if check_env_var "CRM_REDIS_HOST"; then
    echo -e "${GREEN}✓ Redis is configured${NC}"
else
    echo -e "${YELLOW}Setting up Redis configuration...${NC}"
    echo "CRM_REDIS_HOST=localhost" >> $ENV_FILE
    echo "CRM_REDIS_PORT=6379" >> $ENV_FILE
    echo "CRM_REDIS_PASSWORD=" >> $ENV_FILE
    echo "CRM_REDIS_DB=0" >> $ENV_FILE
    echo -e "${GREEN}✓ Redis configuration added to .env${NC}"
fi

# 4. CORS Configuration
echo ""
echo "4️⃣  Setting up CORS Configuration..."
if check_env_var "CRM_CORS_ALLOWED_ORIGINS"; then
    echo -e "${GREEN}✓ CORS is configured${NC}"
else
    echo "CRM_CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001" >> $ENV_FILE
    echo -e "${GREEN}✓ CORS configuration added${NC}"
fi

# 5. Rate Limiting Configuration
echo ""
echo "5️⃣  Setting up Rate Limiting..."
if check_env_var "CRM_RATE_LIMIT_MAX"; then
    echo -e "${GREEN}✓ Rate limiting is configured${NC}"
else
    echo "CRM_RATE_LIMIT_MAX=100" >> $ENV_FILE
    echo "CRM_RATE_LIMIT_WINDOW=60" >> $ENV_FILE
    echo -e "${GREEN}✓ Rate limiting configuration added${NC}"
fi

# 6. Create .env.example
echo ""
echo "6️⃣  Creating .env.example file..."
cat > .env.example << 'EOF'
# Authentication
CRM_AUTH_JWT_SECRET=your-secure-jwt-secret-here
CRM_AUTH_TOKEN_DURATION=168

# Database
CRM_DATABASE_HOST=localhost
CRM_DATABASE_PORT=5432
CRM_DATABASE_USER=postgres
CRM_DATABASE_PASSWORD=your-database-password
CRM_DATABASE_NAME=crm
CRM_DATABASE_SSLMODE=disable
CRM_DATABASE_TIMEZONE=UTC

# Redis (for caching and token blacklisting)
CRM_REDIS_HOST=localhost
CRM_REDIS_PORT=6379
CRM_REDIS_PASSWORD=
CRM_REDIS_DB=0

# Server
CRM_SERVER_PORT=8080
CRM_SERVER_HOST=0.0.0.0

# CORS
CRM_CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate Limiting
CRM_RATE_LIMIT_MAX=100
CRM_RATE_LIMIT_WINDOW=60

# File Storage
CRM_STORAGE_UPLOAD_PATH=./uploads
CRM_STORAGE_MAX_SIZE=10485760
EOF

echo -e "${GREEN}✓ .env.example created${NC}"

# 7. Add .env to .gitignore
echo ""
echo "7️⃣  Updating .gitignore..."
if [ -f ".gitignore" ]; then
    if ! grep -q "^\.env$" .gitignore; then
        echo ".env" >> .gitignore
        echo -e "${GREEN}✓ Added .env to .gitignore${NC}"
    else
        echo -e "${GREEN}✓ .env already in .gitignore${NC}"
    fi
else
    echo ".env" > .gitignore
    echo ".env.local" >> .gitignore
    echo "*.key" >> .gitignore
    echo "*.pem" >> .gitignore
    echo -e "${GREEN}✓ Created .gitignore with security patterns${NC}"
fi

# 8. Check Redis availability
echo ""
echo "8️⃣  Checking Redis availability..."
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo -e "${GREEN}✓ Redis is running${NC}"
    else
        echo -e "${YELLOW}⚠️  Redis is not running. Start it with: redis-server${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Redis CLI not found. Install Redis for token blacklisting and caching${NC}"
fi

# 9. Final Security Checklist
echo ""
echo "📋 Security Checklist:"
echo "====================="

# Check JWT secret strength
if check_env_var "CRM_AUTH_JWT_SECRET"; then
    JWT_LENGTH=$(grep "^CRM_AUTH_JWT_SECRET=" "$ENV_FILE" | cut -d'=' -f2 | wc -c)
    if [ $JWT_LENGTH -ge 32 ]; then
        echo -e "${GREEN}✓ JWT Secret is strong (${JWT_LENGTH} characters)${NC}"
    else
        echo -e "${RED}✗ JWT Secret is too short (${JWT_LENGTH} characters, minimum 32 recommended)${NC}"
    fi
fi

# Check if default passwords are being used
if grep -q "CRM_DATABASE_PASSWORD=postgres" "$ENV_FILE"; then
    echo -e "${YELLOW}⚠️  Using default database password (change for production)${NC}"
else
    echo -e "${GREEN}✓ Custom database password configured${NC}"
fi

# Additional recommendations
echo ""
echo "🔐 Security Recommendations:"
echo "============================"
echo "1. Change all default passwords before production"
echo "2. Use environment-specific .env files (.env.production, .env.staging)"
echo "3. Consider using a secrets management service (AWS Secrets Manager, HashiCorp Vault)"
echo "4. Enable SSL/TLS for database connections in production"
echo "5. Regularly rotate JWT secrets and API keys"
echo "6. Set up monitoring and alerting for security events"
echo "7. Enable audit logging for sensitive operations"
echo ""
echo -e "${GREEN}✅ Security setup complete!${NC}"
echo ""
echo "To start the application with these settings:"
echo "  make dev    # For development"
echo "  make prod   # For production"