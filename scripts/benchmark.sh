#!/bin/bash

# CRM Performance Benchmarking Script
# This script tests various performance aspects of the CRM system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
API_BASE="http://localhost:8080"
FRONTEND_URL="http://localhost:3000"
CONCURRENT_USERS=10
TEST_DURATION=30s

echo -e "${GREEN}=== CRM Performance Benchmark Suite ===${NC}"
echo "API Base: $API_BASE"
echo "Frontend: $FRONTEND_URL"
echo "Test Duration: $TEST_DURATION"
echo "Concurrent Users: $CONCURRENT_USERS"
echo

# Function to check if service is running
check_service() {
    local url=$1
    local name=$2
    echo -n "Checking $name... "
    if curl -s --max-time 5 "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}OK${NC}"
        return 0
    else
        echo -e "${RED}FAILED${NC}"
        return 1
    fi
}

# Function to measure response time
measure_response_time() {
    local url=$1
    local headers=$2
    curl -o /dev/null -s -w "Connect: %{time_connect}s, Start Transfer: %{time_starttransfer}s, Total: %{time_total}s, Size: %{size_download} bytes\n" $headers "$url"
}

# Function to run load test with wrk (if available)
load_test() {
    local url=$1
    local name=$2
    local headers=$3
    
    echo -e "\n${YELLOW}Load Testing: $name${NC}"
    
    if command -v wrk >/dev/null 2>&1; then
        wrk -t4 -c$CONCURRENT_USERS -d$TEST_DURATION $headers "$url"
    elif command -v ab >/dev/null 2>&1; then
        ab -n 1000 -c $CONCURRENT_USERS $headers "$url"
    else
        echo "wrk or ab not available, skipping load test"
        # Simple alternative using curl in background
        echo "Running simple concurrent test..."
        for i in $(seq 1 5); do
            curl -s -o /dev/null -w "Request $i: %{time_total}s\n" "$url" &
        done
        wait
    fi
}

# Main benchmark function
run_benchmarks() {
    echo -e "\n${GREEN}1. Service Health Checks${NC}"
    check_service "$API_BASE/health" "Backend API"
    check_service "$FRONTEND_URL" "Frontend"
    
    echo -e "\n${GREEN}2. Database Connection Test${NC}"
    measure_response_time "$API_BASE/health" ""
    
    echo -e "\n${GREEN}3. Static Endpoints Performance${NC}"
    echo "Health endpoint:"
    measure_response_time "$API_BASE/health" ""
    
    echo -e "\n${GREEN}4. Frontend Bundle Analysis${NC}"
    if [ -d "./frontend/.next" ]; then
        echo "Next.js build analysis:"
        find ./frontend/.next/static -name "*.js" -exec ls -lh {} \; | head -10
        echo
        echo "Total bundle size:"
        du -sh ./frontend/.next/static 2>/dev/null || echo "Build not found"
    else
        echo "Frontend not built. Run 'npm run build' in frontend directory first."
    fi
    
    echo -e "\n${GREEN}5. Memory Usage Analysis${NC}"
    # Check if Go backend is running
    if pgrep -f "go run\|main" > /dev/null; then
        echo "Go backend memory usage:"
        ps aux | grep -E "(go run|main)" | grep -v grep | awk '{print "PID: " $2 ", Memory: " $6/1024 " MB"}'
    else
        echo "Go backend not running in development mode"
    fi
    
    # Check Node.js processes (Next.js)
    if pgrep -f "node.*next" > /dev/null; then
        echo "Next.js memory usage:"
        ps aux | grep "node.*next" | grep -v grep | awk '{print "PID: " $2 ", Memory: " $6/1024 " MB"}'
    else
        echo "Next.js not running"
    fi
    
    echo -e "\n${GREEN}6. Database Performance${NC}"
    if command -v psql >/dev/null 2>&1; then
        echo "Running database queries..."
        PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c "
        SELECT 
            schemaname,
            tablename,
            n_tup_ins as inserts,
            n_tup_upd as updates,
            n_tup_del as deletes,
            n_tup_ins + n_tup_upd + n_tup_del as total_ops
        FROM pg_stat_user_tables 
        ORDER BY total_ops DESC 
        LIMIT 10;" 2>/dev/null || echo "Cannot connect to database"
    else
        echo "psql not available for database testing"
    fi
    
    echo -e "\n${GREEN}7. File System Performance${NC}"
    if [ -d "./uploads" ]; then
        echo "Upload directory size:"
        du -sh ./uploads 2>/dev/null || echo "No uploads directory"
    fi
    
    echo -e "\n${GREEN}8. Network Performance Tests${NC}"
    echo "Testing API endpoints response times..."
    
    # Test various endpoints if they're available
    endpoints=(
        "/health"
        "/api/v1/auth/login"
    )
    
    for endpoint in "${endpoints[@]}"; do
        echo "Testing $endpoint:"
        if curl -s --max-time 5 "$API_BASE$endpoint" > /dev/null 2>&1; then
            measure_response_time "$API_BASE$endpoint" ""
        else
            echo "Endpoint not available or requires authentication"
        fi
    done
}

# Performance recommendations
generate_recommendations() {
    echo -e "\n${GREEN}9. Performance Recommendations${NC}"
    
    # Check Go version
    if command -v go >/dev/null 2>&1; then
        echo "Go version: $(go version)"
        if [[ $(go version | grep -oE '[0-9]+\.[0-9]+' | head -1) < "1.21" ]]; then
            echo "⚠️  Consider upgrading Go to 1.21+ for better performance"
        fi
    fi
    
    # Check Node.js version
    if command -v node >/dev/null 2>&1; then
        echo "Node.js version: $(node --version)"
        if [[ $(node --version | grep -oE '[0-9]+' | head -1) -lt 18 ]]; then
            echo "⚠️  Consider upgrading Node.js to 18+ for better performance"
        fi
    fi
    
    # Check for common performance issues
    echo -e "\n${YELLOW}Backend Performance Checks:${NC}"
    
    # Check for database indexes
    if [ -f "./migrations" ] || [ -d "./migrations" ]; then
        echo "✓ Migrations directory exists"
    else
        echo "⚠️  No migrations directory found"
    fi
    
    # Check for Redis usage
    if pgrep redis > /dev/null; then
        echo "✓ Redis is running (good for caching)"
    else
        echo "⚠️  Redis not running - consider enabling for caching"
    fi
    
    echo -e "\n${YELLOW}Frontend Performance Checks:${NC}"
    
    # Check for build optimization
    if [ -f "./frontend/.next/build-manifest.json" ]; then
        echo "✓ Next.js build exists"
    else
        echo "⚠️  Run 'npm run build' for production optimization"
    fi
    
    # Check bundle size
    if [ -d "./frontend/.next/static" ]; then
        bundle_size=$(du -sb ./frontend/.next/static 2>/dev/null | cut -f1)
        if [ "$bundle_size" -gt 5242880 ]; then # 5MB
            echo "⚠️  Bundle size is large (>5MB) - consider code splitting"
        else
            echo "✓ Bundle size is reasonable"
        fi
    fi
}

# Main execution
main() {
    echo "Starting CRM Performance Benchmark..."
    echo "Timestamp: $(date)"
    echo
    
    run_benchmarks
    generate_recommendations
    
    echo -e "\n${GREEN}Benchmark completed!${NC}"
    echo "For detailed analysis, check the generated performance-metrics.json file"
}

# Run if called directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi