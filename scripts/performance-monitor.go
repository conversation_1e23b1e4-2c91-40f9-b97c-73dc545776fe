package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"runtime"
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// PerformanceMetrics holds system performance data
type PerformanceMetrics struct {
	Timestamp          time.Time     `json:"timestamp"`
	Memory             MemoryStats   `json:"memory"`
	Database           DatabaseStats `json:"database"`
	API                APIStats      `json:"api"`
	SystemLoad         SystemStats   `json:"system_load"`
	RecommendedActions []string      `json:"recommended_actions"`
}

type MemoryStats struct {
	AllocMB      float64 `json:"alloc_mb"`
	TotalAllocMB float64 `json:"total_alloc_mb"`
	SysMB        float64 `json:"sys_mb"`
	NumGC        uint32  `json:"num_gc"`
	GCCPUPercent float64 `json:"gc_cpu_percent"`
	HeapObjects  uint64  `json:"heap_objects"`
}

type DatabaseStats struct {
	ActiveConnections    int64   `json:"active_connections"`
	IdleConnections      int64   `json:"idle_connections"`
	SlowQueries          int64   `json:"slow_queries"`
	AverageQueryTime     float64 `json:"avg_query_time_ms"`
	TotalQueries         int64   `json:"total_queries"`
	CacheHitRatio        float64 `json:"cache_hit_ratio"`
	ConnectionPoolStatus string  `json:"connection_pool_status"`
}

type APIStats struct {
	RequestsPerSecond   float64            `json:"requests_per_second"`
	AverageResponseTime float64            `json:"avg_response_time_ms"`
	ErrorRate           float64            `json:"error_rate_percent"`
	EndpointPerformance map[string]float64 `json:"endpoint_performance"`
	ActiveConnections   int                `json:"active_connections"`
}

type SystemStats struct {
	Goroutines  int     `json:"goroutines"`
	CPUCores    int     `json:"cpu_cores"`
	LoadAverage float64 `json:"load_average"`
	DiskUsageMB int64   `json:"disk_usage_mb"`
}

// PerformanceMonitor tracks and analyzes system performance
type PerformanceMonitor struct {
	db           *gorm.DB
	app          *fiber.App
	metrics      *PerformanceMetrics
	mu           sync.RWMutex
	requests     int64
	errors       int64
	responseTime float64
	startTime    time.Time
}

// NewPerformanceMonitor creates a new performance monitoring instance
func NewPerformanceMonitor(dbURL string) (*PerformanceMonitor, error) {
	// Configure GORM with detailed logging for performance analysis
	db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				SlowThreshold:             200 * time.Millisecond, // Log slow queries
				LogLevel:                  logger.Info,
				IgnoreRecordNotFoundError: false,
				Colorful:                  true,
			},
		),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	// Configure connection pool for optimal performance
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %v", err)
	}

	// Optimal connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(50)
	sqlDB.SetConnMaxLifetime(time.Hour)
	sqlDB.SetConnMaxIdleTime(10 * time.Minute)

	pm := &PerformanceMonitor{
		db:        db,
		startTime: time.Now(),
	}

	return pm, nil
}

// StartMonitoring begins collecting performance metrics
func (pm *PerformanceMonitor) StartMonitoring(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // Collect metrics every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			pm.collectMetrics()
		}
	}
}

// collectMetrics gathers current system performance data
func (pm *PerformanceMonitor) collectMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	metrics := &PerformanceMetrics{
		Timestamp: time.Now(),
		Memory: MemoryStats{
			AllocMB:      bToMB(m.Alloc),
			TotalAllocMB: bToMB(m.TotalAlloc),
			SysMB:        bToMB(m.Sys),
			NumGC:        m.NumGC,
			GCCPUPercent: m.GCCPUFraction * 100,
			HeapObjects:  m.HeapObjects,
		},
		Database:   pm.getDatabaseStats(),
		API:        pm.getAPIStats(),
		SystemLoad: pm.getSystemStats(),
	}

	// Generate recommendations based on metrics
	metrics.RecommendedActions = pm.generateRecommendations(metrics)

	pm.metrics = metrics

	// Log critical performance issues
	pm.logCriticalIssues(metrics)
}

// getDatabaseStats collects database performance metrics
func (pm *PerformanceMonitor) getDatabaseStats() DatabaseStats {
	sqlDB, err := pm.db.DB()
	if err != nil {
		return DatabaseStats{ConnectionPoolStatus: "ERROR: Cannot access DB"}
	}

	stats := sqlDB.Stats()

	// Query PostgreSQL statistics
	var activeConnections, idleConnections int64
	var cacheHitRatio float64

	// Get connection stats from pg_stat_activity
	pm.db.Raw("SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active'").Scan(&activeConnections)
	pm.db.Raw("SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'idle'").Scan(&idleConnections)

	// Get cache hit ratio
	pm.db.Raw(`
		SELECT 
			CASE 
				WHEN (blks_hit + blks_read) = 0 THEN 100
				ELSE (blks_hit::float / (blks_hit + blks_read)::float) * 100
			END as hit_ratio
		FROM pg_stat_database 
		WHERE datname = current_database()
	`).Scan(&cacheHitRatio)

	// Get slow query count (queries > 200ms from pg_stat_statements if available)
	var slowQueries int64
	pm.db.Raw("SELECT COUNT(*) FROM pg_stat_activity WHERE query_start < NOW() - INTERVAL '200 milliseconds' AND state = 'active'").Scan(&slowQueries)

	poolStatus := "HEALTHY"
	if stats.OpenConnections > 40 {
		poolStatus = "HIGH_USAGE"
	}
	if stats.OpenConnections >= 50 {
		poolStatus = "CRITICAL"
	}

	return DatabaseStats{
		ActiveConnections:    activeConnections,
		IdleConnections:      idleConnections,
		SlowQueries:          slowQueries,
		AverageQueryTime:     0, // Would need query timing implementation
		TotalQueries:         int64(stats.OpenConnections),
		CacheHitRatio:        cacheHitRatio,
		ConnectionPoolStatus: poolStatus,
	}
}

// getAPIStats collects API performance metrics
func (pm *PerformanceMonitor) getAPIStats() APIStats {
	uptime := time.Since(pm.startTime).Seconds()
	rps := float64(pm.requests) / uptime
	errorRate := 0.0
	if pm.requests > 0 {
		errorRate = (float64(pm.errors) / float64(pm.requests)) * 100
	}

	return APIStats{
		RequestsPerSecond:   rps,
		AverageResponseTime: pm.responseTime,
		ErrorRate:           errorRate,
		EndpointPerformance: map[string]float64{
			"/api/dashboard/stats": 0, // Would track individual endpoints
			"/api/cards":           0,
			"/api/contacts":        0,
		},
		ActiveConnections: runtime.NumGoroutine(),
	}
}

// getSystemStats collects system-level performance metrics
func (pm *PerformanceMonitor) getSystemStats() SystemStats {
	return SystemStats{
		Goroutines:  runtime.NumGoroutine(),
		CPUCores:    runtime.NumCPU(),
		LoadAverage: 0, // Would need system-specific implementation
		DiskUsageMB: 0, // Would need disk usage implementation
	}
}

// generateRecommendations provides actionable performance advice
func (pm *PerformanceMonitor) generateRecommendations(metrics *PerformanceMetrics) []string {
	var recommendations []string

	// Memory recommendations
	if metrics.Memory.AllocMB > 100 {
		recommendations = append(recommendations, "HIGH MEMORY USAGE: Consider implementing object pooling or reducing object allocations")
	}

	if metrics.Memory.GCCPUPercent > 5 {
		recommendations = append(recommendations, "HIGH GC PRESSURE: Optimize memory allocations and reduce garbage generation")
	}

	// Database recommendations
	if metrics.Database.ConnectionPoolStatus == "CRITICAL" {
		recommendations = append(recommendations, "CRITICAL: Database connection pool exhausted - implement connection pooling")
	}

	if metrics.Database.CacheHitRatio < 95 {
		recommendations = append(recommendations, "LOW CACHE HIT RATIO: Add database indexes or increase shared_buffers")
	}

	if metrics.Database.SlowQueries > 5 {
		recommendations = append(recommendations, "SLOW QUERIES DETECTED: Review and optimize database queries")
	}

	// API recommendations
	if metrics.API.ErrorRate > 2 {
		recommendations = append(recommendations, "HIGH ERROR RATE: Investigate and fix API errors")
	}

	if metrics.API.AverageResponseTime > 500 {
		recommendations = append(recommendations, "SLOW API RESPONSES: Implement caching and optimize query patterns")
	}

	// System recommendations
	if metrics.SystemLoad.Goroutines > 1000 {
		recommendations = append(recommendations, "HIGH GOROUTINE COUNT: Check for goroutine leaks")
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "SYSTEM PERFORMANCE: All metrics within acceptable ranges")
	}

	return recommendations
}

// logCriticalIssues logs performance issues that need immediate attention
func (pm *PerformanceMonitor) logCriticalIssues(metrics *PerformanceMetrics) {
	if metrics.Memory.AllocMB > 200 {
		log.Printf("🚨 CRITICAL: Memory usage is %0.2fMB - possible memory leak", metrics.Memory.AllocMB)
	}

	if metrics.Database.ConnectionPoolStatus == "CRITICAL" {
		log.Printf("🚨 CRITICAL: Database connection pool exhausted")
	}

	if metrics.API.ErrorRate > 5 {
		log.Printf("🚨 CRITICAL: API error rate is %0.2f%%", metrics.API.ErrorRate)
	}
}

// GetCurrentMetrics returns the latest performance metrics
func (pm *PerformanceMonitor) GetCurrentMetrics() *PerformanceMetrics {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	if pm.metrics == nil {
		pm.collectMetrics() // Collect initial metrics if none exist
	}

	return pm.metrics
}

// MiddlewareHandler returns a Fiber middleware for request tracking
func (pm *PerformanceMonitor) MiddlewareHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()

		err := c.Next()

		duration := time.Since(start).Milliseconds()

		pm.mu.Lock()
		pm.requests++
		if err != nil {
			pm.errors++
		}
		// Simple moving average for response time
		pm.responseTime = (pm.responseTime + float64(duration)) / 2
		pm.mu.Unlock()

		return err
	}
}

// HTTPHandler provides an HTTP endpoint for metrics
func (pm *PerformanceMonitor) HTTPHandler(c *fiber.Ctx) error {
	metrics := pm.GetCurrentMetrics()
	return c.JSON(metrics)
}

// LoadTestEndpoints provides load testing utilities
func (pm *PerformanceMonitor) LoadTestEndpoints() fiber.Handler {
	return func(c *fiber.Ctx) error {
		testType := c.Query("type", "light")

		switch testType {
		case "light":
			// Simulate light database load
			var count int64
			pm.db.Model(&struct {
				ID int `gorm:"primaryKey"`
			}{}).Count(&count)

		case "heavy":
			// Simulate heavy database load
			for i := 0; i < 10; i++ {
				var results []map[string]interface{}
				pm.db.Raw("SELECT COUNT(*) FROM information_schema.tables").Scan(&results)
			}

		case "memory":
			// Simulate memory allocation
			data := make([][]byte, 1000)
			for i := range data {
				data[i] = make([]byte, 1024*1024) // 1MB each
			}
			runtime.GC() // Force garbage collection
		}

		return c.JSON(fiber.Map{
			"test_type": testType,
			"status":    "completed",
			"timestamp": time.Now(),
		})
	}
}

// Helper function to convert bytes to MB
func bToMB(b uint64) float64 {
	return float64(b) / 1024 / 1024
}

// Example usage and standalone server
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run performance-monitor.go <database_url>")
		fmt.Println("Example: go run performance-monitor.go 'postgres://postgres:postgres@localhost:5432/crm?sslmode=disable'")
		os.Exit(1)
	}

	dbURL := os.Args[1]

	pm, err := NewPerformanceMonitor(dbURL)
	if err != nil {
		log.Fatalf("Failed to create performance monitor: %v", err)
	}

	// Start background monitoring
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go pm.StartMonitoring(ctx)

	// Create Fiber app for metrics API
	app := fiber.New(fiber.Config{
		DisableStartupMessage: false,
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": err.Error(),
			})
		},
	})

	// Add performance tracking middleware
	app.Use(pm.MiddlewareHandler())

	// Metrics endpoints
	app.Get("/metrics", pm.HTTPHandler)
	app.Get("/loadtest", pm.LoadTestEndpoints())

	// Health check with performance data
	app.Get("/health", func(c *fiber.Ctx) error {
		metrics := pm.GetCurrentMetrics()
		status := "healthy"

		if metrics.Memory.AllocMB > 100 ||
			metrics.Database.ConnectionPoolStatus == "CRITICAL" ||
			metrics.API.ErrorRate > 5 {
			status = "degraded"
		}

		return c.JSON(fiber.Map{
			"status":     status,
			"timestamp":  time.Now(),
			"memory_mb":  metrics.Memory.AllocMB,
			"goroutines": metrics.SystemLoad.Goroutines,
			"db_status":  metrics.Database.ConnectionPoolStatus,
			"cache_hit":  fmt.Sprintf("%.1f%%", metrics.Database.CacheHitRatio),
		})
	})

	fmt.Println("🚀 Performance Monitor starting on :9090")
	fmt.Println("📊 Metrics endpoint: http://localhost:9090/metrics")
	fmt.Println("🏥 Health endpoint: http://localhost:9090/health")
	fmt.Println("🧪 Load test endpoint: http://localhost:9090/loadtest?type=light|heavy|memory")

	log.Fatal(app.Listen(":9090"))
}
