version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: crm-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
      POSTGRES_DB: crm
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/000-init.sql
      - ./migrations/001_initial_schema.sql:/docker-entrypoint-initdb.d/001-initial-schema.sql
      - ./migrations/002_jsonb_fields.sql:/docker-entrypoint-initdb.d/002-jsonb-fields.sql
      - ./migrations/003_indexes.sql:/docker-entrypoint-initdb.d/003-indexes.sql
      - ./migrations/004_seed_data.sql:/docker-entrypoint-initdb.d/004-seed-data.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d crm"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - crm-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: crm-redis-prod
    restart: unless-stopped
    command: >
      sh -c 'if [ -n "$${REDIS_PASSWORD}" ]; then 
        redis-server --appendonly yes --requirepass "$${REDIS_PASSWORD}";
      else 
        redis-server --appendonly yes;
      fi'
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "sh", "-c", "redis-cli $${REDIS_PASSWORD:+-a $$REDIS_PASSWORD} ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - crm-network

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: crm-minio-prod
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: ${MINIO_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_PASSWORD:-minioadmin}
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - crm-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crm-backend-prod
    restart: unless-stopped
    environment:
      # Database configuration
      CRM_DATABASE_HOST: postgres
      CRM_DATABASE_PORT: 5432
      CRM_DATABASE_USER: postgres
      CRM_DATABASE_PASSWORD: ${DB_PASSWORD:-postgres}
      CRM_DATABASE_DBNAME: crm
      CRM_DATABASE_SSLMODE: disable
      
      # Redis configuration
      CRM_REDIS_HOST: redis
      CRM_REDIS_PORT: 6379
      CRM_REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      CRM_REDIS_DB: 0
      
      # Server configuration
      CRM_SERVER_HOST: 0.0.0.0
      CRM_SERVER_PORT: 8080
      
      # Auth configuration
      CRM_AUTH_JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      CRM_AUTH_TOKEN_DURATION: ${JWT_TOKEN_DURATION:-168}
      
      # Storage configuration
      CRM_STORAGE_UPLOAD_PATH: /root/uploads
      CRM_STORAGE_MAX_SIZE: ${MAX_UPLOAD_SIZE:-10485760}
      
      # MinIO configuration
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: ${MINIO_USER:-minioadmin}
      MINIO_SECRET_KEY: ${MINIO_PASSWORD:-minioadmin}
      MINIO_USE_SSL: "false"
      
      # Telegram Bot (optional)
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN:-}
    volumes:
      - ./uploads:/root/uploads
      - ./config.yaml:/root/config.yaml:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      start_period: 40s
      retries: 3
    networks:
      - crm-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: crm-frontend-prod
    restart: unless-stopped
    environment:
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:8080}
      API_URL: http://backend:8080
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - crm-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: crm-nginx-prod
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./uploads:/var/www/uploads
    depends_on:
      - backend
      - frontend
    networks:
      - crm-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  crm-network:
    driver: bridge