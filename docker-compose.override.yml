# Docker Compose Override for Local Development
# This file automatically overrides settings in docker-compose.yml
# for local development environment

version: '3.8'

services:
  postgres:
    ports:
      - "5432:5432"
    environment:
      POSTGRES_PASSWORD: postgres  # Simple password for dev
    volumes:
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql:ro

  redis:
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes  # No password in dev

  minio:
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin

  # Optional: Add development tools
  adminer:
    image: adminer
    restart: always
    ports:
      - "8081:8080"
    networks:
      - crm-network
    environment:
      ADMINER_DEFAULT_SERVER: postgres