{"permissions": {"allow": ["mcp__code-reasoning__code-reasoning", "Bash(npx create-next-app:*)", "Bash(npm install)", "Bash(npm install:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(docker-compose down:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(PGPASSWORD=postgres docker exec crm-postgres psql -U postgres -d crm -c \"SELECT COUNT(*) FROM pipelines;\")", "Bash(psql:*)", "<PERSON><PERSON>(docker cp:*)", "<PERSON><PERSON>(go mod tidy:*)", "<PERSON><PERSON>(go run:*)", "Bash(go build:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(pgrep:*)", "<PERSON><PERSON>(curl:*)", "Bash(ps:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_fill_form", "mcp__playwright__browser_click", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_press_key", "Bash(./server)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(npx playwright install:*)", "mcp__mcp-reasoner__mcp-reasoner", "Read(/home/<USER>/**)", "<PERSON><PERSON>(cat:*)", "Bash(npm run dev:*)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_close", "mcp__playwright__browser_install", "<PERSON><PERSON>(timeout:*)", "Bash(PGPASSWORD=postgres docker exec crm-postgres psql -U postgres -d crm -c \"\\d cards\")", "Bash(find:*)", "Bash(PGPASSWORD=postgres docker exec crm-postgres psql -U postgres -d crm -c \"\\dt\")", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_type", "mcp__playwright__browser_resize", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_drag", "Bash(lsof:*)", "Bash(grep:*)", "Bash(kill:*)", "Bash(PGPASSWORD=postgres psql -U postgres -d crm -h localhost -c \"SELECT email, first_name, last_name FROM users LIMIT 5;\")", "mcp__sequential-thinking__sequentialthinking", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c \"\\d users\")", "Bash(make status:*)", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c \"SELECT name FROM pipelines;\")", "<PERSON><PERSON>(make:*)", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c \"\\d contacts\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c \"\\d companies\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c \"\\dt\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c \"SELECT title, value, priority FROM cards ORDER BY value DESC;\")", "Bash(ls:*)", "mcp__playwright__browser_handle_dialog", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c \"SELECT p.resource, p.action, p.description, COUNT(rp.role_id) as role_count FROM permissions p LEFT JOIN role_permissions rp ON p.id = rp.permission_id WHERE p.deleted_at IS NULL GROUP BY p.resource, p.action, p.description ORDER BY p.resource, p.action;\")", "Bash(npx next build:*)", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d crm -c \"\\d stages\")", "<PERSON><PERSON>(time curl:*)", "Bash(tree:*)", "Bash(docker logs:*)", "Bash(go install:*)"], "deny": [], "ask": []}}