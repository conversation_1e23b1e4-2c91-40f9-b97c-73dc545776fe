# Сценарий презентации CRM системы

## 🎬 Подготовка перед презентацией

1. Откройте браузер на http://localhost:3001
2. Войдите как <EMAIL> / password123
3. Откройте вторую вкладку/браузер для демонстрации real-time
4. Убедитесь, что есть тестовые данные в pipeline

---

## СЛАЙД 1: Вступление (2 минуты)

### 💬 ГОВОРИМ:
"Добрый день! Представьте ситуацию: у вас 50 активных сделок, 200 контактов, и каждый день приходят новые лиды. Как не потерять ни одного клиента? Как понять, на какой стадии каждая сделка? Как координировать работу отдела продаж?

Сегодня многие компании решают эти задачи в Excel или Google Sheets. Но это приводит к:
- Потере данных при одновременном редактировании
- Отсутствию визуализации процесса продаж
- Сложности в отслеживании истории изменений
- Невозможности работать с мобильного

Мы создали современную CRM систему, которая решает все эти проблемы."

### 👆 КЛИКАЕМ:
- Ничего не кликаем, показываем слайд с проблемами

---

## СЛАЙД 2: Первое впечатление (1 минута)

### 💬 ГОВОРИМ:
"Давайте сразу посмотрим на систему в действии. Вот наш главный экран - pipeline продаж."

### 👆 КЛИКАЕМ:
1. Переходим в браузер
2. Кликаем на **Pipeline** в левом меню
3. Показываем общий вид pipeline

---

## СЛАЙД 3: Демонстрация Pipeline (5 минут)

### 💬 ГОВОРИМ:
"Это визуальное представление всех ваших сделок. Каждая колонка - это этап продажи: новые лиды, квалификация, предложение, переговоры, закрытие."

### 👆 КЛИКАЕМ - Создание новой сделки:
1. Нажимаем кнопку **"+ Add Card"** в колонке "New"
2. В диалоге заполняем:
   - Title: "Крупная сделка с Альфа-Банк"
   - Value: "5000000"
   - Priority: выбираем "High"
   - Description: "Внедрение CRM для отдела продаж"
3. Нажимаем **"Create"**

### 💬 ГОВОРИМ:
"Смотрите, как просто создать новую сделку. Заполнили основные поля - и она уже в pipeline. Обратите внимание на цветовую индикацию приоритета."

### 👆 КЛИКАЕМ - Drag & Drop:
1. Берем созданную карточку мышкой
2. Перетаскиваем в колонку **"Qualified"**
3. Отпускаем

### 💬 ГОВОРИМ:
"А теперь главная фишка - drag and drop. Просто перетащили карточку на следующий этап. Никаких форм, никаких лишних кликов. Это интуитивно понятно даже новому сотруднику."

### 👆 КЛИКАЕМ - Редактирование карточки:
1. Кликаем на карточку "Enterprise Deal - TechCorp"
2. В открывшемся диалоге показываем:
   - Основные поля
   - Custom fields (прокручиваем вниз)
   - Activity лог
3. Меняем Value на "750000"
4. Нажимаем **"Save"**

### 💬 ГОВОРИМ:
"Каждая карточка содержит полную информацию о сделке. Особенно важно - это custom fields. Вы можете добавлять любые поля без программирования. Нужно поле 'Источник лида'? Добавили. 'Ответственный менеджер'? Пожалуйста."

### 👆 КЛИКАЕМ - Real-time синхронизация:
1. Переходим во **второй браузер/вкладку**
2. Возвращаемся в первый браузер
3. Перетаскиваем любую карточку в другую колонку
4. Показываем, что во втором браузере изменение произошло мгновенно

### 💬 ГОВОРИМ:
"А теперь магия real-time синхронизации. Смотрите - я перемещаю карточку здесь, и она мгновенно перемещается у всех пользователей. Никаких F5, никаких обновлений страницы. Вся команда видит актуальную картину."

---

## СЛАЙД 4: Управление контактами (3 минуты)

### 💬 ГОВОРИМ:
"Pipeline - это здорово, но CRM - это прежде всего о людях. Давайте посмотрим раздел контактов."

### 👆 КЛИКАЕМ:
1. Кликаем **"Contacts"** в левом меню
2. Страница загружается с списком контактов

### 💬 ГОВОРИМ:
"Здесь все наши контакты. Каждый с фотографией, должностью, компанией."

### 👆 КЛИКАЕМ - Поиск:
1. В поле поиска вводим **"alice"**
2. Показываем, как список фильтруется в реальном времени

### 💬 ГОВОРИМ:
"Обратите внимание - поиск мгновенный, без морганий и задержек. Ищет по всем полям: имя, email, телефон, компания."

### 👆 КЛИКАЕМ - Создание контакта:
1. Очищаем поиск (крестик в поле)
2. Нажимаем **"+ Add Contact"**
3. Заполняем:
   - First Name: "Иван"
   - Last Name: "Петров"
   - Email: "<EMAIL>"
   - Phone: "****** 123-45-67"
   - Job Title: "Директор по развитию"
   - Company: выбираем из списка
4. Нажимаем **"Create"**

### 💬 ГОВОРИМ:
"Новый контакт сразу привязывается к компании. Вся история взаимодействий сохраняется."

---

## СЛАЙД 5: Управление компаниями (2 минуты)

### 💬 ГОВОРИМ:
"И конечно, компании - наши клиенты и партнеры."

### 👆 КЛИКАЕМ:
1. Кликаем **"Companies"** в левом меню
2. В поле поиска вводим **"tech"**

### 💬 ГОВОРИМ:
"Умный поиск работает и здесь. Ищем 'tech' - находим Tech Corp. Можно искать по названию, сайту, email, даже по индустрии."

### 👆 КЛИКАЕМ - Фильтры:
1. Очищаем поиск
2. Кликаем на **"Category"**
3. Выбираем "Technology"
4. Показываем отфильтрованный список

### 💬 ГОВОРИМ:
"Фильтры помогают сегментировать базу. Нужны только IT компании? Пожалуйста. Только с 100+ сотрудниками? Легко."

---

## СЛАЙД 6: Несколько Pipeline (2 минуты)

### 💬 ГОВОРИМ:
"Часто разные отделы имеют разные процессы продаж. Мы это предусмотрели."

### 👆 КЛИКАЕМ:
1. Кликаем **"Pipelines"** в левом меню
2. Показываем список pipeline
3. Кликаем на **"Enterprise Sales"**

### 💬 ГОВОРИМ:
"У каждого отдела может быть свой pipeline со своими этапами. Enterprise продажи - одни этапы, работа с SMB - другие. Все настраивается."

---

## СЛАЙД 7: Технические преимущества (2 минуты)

### 💬 ГОВОРИМ:
"Теперь о технической стороне. Система построена на современном стеке:
- Backend на Go - это скорость и надежность
- Frontend на Next.js - это отзывчивый интерфейс
- PostgreSQL с JSONB - гибкость без потери производительности

Что это дает вам:
- Страницы грузятся менее чем за секунду
- Поддержка тысяч одновременных пользователей
- Работа даже на слабом интернете
- Безопасность корпоративного уровня"

### 👆 КЛИКАЕМ:
1. Быстро переключаемся между разделами, показывая скорость
2. Открываем Network tab в DevTools (F12)
3. Показываем время загрузки запросов (все < 100ms)

---

## СЛАЙД 8: Права доступа (1 минута)

### 💬 ГОВОРИМ:
"Безопасность - это критично. У нас 4 уровня доступа: Administrator, Manager, Sales, Viewer. Каждая роль имеет свои права."

### 👆 КЛИКАЕМ:
1. Показываем, что у админа есть кнопка создания pipeline
2. Упоминаем, что у обычного пользователя её не будет

---

## СЛАЙД 9: Мобильная адаптация (1 минута)

### 💬 ГОВОРИМ:
"Современный бизнес - это мобильность. Система полностью адаптивна."

### 👆 КЛИКАЕМ:
1. Открываем DevTools (F12)
2. Нажимаем Toggle device toolbar (Ctrl+Shift+M)
3. Выбираем iPhone 12
4. Показываем, как выглядит pipeline на мобильном

---

## СЛАЙД 10: Конкурентные преимущества (2 минуты)

### 💬 ГОВОРИМ:
"Почему наша CRM, а не Bitrix24 или amoCRM?

1. **Скорость** - мы в 10 раз быстрее. Никаких зависаний.
2. **Простота** - обучение сотрудника за 15 минут.
3. **Гибкость** - custom fields без программистов.
4. **Цена** - open source, платите только за поддержку.
5. **Контроль** - ваши данные на ваших серверах.

При этом у нас есть всё необходимое для старта."

---

## СЛАЙД 11: Roadmap (1 минута)

### 💬 ГОВОРИМ:
"Это не конечная версия. В ближайших планах:
- Email интеграция - письма прямо в карточке сделки
- Календарь и напоминания
- Мобильное приложение
- Интеграция с 1С
- AI-ассистент для прогнозирования продаж"

---

## СЛАЙД 12: Live Demo - Полный цикл (3 минуты)

### 💬 ГОВОРИМ:
"Давайте пройдем полный цикл работы с клиентом."

### 👆 КЛИКАЕМ - Полный сценарий:

1. **Создаем компанию:**
   - Companies → Add Company
   - Name: "Рога и Копыта"
   - Website: "rogaikopyta.ru"
   - Industry: "Manufacturing"
   - Create

2. **Создаем контакт:**
   - Contacts → Add Contact
   - Остап Бендер
   - <EMAIL>
   - Выбираем созданную компанию
   - Create

3. **Создаем сделку:**
   - Pipeline → Add Card
   - Title: "Поставка рогов"
   - Value: 1000000
   - Create

4. **Двигаем по pipeline:**
   - Перетаскиваем из New в Qualified
   - Из Qualified в Proposal
   - Показываем, что каждое действие логируется

### 💬 ГОВОРИМ:
"Вот так просто - создали компанию, контакт, сделку, и ведем её по воронке. Всё связано, всё отслеживается."

---

## СЛАЙД 13: Вопросы и ответы (5 минут)

### 💬 ГОВОРИМ:
"Спасибо за внимание! Готов ответить на ваши вопросы."

### Частые вопросы и ответы:

**Q: Сколько стоит?**
A: "Сама система - open source, бесплатно. Мы зарабатываем на внедрении, кастомизации и поддержке. Типичный проект - от 300 тыс. рублей."

**Q: Можно ли интегрировать с 1С?**
A: "Да, у нас есть REST API. Интеграция занимает 2-3 недели."

**Q: А если нужны специфические поля?**
👆 КЛИКАЕМ: Показываем custom fields в любой карточке
A: "Любые поля добавляются через интерфейс, без программирования."

**Q: Как с безопасностью?**
A: "JWT токены, шифрование, ролевая модель, аудит всех действий. Можно развернуть в вашем контуре."

**Q: Сколько пользователей поддерживает?**
A: "Тестировали до 1000 одновременных. Архитектура позволяет масштабировать дальше."

**Q: Есть ли мобильное приложение?**
A: "Пока веб-версия, полностью адаптивная. Native приложения в roadmap на Q2 2025."

---

## СЛАЙД 14: Call to Action (1 минута)

### 💬 ГОВОРИМ:
"Что дальше?

1. **Пилотный проект** - развернем у вас на 1 месяц бесплатно
2. **Демо на ваших данных** - импортируем ваш Excel и покажем, как это будет выглядеть
3. **Кастомизация** - допилим под ваши процессы

Оставьте контакты, и мы начнем уже на следующей неделе.

Спасибо за внимание!"

---

## 📝 Чек-лист перед презентацией

- [ ] Проверить, что сервер запущен
- [ ] Войти в систему под админом
- [ ] Создать тестовые данные, если нужно
- [ ] Открыть второй браузер для real-time демо
- [ ] Подготовить DevTools для показа скорости
- [ ] Проверить, что drag & drop работает
- [ ] Отключить уведомления на компьютере
- [ ] Увеличить размер шрифта в браузере (Ctrl++)

## 🚨 Если что-то пошло не так

**Не работает drag & drop:**
- Обновите страницу
- Скажите: "Это особенность демо-версии, в production всё стабильно"

**Долго грузится:**
- Скажите: "Сейчас работаем на минимальном сервере, в production будет мгновенно"
- Покажите другой функционал

**Ошибка при создании:**
- Проверьте, заполнены ли обязательные поля
- Если не помогло, скажите: "Покажу на существующих данных"

**Потеряли мысль:**
- Вернитесь к pipeline - это главное
- Спросите: "Какой функционал вас интересует больше всего?"

---

## 🎯 Ключевые фразы для запоминания

- "Drag and drop - интуитивно понятно"
- "Real-time синхронизация - все видят изменения мгновенно"
- "Custom fields - гибкость без программирования"
- "Менее секунды - скорость загрузки"
- "Open source - вы владеете системой"
- "15 минут - время обучения сотрудника"
- "Ваши данные - на ваших серверах"