# Database Configuration
DB_PASSWORD=postgres
DB_NAME=crm
DB_USER=postgres

# Redis Configuration
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_TOKEN_DURATION=168

# MinIO Object Storage
MINIO_USER=minioadmin
MINIO_PASSWORD=minioadmin

# File Upload
MAX_UPLOAD_SIZE=10485760

# Telegram Bot (Optional)
TELEGRAM_BOT_TOKEN=

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8080

# Nginx Ports (Production)
HTTP_PORT=80
HTTPS_PORT=443

# PgAdmin (Optional)
PGADMIN_PASSWORD=admin