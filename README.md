# 🚀 CRM System (AmoCRM Clone)

Полноценная CRM система с гибкими воронками продаж, динамическими полями и интеграциями с внешними сервисами.

## 📋 Основные возможности

### ✅ Реализовано в MVP

- **Воронки продаж** с кастомными этапами (OUTSTAFF SALES, OUTSS)
- **Drag & Drop** перемещение карточек между этапами
- **Динамические поля** через JSONB (без изменения схемы БД)
- **Real-time обновления** через Server-Sent Events
- **JWT авторизация** с refresh токенами
- **Файловое хранилище** на MinIO (S3-совместимое)
- **История изменений** для всех карточек
- **Responsive UI** на Next.js 14 с shadcn/ui

### 🔧 Технологический стек

**Backend:**
- Go 1.21 с Clean Architecture
- Fiber (веб-фреймворк)
- GORM (ORM)
- PostgreSQL 15 с JSONB
- Redis (кэширование)
- MinIO (файлы)

**Frontend:**
- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- shadcn/ui компоненты
- Zustand (state management)
- React Query
- @dnd-kit (drag & drop)

## 🚀 Быстрый старт (1 минута)

```bash
# Клонировать репозиторий
git clone <repository-url>
cd CRM_NEW

# Запустить систему
make init

# Или используя скрипт напрямую
./init.sh
```

После запуска система доступна по адресам:
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8080
- **MinIO Console:** http://localhost:9001
- **PgAdmin:** http://localhost:5050 (опционально)

## 📦 Структура проекта

```
CRM_NEW/
├── backend/                 # Go backend с Clean Architecture
│   ├── cmd/                # Точки входа приложения
│   ├── internal/           # Бизнес-логика
│   │   ├── domain/         # Сущности и интерфейсы
│   │   ├── usecases/       # Use cases
│   │   ├── adapters/       # HTTP, DB, интеграции
│   │   └── infrastructure/ # Конфигурация, логи
│   └── migrations/         # SQL миграции
│
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── app/           # Pages (App Router)
│   │   ├── components/    # React компоненты
│   │   ├── lib/          # Утилиты и API клиент
│   │   ├── stores/       # Zustand stores
│   │   └── types/        # TypeScript типы
│   └── public/           # Статические файлы
│
├── docker-compose.yml     # Docker конфигурация
├── Makefile              # Команды управления
└── init.sh              # Скрипт инициализации
```

## 🔧 Управление системой

### Основные команды

```bash
# Запуск и остановка
make dev          # Запустить в режиме разработки
make prod         # Запустить в production
make stop         # Остановить все сервисы
make restart      # Перезапустить все сервисы

# Логи и отладка
make logs         # Показать логи всех сервисов
make backend-logs # Логи backend
make frontend-logs # Логи frontend

# База данных
make db-shell     # Подключиться к PostgreSQL
make db-backup    # Создать backup БД
make migrate      # Запустить миграции

# Очистка
make clean        # Удалить все контейнеры и данные
```

### Docker команды

```bash
# Просмотр статуса
docker-compose ps

# Логи конкретного сервиса
docker-compose logs -f backend

# Выполнить команду в контейнере
docker-compose exec backend sh
docker-compose exec postgres psql -U postgres -d crm

# Мониторинг ресурсов
docker stats
```

## 🏗️ Архитектура системы

### Backend (Clean Architecture)

```
internal/
├── domain/           # Бизнес-логика
│   ├── entities/    # Сущности (Pipeline, Card, Contact)
│   ├── repositories/ # Интерфейсы репозиториев
│   └── services/    # Доменные сервисы
│
├── usecases/        # Сценарии использования
│   ├── pipeline/    # Управление воронками
│   ├── card/        # Работа с карточками
│   └── field/       # Динамические поля
│
├── adapters/        # Внешние адаптеры
│   ├── http/        # HTTP handlers (Fiber)
│   ├── database/    # GORM репозитории
│   ├── events/      # SSE manager
│   └── integrations/ # Telegram, ЭДО, 1С
│
└── infrastructure/  # Инфраструктура
    ├── config/      # Конфигурация
    └── logger/      # Логирование
```

### Динамические поля (JSONB)

```sql
-- Пример структуры custom_fields в карточке
{
  "company_name": "ООО Рога и Копыта",
  "budget": 150000,
  "timeline": "2024-03-01",
  "tech_stack": ["Go", "React", "PostgreSQL"],
  "requirements": {
    "developers": 5,
    "experience": "3+ years",
    "english": "B2"
  }
}
```

### API Endpoints

```bash
# Авторизация
POST   /api/v1/auth/register
POST   /api/v1/auth/login
POST   /api/v1/auth/refresh

# Воронки
GET    /api/v1/pipelines
POST   /api/v1/pipelines
PUT    /api/v1/pipelines/:id
DELETE /api/v1/pipelines/:id

# Карточки
GET    /api/v1/cards
POST   /api/v1/cards
PUT    /api/v1/cards/:id
DELETE /api/v1/cards/:id
POST   /api/v1/cards/:id/move  # Перемещение между этапами

# Динамические поля
GET    /api/v1/fields/definitions
POST   /api/v1/fields/definitions
PUT    /api/v1/fields/definitions/:id

# Real-time события
GET    /api/v1/events  # SSE endpoint
```

## 🎯 Воронки продаж

### OUTSTAFF SALES (Поиск партнеров)

1. **Пауза** - Нет активности более 3 недель
2. **Первый контакт** - Компания найдена, начат контакт
3. **Выявление потребностей** - Анализ требований
4. **Классификация потребностей** - Сопоставление с компетенциями
5. **Проект договора** - Передан шаблон договора
6. **Договор подписан** - Подписан, заявок пока нет
7. **Передано в аккаунт** - Получены заявки
8. **Закрыто не реализовано** - Сделка завершена без результата

### OUTSS (Подбор кандидатов)

1. **Пауза** - Временная остановка
2. **Заявка** - Описание вакансии
3. **Поиск кандидата** - Анализ рынка
4. **Квалификация кандидата** - Проверка навыков
5. **Передано заказчику** - Отправка резюме
6. **Собеседование** - Интервью с клиентом
7. **Оформление доступов** - Подготовка к работе
8. **Сделка** - Кандидат приступил к работе
9. **Успешно реализовано** - Завершено успешно
10. **Сделка закрыта** - Завершено

## 🔌 Интеграции

### Telegram Bot

```bash
# Установить токен бота в .env
TELEGRAM_BOT_TOKEN=your-bot-token

# Webhook автоматически настраивается при запуске
# Бот принимает резюме и рассылает вакансии партнерам
```

### Планируемые интеграции

- ✅ Telegram Bot (реализовано)
- ⏳ ЭДО (Диадок) - в разработке
- ⏳ 1С - в разработке
- ⏳ Talentix (HH.ru) - в разработке
- ⏳ Email - в разработке

## 🚀 Production деплой

### С использованием Docker

```bash
# Настроить production переменные
cp .env .env.production
vim .env.production  # Изменить пароли и ключи

# Запустить production версию
make deploy

# Или через docker-compose
docker-compose -f docker-compose.production.yml up -d
```

### Настройка Nginx (опционально)

```nginx
server {
    listen 80;
    server_name crm.yourdomain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /api {
        proxy_pass http://localhost:8080;
        # ... остальные настройки
    }
}
```

## 📊 Мониторинг и обслуживание

### Backup базы данных

```bash
# Создать backup
make db-backup

# Восстановить из backup
make db-restore FILE=backups/backup_20240101_120000.sql
```

### Мониторинг ресурсов

```bash
# Просмотр использования ресурсов
make monitor

# Проверка здоровья сервисов
make health
```

### Логи и отладка

```bash
# Все логи
docker-compose logs -f

# Логи конкретного сервиса
docker-compose logs -f backend --tail=100

# Подключение к контейнеру
docker-compose exec backend sh
```

## 🧪 Тестирование

```bash
# Запустить все тесты
make test

# Backend тесты
make backend-test

# Frontend тесты
make frontend-test
```

## 📝 Переменные окружения

```env
# Database
DB_PASSWORD=secure_password

# JWT
JWT_SECRET=your-secret-key-here

# MinIO
MINIO_USER=minioadmin
MINIO_PASSWORD=secure_minio_password

# Telegram Bot (optional)
TELEGRAM_BOT_TOKEN=your-bot-token

# API URLs
API_URL=http://localhost:8080
WS_URL=ws://localhost:8080
```

## 🤝 Поддержка

При возникновении проблем:

1. Проверьте логи: `make logs`
2. Убедитесь что Docker запущен
3. Проверьте свободные порты: 3000, 8080, 5432, 6379, 9000
4. Попробуйте перезапустить: `make restart`
5. Полная переустановка: `make clean && make init`

## 📄 Лицензия

MIT License

---

**Разработано за 24 часа** с использованием современных технологий и best practices.