# 🧪 Test Accounts

## Quick Login Feature
The login page now includes **Quick Login buttons** for easy access during development and testing. Just click any test account button to auto-fill credentials!

## Users

| Email | Password | Name | Role | Description |
|-------|----------|------|------|-------------|
| <EMAIL> | admin123 | Admin User | Admin | Full system access |
| <EMAIL> | manager123 | John Manager | User | Manager role for testing |
| <EMAIL> | sales123 | Sarah Sales | User | Sales representative |
| <EMAIL> | support123 | Mike Support | User | Support team member |
| <EMAIL> | test123 | Test User | User | General testing account |

## Test Data Created Automatically

### Companies (3)
- **Tech Corp** - Technology company from San Francisco
- **Sales Pro Inc** - Sales & Marketing company from New York
- **Global Services Ltd** - Consulting company from London

### Contacts (4)
- <PERSON> (CTO at Tech Corp)
- <PERSON> (Sales Director at Sales Pro Inc)
- <PERSON> (Project Manager at Global Services Ltd)
- <PERSON> (Lead Developer at Tech Corp)

### Pipeline Cards (5)
1. **Enterprise Software Deal** - $250,000 (High Priority) - Proposal Stage
2. **CRM Implementation Project** - $75,000 (Medium Priority) - Qualified Stage
3. **Consulting Services Agreement** - $120,000 (Medium Priority) - Negotiation Stage
4. **Website Development** - $35,000 (Low Priority) - Lead Stage
5. **Mobile App Development** - $95,000 (High Priority) - Qualified Stage

## Quick Login Test

```bash
# Test admin login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Test regular user login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"sales123"}'
```

## Frontend Access

1. Open http://localhost:3000
2. Use any of the test accounts above
3. You'll see pre-populated data in:
   - Pipeline view with 5 cards in different stages
   - Companies list with 3 companies
   - Contacts list with 4 contacts

## Reset Test Data

To reset all data and start fresh:

```bash
# Stop all services
docker-compose down -v

# Start fresh
docker-compose up -d

# Restart backend (will create all test data)
cd /home/<USER>/start_up/CRM_NEW
go run cmd/server/main.go
```

All test data is created automatically when the database is empty!