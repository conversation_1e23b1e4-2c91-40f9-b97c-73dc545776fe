# CRM System Makefile

.PHONY: help init dev prod start stop restart clean logs test build

# Default target
help:
	@echo "CRM System Management Commands"
	@echo "=============================="
	@echo ""
	@echo "Development Commands:"
	@echo "  make init       - Initialize the system (first time setup)"
	@echo "  make dev        - Start infrastructure + run frontend & backend locally"
	@echo "  make dev-infra  - Start only infrastructure (PostgreSQL, Redis, MinIO)"
	@echo "  make dev-backend - Run backend locally"
	@echo "  make dev-frontend - Run frontend locally"
	@echo ""
	@echo "Production Commands:"
	@echo "  make prod       - Start production environment (all in Docker)"
	@echo "  make prod-build - Build production Docker images"
	@echo "  make prod-up    - Start production containers"
	@echo "  make prod-down  - Stop production containers"
	@echo ""
	@echo "Common Commands:"
	@echo "  make stop       - Stop all containers"
	@echo "  make restart    - Restart all containers"
	@echo "  make clean      - Remove all containers and volumes"
	@echo "  make logs       - Show logs from all services"
	@echo "  make status     - Show status of all services"
	@echo ""
	@echo "Database Commands:"
	@echo "  make db-shell   - Connect to PostgreSQL"
	@echo "  make db-backup  - Create database backup"
	@echo "  make db-restore - Restore database from backup"
	@echo "  make migrate    - Run database migrations"
	@echo ""
	@echo "Testing Commands:"
	@echo "  make test       - Run all tests"
	@echo "  make test-backend - Run backend tests"
	@echo "  make test-frontend - Run frontend tests"
	@echo "  make test-e2e   - Run E2E tests"

# Initialize the system
init:
	@echo "🚀 Initializing CRM System..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "✅ Created .env file from template"; \
	else \
		echo "ℹ️  .env file already exists"; \
	fi
	@echo "📦 Starting infrastructure services..."
	@docker-compose up -d
	@echo "⏳ Waiting for services to be ready..."
	@sleep 5
	@echo "📦 Installing frontend dependencies..."
	@cd frontend && npm install
	@echo "📦 Installing backend dependencies..."
	@go mod download
	@echo "✅ System initialized successfully!"
	@echo ""
	@echo "To start development:"
	@echo "  make dev"

# Development environment
dev: dev-infra
	@echo "🚀 Starting development environment..."
	@echo "Starting backend and frontend in parallel..."
	@make -j 2 dev-backend-bg dev-frontend-bg

dev-infra:
	@echo "🐳 Starting infrastructure services..."
	@docker-compose up -d
	@echo "✅ Infrastructure services started:"
	@echo "  - PostgreSQL: localhost:5432"
	@echo "  - Redis: localhost:6379"
	@echo "  - MinIO: localhost:9000 (Console: localhost:9001)"

dev-backend:
	@echo "🚀 Starting backend server..."
	@go run main.go

dev-backend-bg:
	@echo "🚀 Starting backend server in background..."
	@go run main.go > backend.log 2>&1 &
	@echo "✅ Backend started at http://localhost:8080"
	@echo "   Logs: tail -f backend.log"

dev-frontend:
	@echo "🚀 Starting frontend server..."
	@cd frontend && npm run dev

dev-frontend-bg:
	@echo "🚀 Starting frontend server in background..."
	@cd frontend && npm run dev > ../frontend.log 2>&1 &
	@echo "✅ Frontend started at http://localhost:3000"
	@echo "   Logs: tail -f frontend.log"

# Production environment
prod:
	@echo "🚀 Starting production environment..."
	@docker-compose -f docker-compose.production.yml up -d
	@echo "✅ Production environment started!"
	@echo "  - Frontend: http://localhost:80"
	@echo "  - Backend API: http://localhost:80/api"
	@echo "  - MinIO Console: http://localhost:9001"

prod-build:
	@echo "🔨 Building production images..."
	@docker-compose -f docker-compose.production.yml build

prod-up:
	@echo "🚀 Starting production containers..."
	@docker-compose -f docker-compose.production.yml up -d

prod-down:
	@echo "🛑 Stopping production containers..."
	@docker-compose -f docker-compose.production.yml down

# Stop all services
stop:
	@echo "🛑 Stopping all services..."
	@docker-compose down
	@-pkill -f "go run main.go" 2>/dev/null || true
	@-pkill -f "next dev" 2>/dev/null || true
	@echo "✅ All services stopped"

stop-dev:
	@echo "🛑 Stopping development services..."
	@-pkill -f "go run main.go" 2>/dev/null || true
	@-pkill -f "next dev" 2>/dev/null || true
	@echo "✅ Development services stopped"

# Restart all services
restart: stop dev

# Clean everything
clean:
	@echo "🧹 Cleaning up..."
	@docker-compose down -v
	@docker-compose -f docker-compose.production.yml down -v
	@rm -rf uploads/*
	@rm -f backend.log frontend.log
	@echo "✅ Cleanup complete!"

# Show logs
logs:
	@docker-compose logs -f

logs-prod:
	@docker-compose -f docker-compose.production.yml logs -f

# Backend specific
backend-logs:
	@if [ -f backend.log ]; then \
		tail -f backend.log; \
	else \
		docker-compose logs -f backend 2>/dev/null || echo "Backend not running in Docker"; \
	fi

backend-shell:
	@docker-compose exec backend sh 2>/dev/null || echo "Backend not running in Docker"

backend-test:
	@go test ./...

# Frontend specific
frontend-logs:
	@if [ -f frontend.log ]; then \
		tail -f frontend.log; \
	else \
		docker-compose logs -f frontend 2>/dev/null || echo "Frontend not running in Docker"; \
	fi

frontend-shell:
	@docker-compose exec frontend sh 2>/dev/null || echo "Frontend not running in Docker"

frontend-build:
	@cd frontend && npm run build

frontend-test:
	@cd frontend && npm test

frontend-lint:
	@cd frontend && npm run lint

frontend-type-check:
	@cd frontend && npm run type-check

# Database specific
db-shell:
	@docker-compose exec postgres psql -U postgres -d crm

db-backup:
	@mkdir -p backups
	@docker-compose exec postgres pg_dump -U postgres crm > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Database backup created in backups/"

db-restore:
	@if [ -z "$(FILE)" ]; then \
		echo "Usage: make db-restore FILE=backups/backup_file.sql"; \
	else \
		docker-compose exec -T postgres psql -U postgres -d crm < $(FILE); \
		echo "✅ Database restored from $(FILE)"; \
	fi

migrate:
	@echo "Running migrations..."
	@docker-compose exec postgres psql -U postgres -d crm < migrations/001_initial_schema.sql
	@docker-compose exec postgres psql -U postgres -d crm < migrations/002_jsonb_fields.sql
	@docker-compose exec postgres psql -U postgres -d crm < migrations/003_indexes.sql
	@docker-compose exec postgres psql -U postgres -d crm < migrations/004_seed_data.sql
	@echo "✅ Migrations complete"

# Redis specific
redis-cli:
	@docker-compose exec redis redis-cli

redis-flush:
	@docker-compose exec redis redis-cli FLUSHALL

# MinIO specific
minio-shell:
	@docker-compose exec minio sh

# Testing
test: backend-test frontend-test

test-backend:
	@echo "🧪 Running backend tests..."
	@go test ./... -v

test-frontend:
	@echo "🧪 Running frontend tests..."
	@cd frontend && npm test

test-e2e:
	@echo "🧪 Running E2E tests..."
	@npx playwright test test-crm-ui.spec.js

# Status check
status:
	@echo "📊 Service Status:"
	@echo "===================="
	@docker-compose ps
	@echo ""
	@echo "📡 Local Services:"
	@-pgrep -f "go run main.go" > /dev/null && echo "✅ Backend: Running" || echo "❌ Backend: Not running"
	@-pgrep -f "next dev" > /dev/null && echo "✅ Frontend: Running" || echo "❌ Frontend: Not running"

# Health check
health:
	@echo "🏥 Health Check:"
	@echo "================"
	@-curl -s http://localhost:8080/health > /dev/null && echo "✅ Backend API: Healthy" || echo "❌ Backend API: Not responding"
	@-curl -s http://localhost:3000 > /dev/null && echo "✅ Frontend: Healthy" || echo "❌ Frontend: Not responding"
	@-docker-compose exec -T postgres pg_isready > /dev/null 2>&1 && echo "✅ PostgreSQL: Healthy" || echo "❌ PostgreSQL: Not responding"
	@-docker-compose exec -T redis redis-cli ping > /dev/null 2>&1 && echo "✅ Redis: Healthy" || echo "❌ Redis: Not responding"

# Development helpers
dev-seed:
	@echo "🌱 Seeding development data..."
	@docker-compose exec postgres psql -U postgres -d crm < migrations/004_seed_data.sql
	@echo "✅ Development data seeded"

dev-reset: clean init dev-seed
	@echo "✅ Development environment reset complete!"

# Install dependencies
install:
	@echo "📦 Installing all dependencies..."
	@cd frontend && npm install
	@go mod download
	@echo "✅ Dependencies installed"

# Update dependencies
update:
	@echo "📦 Updating dependencies..."
	@cd frontend && npm update
	@go get -u ./...
	@go mod tidy
	@echo "✅ Dependencies updated"