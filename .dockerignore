# Ignore git files
.git
.gitignore

# Ignore development files
*.md
.env*
.vscode
.idea

# Ignore build artifacts
*.exe
*.dll
*.so
*.dylib

# Ignore test files
*_test.go
coverage.out

# Ignore temporary files
*.tmp
*.log
tmp/

# Ignore uploads directory (will be created in container)
uploads/

# Ignore Docker files
Dockerfile*
docker-compose*
.dockerignore

# Ignore CI files
.github/
.gitlab-ci.yml
.travis.yml

# Ignore documentation
docs/